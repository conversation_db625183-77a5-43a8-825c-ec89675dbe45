import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    unoptimized: true
  },
  // Remove standalone output for Vercel - let Vercel handle the deployment
  eslint: {
    ignoreDuringBuilds: true,
  },
  //  compiler: {
  //   removeConsole:
  //     process.env.NEXT_PUBLIC_NODE_ENV === "production" ? { exclude: ["error"] } : false,
  // },
};

export default nextConfig;
