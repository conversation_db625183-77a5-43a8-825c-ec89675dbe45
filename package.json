{"name": "tickflo-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build && next export", "build:hostinger": "next build", "checkTypeScriptError": "npx tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-tooltip": "^1.2.8", "@reduxjs/toolkit": "^2.8.2", "@types/react-datepicker": "^6.2.0", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "lucide-react": "^0.539.0", "moment": "^2.30.1", "next": "^15.4.10", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "react": "19.1.0", "react-datepicker": "^8.7.0", "react-dom": "19.1.0", "react-redux": "^9.2.0", "react-spinners": "^0.17.0", "recharts": "^3.1.2", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/axios": "^0.9.36", "@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}