import Link from 'next/link';
import { Home } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

export default function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-background text-foreground px-4">
      <div className="text-center">
   
        <Home className="mx-auto mb-4 h-24 w-24 text-primary" />
        <h1 className="text-8xl font-bold text-primary mb-4">404</h1>
        <p className="text-2xl mb-8 text-muted-foreground">This page could not be found</p>
        <Button asChild size="lg">
          <Link href="/">
            Go Home
          </Link>
        </Button>
      </div>
    </div>
  );
}