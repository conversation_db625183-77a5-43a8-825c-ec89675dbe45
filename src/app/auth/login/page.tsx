"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { Building, ArrowRight, User, Edit2, CheckCircle, Chrome, Monitor, Eye, EyeOff } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  AppInput,
  PageLoader,
} from "@/components/ui-toolkit";

import { toast } from "sonner";
import { useAppDispatch, useAppSelector } from "@/redux/store";
import { validateTenant, loginUser, clearError, setTenant, activateAccount, verifyUserOTP, resendUserActivationOTP, forgotPassword } from "@/redux/slices/auth";
import { useThemeClasses, useThemeAccent } from "@/components/theme-provider";
import type { LoginRequest } from "@/types";

export default function LoginPage() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { tenant, loading, error, isAuthenticated, mfaRequired, activationRequired, user } = useAppSelector((state) => state.auth);
  const themeClasses = useThemeClasses();
  const { setAccent } = useThemeAccent();

  const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';

  const [step, setStep] = React.useState<"tenant" | "verification" | "credentials" | "user-activation" | "forgot-password">("tenant");
  const [branding, setBranding] = React.useState<any>(null);
  const [isLoaded, setIsLoaded] = React.useState(false);

  // Branding text states to prevent flicker
  const [tenantName, setTenantName] = React.useState("Tickflo");
  const [tagline, setTagline] = React.useState("Multi-Tenant Ticket Management System");
  const [description, setDescription] = React.useState("Streamline your customer support with powerful ticket management, knowledge base, and analytics - all in one place.");
  const [featureHighlights, setFeatureHighlights] = React.useState([
    "Multi-tenant architecture",
    "Real-time collaboration",
    "Advanced analytics & reporting"
  ]);

  // Tenant state
  const [tenantSlug, setTenantSlug] = React.useState("");

  // Login state
  const [email, setEmail] = React.useState("");
  const [password, setPassword] = React.useState("");
  const [showPassword, setShowPassword] = React.useState(false);
  const [mfaCode, setMfaCode] = React.useState("");
  const [showMFA, setShowMFA] = React.useState(false);
  const [verificationCode, setVerificationCode] = React.useState("");
  const [activationPassword, setActivationPassword] = React.useState("");
  const [showActivationPassword, setShowActivationPassword] = React.useState(false);

  // User activation state
  const [activationOTP, setActivationOTP] = React.useState("");
  const [activationUserId, setActivationUserId] = React.useState("");
  const [resendTimer, setResendTimer] = React.useState(0);

  // Show MFA field if MFA is required
  const shouldShowMFA = showMFA || mfaRequired;

  // Check localStorage for saved tenant on mount
  React.useLayoutEffect(() => {
    const savedTenantSlug = localStorage.getItem("tenantSlug");
    const savedTenantName = localStorage.getItem("tenantName");
    const savedTenantData = localStorage.getItem("tenant");
    const savedBranding = localStorage.getItem("branding");

    // Initialize states with localStorage data immediately to prevent flicker
    if (savedTenantData && savedTenantData !== "null") {
      try {
        const tenantData = JSON.parse(savedTenantData);
        if (tenantData) {
          setTenantName(tenantData.name || tenantData.companyName || "Tickflo");
        } else {
          setTenantName("Tickflo");
        }
      } catch (error) {
        console.error("Error parsing saved tenant:", error);
        setTenantName("Tickflo");
      }
    } else {
      setTenantName("Tickflo");
    }

    if (savedBranding && savedBranding !== "null") {
      try {
        const brandingData = JSON.parse(savedBranding);
        if (brandingData) {
          setTagline(brandingData.tagline || "Multi-Tenant Ticket Management System");
          setDescription(brandingData.description || "Streamline your customer support with powerful ticket management, knowledge base, and analytics - all in one place.");
          if (Array.isArray(brandingData.featureHighlights) && brandingData.featureHighlights.length > 0) {
            setFeatureHighlights(brandingData.featureHighlights);
          } else {
            setFeatureHighlights([
              "Multi-tenant architecture",
              "Real-time collaboration",
              "Advanced analytics & reporting"
            ]);
          }
        } else {
          setTagline("Multi-Tenant Ticket Management System");
          setDescription("Streamline your customer support with powerful ticket management, knowledge base, and analytics - all in one place.");
          setFeatureHighlights([
            "Multi-tenant architecture",
            "Real-time collaboration",
            "Advanced analytics & reporting"
          ]);
        }
      } catch (error) {
        console.error("Error parsing saved branding:", error);
        setTagline("Multi-Tenant Ticket Management System");
        setDescription("Streamline your customer support with powerful ticket management, knowledge base, and analytics - all in one place.");
        setFeatureHighlights([
          "Multi-tenant architecture",
          "Real-time collaboration",
          "Advanced analytics & reporting"
        ]);
      }
    } else {
      setTagline("Multi-Tenant Ticket Management System");
      setDescription("Streamline your customer support with powerful ticket management, knowledge base, and analytics - all in one place.");
      setFeatureHighlights([
        "Multi-tenant architecture",
        "Real-time collaboration",
        "Advanced analytics & reporting"
      ]);
    }

    // Accept either explicit slug/name keys OR a saved tenant JSON object.
    if (savedTenantData || (savedTenantSlug && savedTenantName && savedTenantData)) {
      try {
        // Prefer parsing the saved tenant object if present
        const tenantData = savedTenantData ? JSON.parse(savedTenantData) : null;

        // If tenant object exists, derive slug/name from it when explicit keys are missing
        if (tenantData && tenantData.slug) {
          setTenantSlug(tenantData.slug);

          // Load saved branding immediately to avoid flickering
          if (savedBranding) {
            try {
              const brandingData = JSON.parse(savedBranding);
              setBranding(brandingData);
              // Apply accent if stored
              if (brandingData?.customColors && brandingData?.colors) {
                setAccent(brandingData.colors);
              }
            } catch (error) {
              console.error("Error parsing saved branding:", error);
            }
          }

          // Set tenant data immediately to avoid "Tickflo" flickering
          dispatch(setTenant(tenantData));

          // Re-validate tenant to get fresh data
          dispatch(validateTenant(tenantData.slug))
            .unwrap()
            .then((result) => {
              if (result) {
                let updatedTenant = {
                  tenantId: (result as any).tenantId,
                  name: (result as any).name,
                  slug: (result as any).slug,
                  branding: (result as any).branding,
                  companyName: (result as any).companyName,
                  isCompanyOwnerActivated: (result as any).isCompanyOwnerActivated,
                  tenantStatus: (result as any).tenantStatus,
                  vertical: (result as any).vertical,
                };

                dispatch(setTenant(updatedTenant));
                localStorage.setItem("tenant", JSON.stringify(updatedTenant));

                // Update states with fresh data
                setTenantName(updatedTenant.name || updatedTenant.companyName || "Tickflo");

                // Handle branding data if it exists
                if ((result as any).branding) {
                  const brandingData = (result as any).branding;
                  setBranding(brandingData);
                  localStorage.setItem("branding", JSON.stringify(brandingData));

                  // Update branding states
                  setTagline(brandingData.tagline || "Multi-Tenant Ticket Management System");
                  setDescription(brandingData.description || "Streamline your customer support with powerful ticket management, knowledge base, and analytics - all in one place.");
                  if (Array.isArray(brandingData.featureHighlights) && brandingData.featureHighlights.length > 0) {
                    setFeatureHighlights(brandingData.featureHighlights);
                  } else {
                    setFeatureHighlights([
                      "Multi-tenant architecture",
                      "Real-time collaboration",
                      "Advanced analytics & reporting"
                    ]);
                  }

                  // Set accent theme if customColors is true
                  if (brandingData.customColors && brandingData.colors) {
                    localStorage.setItem("tickflo-accent-theme", brandingData.colors);
                    setAccent(brandingData.colors);
                  }
                } else {
                  setTagline("Multi-Tenant Ticket Management System");
                  setDescription("Streamline your customer support with powerful ticket management, knowledge base, and analytics - all in one place.");
                  setFeatureHighlights([
                    "Multi-tenant architecture",
                    "Real-time collaboration",
                    "Advanced analytics & reporting"
                  ]);
                }

                setStep("credentials");
              } else {
                // If validation fails, clear localStorage and stay on tenant step
                localStorage.removeItem("tenant");
                localStorage.removeItem("branding");
                localStorage.removeItem("tenantSlug");
                localStorage.removeItem("tenantName");
              }
              setIsLoaded(true);
            })
            .catch((error) => {
              // If validation fails, clear localStorage and stay on tenant step
              localStorage.removeItem("tenant");
              localStorage.removeItem("branding");
              localStorage.removeItem("tenantSlug");
              localStorage.removeItem("tenantName");
              setIsLoaded(true);
            });
        } else if (savedTenantSlug && savedTenantName && savedTenantData) {
          // Fallback to older shape where separate keys exist
          const parsed = JSON.parse(savedTenantData);
          setTenantSlug(savedTenantSlug);
          dispatch(setTenant(parsed));
          setStep("credentials");
          setIsLoaded(true);
        } else {
          setIsLoaded(true);
        }

        // Load saved branding if exists (for fallback case)
        if (!tenantData && savedBranding) {
          try {
            const brandingData = JSON.parse(savedBranding);
            setBranding(brandingData);
            // Apply accent if stored
            if (brandingData?.customColors && brandingData?.colors) {
              setAccent(brandingData.colors);
            }
          } catch (error) {
            console.error("Error parsing saved branding:", error);
          }
        }
      } catch (error) {
        localStorage.removeItem("tenant");
        localStorage.removeItem("branding");
        setIsLoaded(true);
      }
    } else {
      // No saved data, just finish initializing
      setIsLoaded(true);
    }
  }, [dispatch, setAccent]);

  // Clear errors when they change
  React.useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        dispatch(clearError());
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, dispatch]);

  // Redirect if authenticated
  React.useEffect(() => {
    if (isAuthenticated && user) {
      // Check userType and route accordingly
      if (user.userType === "0") {
        // Self Service user - set operatorMode to false and route to self-service
        localStorage.setItem('operatorMode', JSON.stringify(false));
        router.push("/self-service");
      } else if (user.userType === "1") {
        // Operator user - set operatorMode to true and route to dashboard
        localStorage.setItem('operatorMode', JSON.stringify(true));
        router.push("/dashboard");
      } else {
        // Default fallback - route to dashboard
        localStorage.setItem('operatorMode', JSON.stringify(true));
        router.push("/dashboard");
      }
    }
  }, [isAuthenticated, user, router]);

  // Show MFA message when required
  React.useEffect(() => {
    if (mfaRequired) {
      toast.info("Please enter your 2FA code to complete login");
    }
  }, [mfaRequired]);

  // Timer for resend OTP
  React.useEffect(() => {
    let interval: NodeJS.Timeout;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(prev => prev - 1);
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [resendTimer]);

  const handleTenantSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!tenantSlug.trim()) {
      toast.error("Please enter an organization name");
      return;
    }

    try {
      const result = await dispatch(validateTenant(tenantSlug.trim())).unwrap();
      if (result) {
        // Check if tenant is inactive and requires verification
        console.log("result", (result as any));
        let tenant = {
          tenantId: (result as any).tenantId,
          name: (result as any).name,
          slug: (result as any).slug,
          branding: (result as any).branding,
          companyName: (result as any).companyName,
          isCompanyOwnerActivated: (result as any).isCompanyOwnerActivated,
          tenantStatus: (result as any).tenantStatus,
          vertical: (result as any).vertical,
        };
        localStorage.setItem("tenant", JSON.stringify(tenant));
        // Handle branding data if it exists
        if ((result as any).branding) {
          const brandingData = (result as any).branding;
          setBranding(brandingData);
          localStorage.setItem("branding", JSON.stringify(brandingData));

          // Update state variables immediately
          setTenantName(result.name || result.companyName || "Tickflo");
          setTagline(brandingData.tagline || "Multi-Tenant Ticket Management System");
          setDescription(brandingData.description || "Streamline your customer support with powerful ticket management, knowledge base, and analytics - all in one place.");
          if (Array.isArray(brandingData.featureHighlights) && brandingData.featureHighlights.length > 0) {
            setFeatureHighlights(brandingData.featureHighlights);
          } else {
            setFeatureHighlights([
              "Multi-tenant architecture",
              "Real-time collaboration",
              "Advanced analytics & reporting"
            ]);
          }

          // Set accent theme if customColors is true
          if (brandingData.customColors && brandingData.colors) {
            localStorage.setItem("tickflo-accent-theme", brandingData.colors);
            setAccent(brandingData.colors);
          }
        } else {
          // Update tenant name even if no branding
          setTenantName(result.name || result.companyName || "Tickflo");
          setTagline("Multi-Tenant Ticket Management System");
          setDescription("Streamline your customer support with powerful ticket management, knowledge base, and analytics - all in one place.");
          setFeatureHighlights([
            "Multi-tenant architecture",
            "Real-time collaboration",
            "Advanced analytics & reporting"
          ]);
        }

        // Check if tenant is suspended/blocked
        if (result.tenantStatus === 'suspended') {
          toast.info("Tenant is blocked by admin.");
          return;
        }

        // Check if tenant requires verification (inactive or active but owner not activated)
        if (result.tenantStatus === 'inactive' || (result.tenantStatus === 'active' && result.isCompanyOwnerActivated === false)) {
          toast.warning("Tenant requires verification. Please enter verification code.");
          setStep("verification");
          return;
        }

        setStep("credentials");
        toast.success(`Welcome to ${result.name || result.companyName || "Tickflo"}!`);
      } else {
        toast.error("Organization not found. Please check the name and try again.");
      }
    } catch (error: any) {
      toast.error(error || "Failed to validate organization");
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim() || !password.trim()) {
      toast.error("Please fill in all fields");
      return;
    }

    if (shouldShowMFA && !mfaCode.trim()) {
      toast.error("Please enter your 2FA code");
      return;
    }

    if (!tenant?.tenantId) {
      toast.error("Tenant information is missing. Please validate tenant first.");
      return;
    }

    // Check if tenant is suspended/blocked
    if (tenant.tenantStatus === 'suspended') {
      toast.info("Tenant is blocked by admin.");
      return;
    }

    // Check if tenant requires activation before attempting login
    if (tenant.tenantStatus === 'inactive' || (tenant.tenantStatus === 'active' && tenant.isCompanyOwnerActivated === false)) {
      toast.warning("Account requires activation. Please enter activation code.");
      setStep("verification");
      return;
    }

    const loginData: LoginRequest = {
      email: email.trim(),
      password: password.trim(),
      tenantId: tenant.tenantId,
      ...(shouldShowMFA && { mfaCode: mfaCode.trim() }),
    };

    try {
      const result = await dispatch(loginUser(loginData)).unwrap();

      // Check if account activation is required
      if (result.activationRequired) {
        setActivationUserId(result.user._id);
        setResendTimer(60); // Start 60 second timer
        toast.info("Account activation required. Please check your email for OTP.");
        setStep("user-activation");
        return;
      }

      toast.success("Login successful!");
      // Navigation will be handled by the useEffect hook
    } catch (error: any) {
      // Check if error is due to inactive tenant
      toast.error(error || "Invalid email or password");
    }
  };

  const handleVerificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!verificationCode.trim() || !activationPassword.trim()) {
      toast.error("Please enter activation code and password");
      return;
    }

    try {
      const result = await dispatch(activateAccount({
        activationCode: verificationCode.trim(),
        password: activationPassword.trim(),
      })).unwrap();

      toast.success("Account activated successfully!");
      // Since activateAccount logs the user in directly, redirect to dashboard
      // The useEffect will handle the navigation
    } catch (error: any) {
      toast.error(error || "Activation failed");
    }
  };

  const handleUserActivationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!activationOTP.trim()) {
      toast.error("Please enter the OTP");
      return;
    }

    if (!activationUserId) {
      toast.error("User information is missing. Please try logging in again.");
      return;
    }

    try {
      await dispatch(verifyUserOTP({
        userId: activationUserId,
        otp: activationOTP.trim(),
      })).unwrap();

      toast.success("Account activated successfully! Please log in.");
      setStep("credentials");
    } catch (error: any) {
      toast.error(error || "OTP verification failed");
    }
  };

  const handleResendOTP = async () => {
    if (!activationUserId) {
      toast.error("User information is missing. Please try logging in again.");
      return;
    }

    try {
      await dispatch(resendUserActivationOTP(activationUserId)).unwrap();
      setResendTimer(60); // Reset timer to 60 seconds
      toast.success("New OTP sent to your email");
    } catch (error: any) {
      toast.error(error || "Failed to resend OTP");
    }
  };

  const handleForgotPasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) {
      toast.error("Please enter your email");
      return;
    }

    if (!tenant?.tenantId) {
      toast.error("Tenant information is missing");
      return;
    }

    try {
      await dispatch(forgotPassword({
        email: email.trim(),
        tenantId: tenant.tenantId
      })).unwrap();

      toast.success("If an account exists, a reset link has been sent to your email.");
      setStep("credentials");
    } catch (error: any) {
      toast.error(error || "Failed to send reset link");
    }
  };

  const handleChangeTenant = () => {
    localStorage.removeItem("tenant");
    localStorage.removeItem("branding");
    // also clear individually stored slug/name when present
    localStorage.removeItem("tenantSlug");
    localStorage.removeItem("tenantName");
    setTenantSlug("");
    setBranding(null);
    dispatch(setTenant(null as any));

    // Reset state variables to defaults
    setTenantName("Tickflo");
    setTagline("Multi-Tenant Ticket Management System");
    setDescription("Streamline your customer support with powerful ticket management, knowledge base, and analytics - all in one place.");
    setFeatureHighlights([
      "Multi-tenant architecture",
      "Real-time collaboration",
      "Advanced analytics & reporting"
    ]);

    setStep("tenant");
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br ${themeClasses.background} flex relative`}>
      {/* Blur overlay when loading */}
      {!isLoaded && (
        // <div className="absolute inset-0 bg-grey backdrop-blur-sm z-50 flex items-center justify-center">
        //   <PageLoader
        //     variant="theme"
        //     size="lg"
        //   />
        // </div>
        <></>
      )}

      {/* Left side - Background */}
      <div className={`hidden lg:flex lg:w-1/2 bg-gradient-to-br ${themeClasses.leftSide} relative overflow-hidden`}>
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>


        <div className="relative z-10 flex flex-col justify-center px-12 text-white">
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <div className="h-12 w-12 bg-white/15 rounded-xl flex items-center justify-center backdrop-blur-sm">
                {
                  branding?.url ?
                    <img src={branding.url} alt="Branding" className="h-12 w-12 rounded-xl" />
                    : <Building className="h-6 w-6" />
                }
              </div>
              <h1 className="text-3xl font-bold">
                {tenantName}
              </h1>
            </div>
            <div className="space-y-4">
              <h2 className="text-4xl font-bold leading-tight">
                {tagline}
              </h2>
              <p className="text-xl text-slate-200">
                {description}
              </p>
            </div>
            <div className="grid grid-cols-1 gap-4 mt-8">
              {featureHighlights.map((feat: string, idx: number) => (
                <div key={idx} className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-emerald-400" />
                  <span>{feat}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
        <a
          href="https://cyranixtech.com"
          target="_blank"
          rel="noopener noreferrer"
          className="absolute bottom-0 left-0 cursor-pointer"
        >
          <img src={"/assets/power_by.svg"} alt="Powered by Cyranix" style={{
            position: "relative",
            bottom: -12,
            left: 20,
            height: 100,
            width: 150,
            zIndex:9999999
          }}
          onClick={() => window.open("https://cyranixtech.com", "_blank")}
          />
        </a>
      </div>

      {/* Right side - Login */}
      <div className="flex-1 flex items-center justify-center p-8">
        {
          isLoaded ?
            <div className="w-full max-w-md">
              <AppCard className="border-slate-200 shadow-lg bg-white/80 backdrop-blur-sm">
                <AppCardHeader className="text-center pb-6">
                  <div className="flex justify-center mb-4">
                    <div className={`h-12 w-12 ${themeClasses.iconBg} rounded-xl flex items-center justify-center`}>
                      <Building className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <AppCardTitle className="text-2xl font-bold text-slate-900">
                    {step === "tenant" ? "Welcome to Tickflo" : step === "verification" ? "Activate Your Account" : step === "user-activation" ? "Verify Your Email" : step === "forgot-password" ? "Reset Password" : `Welcome to ${tenantName}`}
                  </AppCardTitle>
                  <AppCardDescription className="text-slate-600">
                    {step === "tenant"
                      ? "Enter your organization name to continue"
                      : step === "verification"
                        ? "Enter the activation code and set your password"
                        : step === "user-activation"
                          ? "Enter the OTP sent to your email to activate your account"
                          : step === "forgot-password"
                            ? "Enter your email to receive a reset link"
                            : "Sign in to your account"
                    }
                  </AppCardDescription>
                </AppCardHeader>

                <AppCardContent>
                  {step === "tenant" ? (
                    <form onSubmit={handleTenantSubmit} className="space-y-4">
                      <AppInput
                        label="Organization Name"
                        placeholder="e.g., cyranix"
                        value={tenantSlug}
                        onChange={(e) => setTenantSlug(e.target.value)}
                        required
                        disabled={loading}
                      />

                      <AppButton
                        type="submit"
                        className="w-full cursor-pointer"
                        loading={loading}
                        icon={<ArrowRight className="h-4 w-4" />}
                      >
                        Continue
                      </AppButton>

                      {/* <div className="text-center text-sm text-muted-foreground">
                    Try:{' '}
                    <button
                      type="button"
                      className="text-slate-600 hover:text-slate-800 hover:underline"
                      onClick={() => setTenantSlug('cyranix')}
                    >
                      cyranix
                    </button>
                    {' '}or{' '}
                    <button
                      type="button"
                      className="text-slate-600 hover:text-slate-800 hover:underline"
                      onClick={() => setTenantSlug('acme-labs')}
                    >
                      acme-labs
                    </button>
                  </div> */}
                    </form>
                  ) : step === "verification" ? (
                    <form onSubmit={handleVerificationSubmit} className="space-y-4">
                      {/* Tenant Info */}
                      <div className="bg-slate-50 border border-slate-200 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Building className="h-4 w-4 text-slate-600" />
                            <span className="text-sm font-medium text-slate-900">
                              {tenantName}
                            </span>
                          </div>
                          <button
                            onClick={handleChangeTenant}
                            className="text-slate-600 hover:text-slate-800 text-sm flex items-center space-x-1"
                          >
                            <Edit2 className="h-3 w-3" />
                            <span>Change</span>
                          </button>
                        </div>
                      </div>

                      <AppInput
                        label="Activation Code"
                        placeholder="Enter 6-digit activation code"
                        value={verificationCode}
                        onChange={(e) => setVerificationCode(e.target.value)}
                        required
                        disabled={loading}
                        maxLength={6}
                      />

                      <AppInput
                        label="Password"
                        type={showActivationPassword ? "text" : "password"}
                        placeholder="Enter your password"
                        value={activationPassword}
                        onChange={(e) => setActivationPassword(e.target.value)}
                        required
                        disabled={loading}
                        endIcon={
                          <button
                            type="button"
                            onClick={() => setShowActivationPassword(!showActivationPassword)}
                            className="focus:outline-none hover:text-foreground"
                          >
                            {showActivationPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </button>
                        }
                      />

                      <AppButton
                        type="submit"
                        className="w-full cursor-pointer"
                        loading={loading}
                        icon={<ArrowRight className="h-4 w-4" />}
                      >
                        Activate & Sign In
                      </AppButton>
                    </form>
                  ) : step === "user-activation" ? (
                    <form onSubmit={handleUserActivationSubmit} className="space-y-4">
                      {/* Tenant Info */}
                      <div className="bg-slate-50 border border-slate-200 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Building className="h-4 w-4 text-slate-600" />
                            <span className="text-sm font-medium text-slate-900">
                              {tenantName}
                            </span>
                          </div>
                          <button
                            onClick={handleChangeTenant}
                            className="text-slate-600 hover:text-slate-800 text-sm flex items-center space-x-1"
                          >
                            <Edit2 className="h-3 w-3" />
                            <span>Change</span>
                          </button>
                        </div>
                      </div>

                      <div className="text-center text-sm text-slate-600 mb-4">
                        We've sent a 6-digit OTP to your email address. Please enter it below to activate your account.
                      </div>

                      <AppInput
                        label="OTP Code"
                        placeholder="Enter 6-digit OTP"
                        value={activationOTP}
                        onChange={(e) => setActivationOTP(e.target.value)}
                        required
                        disabled={loading}
                        maxLength={6}
                      />

                      <div className="flex items-center justify-between">
                        <AppButton
                          type="submit"
                          className="flex-1 cursor-pointer mr-2"
                          loading={loading}
                          icon={<ArrowRight className="h-4 w-4" />}
                        >
                          Verify & Activate
                        </AppButton>

                        <button
                          type="button"
                          onClick={handleResendOTP}
                          disabled={resendTimer > 0 || loading}
                          className="px-4 py-2 text-sm text-slate-600 hover:text-slate-800 disabled:text-slate-400 disabled:cursor-not-allowed"
                        >
                          {resendTimer > 0 ? `Resend in ${resendTimer}s` : 'Resend OTP'}
                        </button>
                      </div>
                    </form>
                  ) : step === "forgot-password" ? (
                    <form onSubmit={handleForgotPasswordSubmit} className="space-y-4">
                      <div className="bg-slate-50 border border-slate-200 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Building className="h-4 w-4 text-slate-600" />
                            <span className="text-sm font-medium text-slate-900">
                              {tenantName}
                            </span>
                          </div>
                          <button
                            type="button"
                            onClick={() => setStep("credentials")}
                            className="text-slate-600 hover:text-slate-800 text-sm flex items-center space-x-1"
                          >
                            <span>Back to Login</span>
                          </button>
                        </div>
                      </div>

                      <div className="text-center text-sm text-slate-600 mb-4">
                        Enter your email address and we'll send you a link to reset your password.
                      </div>

                      <AppInput
                        label="Email Address"
                        type="email"
                        placeholder="Enter your email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        disabled={loading}
                      />

                      <AppButton
                        type="submit"
                        className="w-full cursor-pointer"
                        loading={loading}
                        icon={<ArrowRight className="h-4 w-4" />}
                      >
                        Send Reset Link
                      </AppButton>
                    </form>
                  ) : (
                    <div className="space-y-6">
                      {/* Tenant Info */}
                      <div className="bg-slate-50 border border-slate-200 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Building className="h-4 w-4 text-slate-600" />
                            <span className="text-sm font-medium text-slate-900">
                              {tenantName}
                            </span>
                          </div>
                          <button
                            onClick={handleChangeTenant}
                            className="text-slate-600 hover:text-slate-800 text-sm flex items-center space-x-1"
                          >
                            <Edit2 className="h-3 w-3" />
                            <span>Change</span>
                          </button>
                        </div>
                      </div>

                      {/* Login Form */}
                      <form onSubmit={handleLogin} className="space-y-4">
                        <AppInput
                          label="Email Address"
                          type="email"
                          placeholder="Enter your email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          required
                          disabled={loading}
                        />

                        <AppInput
                          label="Password"
                          type={showPassword ? "text" : "password"}
                          placeholder="Enter your password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          required
                          disabled={loading}
                          endIcon={
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="focus:outline-none hover:text-foreground"
                            >
                              {showPassword ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </button>
                          }
                        />

                        <div className="flex justify-end">
                          <button
                            type="button"
                            onClick={() => setStep("forgot-password")}
                            className="text-sm text-primary hover:underline"
                          >
                            Forgot Password?
                          </button>
                        </div>

                        {shouldShowMFA && (
                          <AppInput
                            label="2FA Code"
                            type="text"
                            placeholder="Enter 6-digit code"
                            value={mfaCode}
                            onChange={(e) => setMfaCode(e.target.value)}
                            required
                            disabled={loading}
                            maxLength={6}
                          />
                        )}
                        <AppButton
                          type="submit"
                          className="w-full cursor-pointer"
                          loading={loading}
                          icon={<User className="h-4 w-4" />}
                        >
                          Sign In
                        </AppButton>

                        <div className="space-y-4">
                          <div className="relative">
                            <div className="absolute inset-0 flex items-center">
                              <span className="w-full border-t" />
                            </div>
                            <div className="relative flex justify-center text-xs uppercase">
                              <span className="bg-white px-2 text-muted-foreground">
                                Or continue with
                              </span>
                            </div>
                          </div>
                          <div className="grid grid-cols-2 gap-2">
                            <AppButton
                              type="button"
                              className="w-full bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 hover:border-gray-400 shadow-sm"
                              onClick={() => {
                                console.log(`${API_URL}/auth/google?tenant=${tenantSlug}`)
                                console.log(`${API_URL}/auth/google?tenant=${tenantSlug}`)
                                window.location.href = `${API_URL}/auth/google?tenant=${tenantSlug}`
                              }}
                              icon={
                                <svg className="h-5 w-5" viewBox="0 0 24 24">
                                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                                </svg>
                              }
                            >
                              Continue with Google
                            </AppButton>
                            <AppButton
                              type="button"
                              className="w-full bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 hover:border-gray-400 shadow-sm"
                              onClick={() => window.location.href = `${API_URL}/auth/microsoft?tenant=${encodeURIComponent(tenantSlug)}`}
                              icon={
                                <svg className="h-5 w-5" viewBox="0 0 24 24">
                                  <path fill="#F25022" d="M1 1h10v10H1z" />
                                  <path fill="#00A4EF" d="M12 1h10v10H12z" />
                                  <path fill="#7FBA00" d="M1 12h10v10H1z" />
                                  <path fill="#FFB900" d="M12 12h10v10H12z" />
                                </svg>
                              }
                            >
                              Continue with Microsoft
                            </AppButton>
                          </div>
                        </div>


                      </form>

                      <div className="text-center text-sm text-muted-foreground">
                        Demo credentials: <EMAIL> / Zuni@123
                      </div>

                      {error && (
                        <div className="text-center text-sm text-red-600 bg-red-50 p-2 rounded">
                          {error}
                        </div>
                      )}
                    </div>
                  )}
                </AppCardContent>
              </AppCard>
            </div> :
            <PageLoader
              variant="theme"
              size="lg"
            />
        }

      </div>
    </div>
  );
}
