"use client";

import * as React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Building, ArrowRight, Shield, CheckCircle } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  AppInput,
} from "@/components/ui-toolkit";
import { toast } from "sonner";
import { useAppDispatch, useAppSelector } from "@/redux/store";
import { verifyTenant, clearError } from "@/redux/slices/auth";

export const dynamic = 'force-dynamic';

function VerifyPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();
  const { tenant, loading, error } = useAppSelector((state) => state.auth);

  const [tenantSlug, setTenantSlug] = React.useState("");
  const [verificationCode, setVerificationCode] = React.useState("");

  // Get params from URL if available
  React.useEffect(() => {
    const slugFromUrl = searchParams.get("tenantSlug");
    const codeFromUrl = searchParams.get("code");
    if (slugFromUrl) {
      setTenantSlug(slugFromUrl);
    }
    if (codeFromUrl) {
      setVerificationCode(codeFromUrl);
    }
  }, [searchParams]);

  // Clear errors when they change
  React.useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        dispatch(clearError());
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, dispatch]);

  const handleVerifySubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!tenantSlug.trim() || !verificationCode.trim()) {
      toast.error("Please fill in all fields");
      return;
    }

    try {
      const result = await dispatch(verifyTenant({
        tenantSlug: tenantSlug.trim(),
        verificationCode: verificationCode.trim(),
      })).unwrap();

      toast.success("Tenant verified successfully!");
      router.push("/auth/login");
    } catch (error: any) {
      toast.error(error || "Verification failed");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
            <Shield className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900">Verify Your Tenant</h1>
          <p className="text-gray-600 mt-2">
            Enter your tenant slug and verification code to activate your account
          </p>
        </div>

        <AppCard>
          <AppCardHeader>
            <AppCardTitle className="flex items-center gap-2">
              <Building className="w-5 h-5" />
              Tenant Verification
            </AppCardTitle>
            <AppCardDescription>
              Please provide the verification details sent to your email
            </AppCardDescription>
          </AppCardHeader>
          <AppCardContent>
            <form onSubmit={handleVerifySubmit} className="space-y-4">
              <div>
                <label htmlFor="tenantSlug" className="block text-sm font-medium text-gray-700 mb-1">
                  Tenant Slug
                </label>
                <AppInput
                  id="tenantSlug"
                  type="text"
                  value={tenantSlug}
                  onChange={(e) => setTenantSlug(e.target.value)}
                  placeholder="Enter your tenant slug"
                  required
                />
              </div>

              <div>
                <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700 mb-1">
                  Verification Code
                </label>
                <AppInput
                  id="verificationCode"
                  type="text"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  placeholder="Enter 6-digit verification code"
                  maxLength={6}
                  required
                />
              </div>

              {error && (
                <div className="text-red-600 text-sm bg-red-50 p-3 rounded-md">
                  {error}
                </div>
              )}

              <AppButton
                type="submit"
                className="w-full"
                disabled={loading}
              >
                {loading ? (
                  "Verifying..."
                ) : (
                  <>
                    Verify Tenant
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </>
                )}
              </AppButton>
            </form>

            <div className="mt-6 text-center">
              <button
                onClick={() => router.push("/auth/login")}
                className="text-sm text-blue-600 hover:text-blue-500"
              >
                Back to Login
              </button>
            </div>
          </AppCardContent>
        </AppCard>
      </div>
    </div>
  );
}

export default function VerifyPage() {
  return (
    <React.Suspense fallback={<div>Loading...</div>}>
      <VerifyPageContent />
    </React.Suspense>
  );
}
