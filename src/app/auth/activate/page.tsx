"use client";

export const dynamic = 'force-dynamic';

import * as React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Building, ArrowRight, Shield, CheckCircle, Lock, Key } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  AppInput,
} from "@/components/ui-toolkit";
import { toast } from "sonner";
import { useAppDispatch, useAppSelector } from "@/redux/store";
import { activateAccount, clearError } from "@/redux/slices/auth";

function ActivatePageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();
  const { user, loading, error } = useAppSelector((state) => state.auth);

  const [activationCode, setActivationCode] = React.useState("");
  const [password, setPassword] = React.useState("");
  const [confirmPassword, setConfirmPassword] = React.useState("");

  // Get activation code from URL params if available
  React.useEffect(() => {
    const codeFromUrl = searchParams.get("code");
    if (codeFromUrl) {
      setActivationCode(codeFromUrl);
    }
  }, [searchParams]);

  // Clear errors when they change
  React.useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        dispatch(clearError());
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, dispatch]);

  const handleActivateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!activationCode.trim()) {
      toast.error("Please enter the 6-digit activation code");
      return;
    }

    if (activationCode.length !== 6) {
      toast.error("Activation code must be 6 digits");
      return;
    }

    if (!password.trim()) {
      toast.error("Please enter a password");
      return;
    }

    if (password.length < 8) {
      toast.error("Password must be at least 8 characters long");
      return;
    }

    if (password !== confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    try {
      const result = await dispatch(activateAccount({
        activationCode: activationCode.trim(),
        password: password.trim()
      })).unwrap();
      
      if (result) {
        toast.success("Account activated successfully!");
        router.push("/dashboard");
      }
    } catch (error: any) {
      toast.error(error || "Invalid activation code");
    }
  };

  const handleActivationCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "").slice(0, 6);
    setActivationCode(value);
  };

  const handleBackToLogin = () => {
    router.push("/auth/login");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-slate-100 flex">
      {/* Left side - Background */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-slate-700 via-slate-800 to-slate-900 relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="relative z-10 flex flex-col justify-center px-12 text-white">
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <div className="h-12 w-12 bg-white/15 rounded-xl flex items-center justify-center backdrop-blur-sm">
                <Building className="h-6 w-6" />
              </div>
              <h1 className="text-3xl font-bold">Tickflo</h1>
            </div>
            <div className="space-y-4">
              <h2 className="text-4xl font-bold leading-tight">
                Activate Your Account
              </h2>
              <p className="text-xl text-slate-200">
                Complete your account setup by entering your activation code and setting up your password.
              </p>
            </div>
            <div className="grid grid-cols-1 gap-4 mt-8">
              <div className="flex items-center space-x-3">
                <Shield className="h-5 w-5 text-emerald-400" />
                <span>Secure account activation</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-emerald-400" />
                <span>Password protected access</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-emerald-400" />
                <span>Complete onboarding process</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right side - Activation */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          <AppCard className="border-slate-200 shadow-lg bg-white/80 backdrop-blur-sm">
            <AppCardHeader className="text-center pb-6">
              <div className="flex justify-center mb-4">
                <div className="h-12 w-12 bg-slate-600 rounded-xl flex items-center justify-center">
                  <Key className="h-6 w-6 text-white" />
                </div>
              </div>
              <AppCardTitle className="text-2xl font-bold text-slate-900">
                Activate Your Account
              </AppCardTitle>
              <AppCardDescription className="text-slate-600">
                Enter your activation code and set up your password to get started
              </AppCardDescription>
            </AppCardHeader>

            <AppCardContent>
              <form onSubmit={handleActivateSubmit} className="space-y-6">
                <div className="space-y-2">
                  <AppInput
                    label="Activation Code"
                    placeholder="000000"
                    value={activationCode}
                    onChange={handleActivationCodeChange}
                    required
                    disabled={loading}
                    className="text-center text-2xl font-mono tracking-widest"
                    maxLength={6}
                  />
                  <p className="text-xs text-slate-500 text-center">
                    Enter the 6-digit code from your invitation email
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <AppInput
                      label="New Password"
                      type="password"
                      placeholder="Enter your password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      disabled={loading}
                      className="text-sm"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <AppInput
                      label="Confirm Password"
                      type="password"
                      placeholder="Confirm your password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                      disabled={loading}
                      className="text-sm"
                    />
                  </div>
                  
                  <div className="text-xs text-slate-500">
                    Password must be at least 8 characters long
                  </div>
                </div>

                <AppButton
                  type="submit"
                  className="w-full cursor-pointer"
                  loading={loading}
                  disabled={activationCode.length !== 6 || !password || !confirmPassword}
                  icon={<ArrowRight className="h-4 w-4" />}
                >
                  Activate Account
                </AppButton>

                <div className="text-center">
                  <button
                    type="button"
                    onClick={handleBackToLogin}
                    className="text-slate-600 hover:text-slate-800 text-sm hover:underline"
                  >
                    Back to Login
                  </button>
                </div>

                {error && (
                  <div className="text-center text-sm text-red-600 bg-red-50 p-3 rounded-lg">
                    {error}
                  </div>
                )}
              </form>
            </AppCardContent>
          </AppCard>
        </div>
      </div>
    </div>
  );
}

export default function ActivatePage() {
  return (
    <React.Suspense fallback={<div>Loading...</div>}>
      <ActivatePageContent />
    </React.Suspense>
  );
}
