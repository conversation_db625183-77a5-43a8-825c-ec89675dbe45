"use client";

import * as React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Building, ArrowRight, Eye, EyeOff, CheckCircle } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  AppInput,
  PageLoader,
} from "@/components/ui-toolkit";
import { toast } from "sonner";
import { useAppDispatch } from "@/redux/store";
import { resetPassword, validateTenant } from "@/redux/slices/auth";
import { useThemeClasses, useThemeAccent } from "@/components/theme-provider";

export default function ResetPasswordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();
  const themeClasses = useThemeClasses();
  const { setAccent } = useThemeAccent();

  const token = searchParams.get("token");
  const tenantSlug = searchParams.get("tenant");

  const [password, setPassword] = React.useState("");
  const [confirmPassword, setConfirmPassword] = React.useState("");
  const [showPassword, setShowPassword] = React.useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);
  const [loading, setLoading] = React.useState(false);

  // Branding state
  const [tenantName, setTenantName] = React.useState("Tickflo");
  const [tagline, setTagline] = React.useState("Secure your account");
  const [description, setDescription] = React.useState("Create a new strong password to protect your account.");
  const [branding, setBranding] = React.useState<any>(null);
  const [featureHighlights, setFeatureHighlights] = React.useState<string[]>([]);
  const [isTenantLoaded, setIsTenantLoaded] = React.useState(false);

  React.useEffect(() => {
    if (tenantSlug) {
      dispatch(validateTenant(tenantSlug))
        .unwrap()
        .then((result: any) => {
          if (result) {
            setTenantName(result.name || result.companyName || "Tickflo");
            
            if (result.branding) {
              setBranding(result.branding);
              if (result.branding.tagline) setTagline(result.branding.tagline);
              if (result.branding.description) setDescription(result.branding.description);
              if (result.branding.featureHighlights) setFeatureHighlights(result.branding.featureHighlights);
              
              if (result.branding.customColors && result.branding.colors) {
                setAccent(result.branding.colors);
              }
            }
          }
        })
        .catch((err) => {
          console.error("Failed to load tenant branding", err);
        })
        .finally(() => {
          setIsTenantLoaded(true);
        });
    } else {
      setIsTenantLoaded(true);
    }
  }, [tenantSlug, dispatch, setAccent]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!token) {
      toast.error("Invalid or missing reset token");
      return;
    }

    if (password !== confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    if (password.length < 8) {
      toast.error("Password must be at least 8 characters");
      return;
    }

    setLoading(true);
    try {
      await dispatch(resetPassword({ token, password })).unwrap();
      toast.success("Password reset successfully! Please log in.");
      router.push(`/auth/login?tenant=${tenantSlug || ""}`);
    } catch (error: any) {
      toast.error(error || "Failed to reset password");
    } finally {
      setLoading(false);
    }
  };

  if (!token) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-slate-900 mb-2">Invalid Link</h1>
          <p className="text-slate-600 mb-4">The password reset link is invalid or missing.</p>
          <AppButton onClick={() => router.push("/auth/login")}>
            Go to Login
          </AppButton>
        </div>
      </div>
    );
  }

  if (!isTenantLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-50">
        <PageLoader variant="theme" size="lg" />
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br ${themeClasses.background} flex relative`}>
      {/* Left side - Background */}
      <div className={`hidden lg:flex lg:w-1/2 bg-gradient-to-br ${themeClasses.leftSide} relative overflow-hidden`}>
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="relative z-10 flex flex-col justify-center px-12 text-white">
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <div className="h-12 w-12 bg-white/15 rounded-xl flex items-center justify-center backdrop-blur-sm">
                {branding?.url ? (
                  <img src={branding.url} alt="Branding" className="h-12 w-12 rounded-xl" />
                ) : (
                  <Building className="h-6 w-6" />
                )}
              </div>
              <h1 className="text-3xl font-bold">{tenantName}</h1>
            </div>
            <div className="space-y-4">
              <h2 className="text-4xl font-bold leading-tight">
                {tagline}
              </h2>
              <p className="text-xl text-slate-200">
                {description}
              </p>
            </div>
            {featureHighlights.length > 0 && (
              <div className="grid grid-cols-1 gap-4 mt-8">
                {featureHighlights.map((feat: string, idx: number) => (
                  <div key={idx} className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-emerald-400" />
                    <span>{feat}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        <img src={"/assets/power_by.svg"} alt="Powered by Cyranix" style={{
            position: "relative",
            bottom: -12,
            left: 20,
            height: 100,
            width: 150,
            zIndex:9999999
          }}
          onClick={() => window.open("https://cyranixtech.com", "_blank")}
          />
      </div>

      {/* Right side - Form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          <AppCard className="border-slate-200 shadow-lg bg-white/80 backdrop-blur-sm">
            <AppCardHeader className="text-center pb-6">
              <AppCardTitle className="text-2xl font-bold text-slate-900">
                Reset Password
              </AppCardTitle>
              <AppCardDescription className="text-slate-600">
                Enter your new password below
              </AppCardDescription>
            </AppCardHeader>

            <AppCardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <AppInput
                  label="New Password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter new password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={loading}
                  endIcon={
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="focus:outline-none hover:text-foreground"
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  }
                />

                <AppInput
                  label="Confirm Password"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirm new password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  disabled={loading}
                  endIcon={
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="focus:outline-none hover:text-foreground"
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  }
                />

                <AppButton
                  type="submit"
                  className="w-full cursor-pointer"
                  loading={loading}
                  icon={<ArrowRight className="h-4 w-4" />}
                >
                  Reset Password
                </AppButton>
              </form>
            </AppCardContent>
          </AppCard>
        </div>
      </div>
    </div>
  );
}
