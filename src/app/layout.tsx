import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/sonner";
import ReduxProvider from "@/redux/providers/ReduxProvider";

// Import token utilities for global availability in development
if (process.env.NODE_ENV === 'development') {
  import("@/utils/tokenRefresh");
}

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: "Tickflo - Multi-Tenant Ticket System",
  description: "Professional multi-tenant ticket management system with comprehensive support features",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ThemeProvider>
          <ReduxProvider>
            {children}
          </ReduxProvider>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
