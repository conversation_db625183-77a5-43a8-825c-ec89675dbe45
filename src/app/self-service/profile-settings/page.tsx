'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  User,
  Mail,
  Phone,
  Bell,
  Shield,
  Key,
  Save,
  Camera,
  Clock,
  CheckCircle,
  AlertCircle,
  Settings,
  ArrowLeft,
  Home
} from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"

// Mock user data - in real app this would come from API
type MockUser = {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar: string;
  role: string | { _id: string; name: string; permissions: string[] };
  department: string;
  joinedDate: string;
  lastLogin: string;
  preferences: {
    emailNotifications: boolean;
    ticketUpdates: boolean;
    knowledgeBaseUpdates: boolean;
    marketingEmails: boolean;
    language: string;
    timezone: string;
  };
};

const mockUser: MockUser = {
  id: '1',
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+****************',
  avatar: '',
  role: 'User',
  department: 'IT Support',
  joinedDate: '2023-01-15',
  lastLogin: '2024-01-15 10:30 AM',
  preferences: {
    emailNotifications: true,
    ticketUpdates: true,
    knowledgeBaseUpdates: false,
    marketingEmails: false,
    language: 'en',
    timezone: 'America/New_York'
  }
}

export default function ProfileSettingsPage() {
  const [user, setUser] = useState(mockUser)
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [formData, setFormData] = useState({
    name: user.name,
    email: user.email,
    phone: user.phone,
    preferences: { ...user.preferences }
  })

  const handleInputChange = (field: string, value: string | boolean) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof typeof prev] as object),
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({ ...prev, [field]: value }))
    }
  }

  const handleSave = async () => {
    setIsSaving(true)

    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 1500))
      setUser(prev => ({ ...prev, ...formData }))
      setIsEditing(false)
      toast.success("Profile updated successfully!")
    } catch (error) {
      toast.error("Failed to update profile. Please try again.")
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      name: user.name,
      email: user.email,
      phone: user.phone,
      preferences: { ...user.preferences }
    })
    setIsEditing(false)
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  return (
    <div className="space-y-8 px-6 md:px-8 lg:px-12 max-w-4xl mx-auto">
      {/* Navigation Header */}
      <div className="flex items-center justify-between">
        {/* <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/self-service">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Self Service
            </Link>
          </Button>
        </div> */}
        <Button variant="ghost" size="sm" asChild>
          <Link href="/self-service">
            <Home className="h-4 w-4 mr-2" />
            Home
          </Link>
        </Button>
      </div>

      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
          Profile Settings
        </h1>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          Manage your account information and preferences
        </p>
      </div>

      <div className="grid gap-8 lg:grid-cols-3">
        {/* Profile Overview */}
        <div className="lg:col-span-1">
          <Card className="shadow-lg">
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <div className="relative">
                  <Avatar className="h-24 w-24 mx-auto">
                    <AvatarImage src={user.avatar} alt={user.name} />
                    <AvatarFallback className="text-2xl">
                      {getInitials(user.name)}
                    </AvatarFallback>
                  </Avatar>
                  <Button
                    size="sm"
                    variant="outline"
                    className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0"
                  >
                    <Camera className="h-4 w-4" />
                  </Button>
                </div>

                <div>
                  <h3 className="text-xl font-semibold">{user.name}</h3>
                  <p className="text-muted-foreground">{user.email}</p>
                  <div className="flex justify-center gap-2 mt-2">
                    <Badge variant="secondary">{typeof user.role === 'object' ? user.role.name : user.role}</Badge>
                    <Badge variant="outline">{user.department}</Badge>
                  </div>
                </div>

                <Separator />

                <div className="space-y-3 text-sm">
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Member since</span>
                    <span>{new Date(user.joinedDate).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Last login</span>
                    <span>{user.lastLogin}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Settings Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Personal Information */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <User className="h-5 w-5" />
                Personal Information
              </CardTitle>
              <CardDescription>
                Update your personal details and contact information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  disabled={!isEditing}
                />
              </div>

              {!isEditing ? (
                <Button onClick={() => setIsEditing(true)} className="w-full">
                  <Settings className="h-4 w-4 mr-2" />
                  Edit Profile
                </Button>
              ) : (
                <div className="flex gap-2">
                  <Button
                    onClick={handleSave}
                    disabled={isSaving}
                    className="flex-1"
                  >
                    {isSaving ? (
                      <>
                        <Clock className="h-4 w-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Changes
                      </>
                    )}
                  </Button>
                  <Button variant="outline" onClick={handleCancel} className="flex-1">
                    Cancel
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notification Preferences */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notification Preferences
              </CardTitle>
              <CardDescription>
                Choose how you want to be notified about updates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>Email Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications via email
                    </p>
                  </div>
                  <Checkbox
                    checked={formData.preferences.emailNotifications}
                    onCheckedChange={(checked: boolean) => handleInputChange('preferences.emailNotifications', checked)}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>Ticket Updates</Label>
                    <p className="text-sm text-muted-foreground">
                      Get notified when your tickets are updated
                    </p>
                  </div>
                  <Checkbox
                    checked={formData.preferences.ticketUpdates}
                    onCheckedChange={(checked: boolean) => handleInputChange('preferences.ticketUpdates', checked)}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>Knowledge Base Updates</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive updates about new articles and changes
                    </p>
                  </div>
                  <Checkbox
                    checked={formData.preferences.knowledgeBaseUpdates}
                    onCheckedChange={(checked: boolean) => handleInputChange('preferences.knowledgeBaseUpdates', checked)}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>Marketing Emails</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive promotional emails and product updates
                    </p>
                  </div>
                  <Checkbox
                    checked={formData.preferences.marketingEmails}
                    onCheckedChange={(checked: boolean) => handleInputChange('preferences.marketingEmails', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Security Settings */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security & Privacy
              </CardTitle>
              <CardDescription>
                Manage your account security settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4">
                <Button variant="outline" className="justify-start">
                  <Key className="h-4 w-4 mr-2" />
                  Change Password
                </Button>

                <Button variant="outline" className="justify-start">
                  <Shield className="h-4 w-4 mr-2" />
                  Two-Factor Authentication
                </Button>

                <Button variant="outline" className="justify-start">
                  <User className="h-4 w-4 mr-2" />
                  Download My Data
                </Button>
              </div>

              <Separator />

              <div className="bg-muted/50 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-sm">Account Security</h4>
                    <p className="text-sm text-muted-foreground">
                      Your account is protected with industry-standard encryption.
                      All data is securely stored and access is monitored.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}