import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, List, BookOpen, User, HelpCircle, Phone, ArrowRight } from "lucide-react";
import Link from "next/link";

export default function SelfServicePage() {
  const services = [
    {
      icon: Plus,
      title: "Submit a Ticket",
      description: "Create a new support ticket for your issues",
      action: "Create Ticket",
      buttonVariant: "secondary" as const,
      buttonIcon: Plus,
      href: "/self-service/submit-ticket",
    },
    {
      icon: List,
      title: "View My Tickets",
      description: "Check the status of your submitted tickets",
      action: "View Tickets",
      buttonVariant: "secondary" as const,
      buttonIcon: List,
      href: "/self-service/view-tickets",
    },
    {
      icon: BookOpen,
      title: "Knowledge Base",
      description: "Browse articles and solutions to common problems",
      action: "Browse Articles",
      buttonVariant: "secondary" as const,
      buttonIcon: BookOpen,
      href: "/self-service/knowledge-base",
    },
    {
      icon: User,
      title: "Profile Settings",
      description: "Update your account information and preferences",
      action: "Edit Profile",
      buttonVariant: "secondary" as const,
      buttonIcon: User,
      href: "/self-service/profile-settings",
    },
    {
      icon: HelpCircle,
      title: "FAQs",
      description: "Find answers to frequently asked questions",
      action: "View FAQs",
      buttonVariant: "secondary" as const,
      buttonIcon: HelpCircle,
      href: "/self-service/faq",
    },
    {
      icon: Phone,
      title: "Contact Support",
      description: "Get in touch with our support team directly",
      action: "Contact Us",
      buttonVariant: "secondary" as const,
      buttonIcon: Phone,
      href: "/self-service/contact-support",
    },
  ];

  return (
    <div className="space-y-8 px-6 md:px-8 lg:px-12 max-w-7xl mx-auto">
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
          Self Service Portal
        </h1>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          Access your support tools and manage your account with ease
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {services.map((service, index) => {
          const Icon = service.icon;
          return (
            <Link key={index} href={service.href} className="block">
              <Card className="group hover:shadow-lg hover:shadow-primary/5 transition-all duration-300 cursor-pointer border-2 hover:border-primary/20 bg-gradient-to-br from-card to-card/50 h-full flex flex-col">
                <CardHeader className="pb-4 flex-shrink-0">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="p-4 bg-primary/10 rounded-xl group-hover:bg-primary/20 transition-colors">
                        <Icon className="h-8 w-8 text-primary" />
                      </div>
                      <div className="flex-1">
                        <CardTitle className="text-xl font-semibold group-hover:text-primary transition-colors leading-tight">
                          {service.title}
                        </CardTitle>
                      </div>
                    </div>
                    <ArrowRight className="h-5 w-5 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all flex-shrink-0" />
                  </div>
                </CardHeader>
                <CardContent className="pt-0 flex-1 flex flex-col justify-between">
                  <CardDescription className="mb-6 text-base leading-relaxed flex-1">
                    {service.description}
                  </CardDescription>
                  <Button
                    variant={service.buttonVariant}
                    className="w-full group-hover:shadow-md transition-all duration-300 mt-auto"
                    size="lg"
                  >
                    <service.buttonIcon className="h-4 w-4" />
                    {service.action}
                  </Button>
                </CardContent>
              </Card>
            </Link>
          );
        })}
      </div>

      <div className="text-center">
        <p className="text-muted-foreground">
          Need help? Our support team is here to assist you 24/7
        </p>
      </div>
    </div>
  );
}