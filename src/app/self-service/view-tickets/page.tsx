'use client'

import React, { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  List,
  Eye,
  Calendar,
  User,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Pause,
  Play,
  Archive,
  Plus,
  ArrowLeft,
  Home
} from "lucide-react"
import { useGetTicketsByUserQuery } from "@/services/api/tickets"
import { TicketStatus, TicketPriority } from "@/lib/enums"
import Link from "next/link"
import { useAuth } from "@/hooks/useAuth"
import { useGetStatusesQuery } from "@/redux/slices/statuses"
import ZGrid from '@/shared/ZGrid'
import getTicketColumns from '@/containers/tickets/columns'
import { useRouter } from 'next/navigation'
import TicketsFilterDrawer from '@/components/ui/tickets-filter-drawer'


const PRIORITY_CONFIG = {
  'Urgent': { color: 'bg-red-100 text-red-800', label: 'Urgent' },
  'Top Priority': { color: 'bg-orange-100 text-orange-800', label: 'Top Priority' },
  'Normal': { color: 'bg-blue-100 text-blue-800', label: 'Normal' },
  'Low': { color: 'bg-gray-100 text-gray-800', label: 'Low' },
  'Later': { color: 'bg-slate-100 text-slate-800', label: 'Later' }
}

export default function ViewTicketsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [priorityFilter, setPriorityFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'createdAt' | 'updatedAt' | 'priority'>('updatedAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const router = useRouter()

  const { user } = useAuth()

  const { data: ticketsResponse, isLoading, error } = useGetTicketsByUserQuery({
    userId: user?._id || '',
    page: 1,
    size: 50
  })

  const { data: statuses = [] } = useGetStatusesQuery(user?.tenantId || '', { skip: !user?.tenantId })

  const getStatusConfig = (status: string) => {
    const statusObj = statuses.find(s => s.name === status)
    if (statusObj) {
      return {
        icon: AlertCircle,
        color: `bg-[${statusObj.color}] text-white`,
        label: statusObj.name
      }
    }
    return { icon: AlertCircle, color: 'bg-gray-100 text-gray-800', label: status }
  }

  const tickets = ticketsResponse?.data || []

  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = !searchQuery ||
      ticket.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.ticketKey.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.description.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesStatus = statusFilter === 'all' || ticket.status === statusFilter
    const matchesPriority = priorityFilter === 'all' || ticket.priority === priorityFilter

    return matchesSearch && matchesStatus && matchesPriority
  })

  const sortedTickets = [...filteredTickets].sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortBy) {
      case 'createdAt':
        aValue = new Date(a.createdAt)
        bValue = new Date(b.createdAt)
        break
      case 'updatedAt':
        aValue = new Date(a.lastActionAt || a.updatedAt)
        bValue = new Date(b.lastActionAt || b.updatedAt)
        break
      case 'priority':
        const priorityOrder = { 'Urgent': 5, 'Top Priority': 4, 'Normal': 3, 'Low': 2, 'Later': 1 }
        aValue = priorityOrder[a.priority as keyof typeof priorityOrder] || 0
        bValue = priorityOrder[b.priority as keyof typeof priorityOrder] || 0
        break
      default:
        return 0
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  // Get dynamic columns with status colors
  const ticketColumns = useMemo(() => getTicketColumns(statuses), [statuses]);

  // Map API shapes to grid rows
  const rows = useMemo(() => sortedTickets.map((t: any) => ({
    id: t._id,
    ticketKey: t.ticketKey,
    title: t.title || '-',
    subject: t.title || '-', // For backward compatibility
    status: t.status || '-',
    priority: t.priority || '-',
    requester: t.requester ? { name: t.requester.name } : { name: '-' },
    assignee: t.assigneeId ? { name: 'Assigned User' } : null, // You may need to fetch assignee details separately
    createdAt: t.createdAt || new Date().toISOString(),
  })), [sortedTickets]);

  return (
    <div className="space-y-8 px-6 md:px-8 lg:px-12 max-w-7xl mx-auto">
      {/* Navigation Header */}
      <div className="flex items-center justify-between">
        {/* <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/self-service">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Self Service
            </Link>
          </Button>
        </div> */}
        <Button variant="ghost" size="sm" asChild>
          <Link href="/self-service">
            <Home className="h-4 w-4 mr-2" />
            Home
          </Link>
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            My Tickets
          </h1>
          <p className="text-muted-foreground text-lg">
            View and manage all your support tickets
          </p>
        </div>
        <Button asChild>
          <Link href="/self-service/submit-ticket">
            <Plus className="h-4 w-4 mr-2" />
            New Ticket
          </Link>
        </Button>
      </div>

      {/* Filters */}
      <div className="flex justify-between items-center">
        <TicketsFilterDrawer
          status={statusFilter}
          priority={priorityFilter}
          search={searchQuery}
          sortBy={sortBy}
          sortOrder={sortOrder}
          pageSize={50}
          statuses={statuses.map(s => ({ label: s.name, value: s.name }))}
          onStatusChange={setStatusFilter}
          onPriorityChange={setPriorityFilter}
          onSearchChange={setSearchQuery}
          onSortChange={(value) => {
            const [field, order] = value.split('-')
            setSortBy(field as any)
            setSortOrder(order as any)
          }}
          onPageSizeChange={() => {}}
          onResetFilters={() => {
            setSearchQuery('')
            setStatusFilter('all')
            setPriorityFilter('all')
            setSortBy('updatedAt')
            setSortOrder('desc')
          }}
        />
        <div className="text-sm text-muted-foreground">
          {sortedTickets.length} tickets
        </div>
      </div>

      {/* Tickets Table */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-xl flex items-center gap-2">
            <List className="h-5 w-5" />
            Tickets ({sortedTickets.length})
          </CardTitle>
          <CardDescription>
            {statusFilter !== 'all' && `Filtered by ${getStatusConfig(statusFilter).label} status`}
            {priorityFilter !== 'all' && ` • ${priorityFilter} priority`}
            {searchQuery && ` • Search: "${searchQuery}"`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-full mb-2"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Error loading tickets</h3>
              <p className="text-muted-foreground">
                There was an error loading your tickets. Please try again later.
              </p>
            </div>
          ) : sortedTickets.length === 0 ? (
            <div className="text-center py-12">
              <List className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No tickets found</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery || statusFilter !== 'all' || priorityFilter !== 'all'
                  ? "Try adjusting your filters or search terms."
                  : "You haven't submitted any tickets yet."
                }
              </p>
              <Button asChild>
                <Link href="/self-service/submit-ticket">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Ticket
                </Link>
              </Button>
            </div>
          ) : (
            <div style={{ marginTop: '1rem' }}>
              <ZGrid
                columns={ticketColumns}
                data={rows}
                loading={isLoading}
                pagination={false} // client-side pagination
                selectable={false}
                showSearch={false}
                onRowClick={(row: any) => {
                  router.push(`/self-service/view-tickets/${row.ticketKey}`);
                }}
                pageSize={50}
                showFilters={false}
                filterPosition="header"
                sortIcons="arrows"
                // Zebra stripes with status-based colors
                rowClassName={(row, index) => {
                  const baseClass = index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                  return baseClass
                }}
                rowStyle={(row, index) => ({
                  borderLeft: `4px solid ${statuses.find(s => s.name === row.status)?.color || '#6b7280'}`
                })}
                headerClassName="bg-gray-50 text-black font-semibold"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Stats Cards */}
      {!isLoading && !error && tickets.length > 0 && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {statuses.slice(0, 4).map((status, index) => {
            const icons = [AlertCircle, Clock, CheckCircle, XCircle]
            const colors = ['text-blue-600', 'text-yellow-600', 'text-green-600', 'text-red-600']
            const IconComponent = icons[index] || AlertCircle
            const colorClass = colors[index] || 'text-gray-600'
            const count = tickets.filter(t => t.status === status.name).length

            return (
              <Card key={status._id}>
                <CardContent className="pt-6">
                  <div className="flex items-center gap-2">
                    <IconComponent className={`h-4 w-4 ${colorClass}`} />
                    <div>
                      <p className="text-2xl font-bold">{count}</p>
                      <p className="text-xs text-muted-foreground">{status.name}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}
    </div>
  )
}