'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  ArrowLeft,
  Home,
  Send,
  Paperclip,
  X,
  AlertCircle,
  Calendar,
  User,
  Clock,
  Reply,
  Edit2,
  Save,
  Eye
} from "lucide-react"
import { useGetTicketByIdQuery } from "@/services/api/tickets"
import { format } from "date-fns"
import Link from "next/link"
import { useAuth } from "@/hooks/useAuth"
import { useGetStatusesQuery } from "@/redux/slices/statuses"
import { useParams } from 'next/navigation'
import { useGetCommentsQuery, useCreateCommentMutation, useUpdateCommentMutation, useDeleteCommentMutation } from "@/services/api/comments"

const PRIORITY_CONFIG = {
  'Urgent': { color: 'bg-red-100 text-red-800', label: 'Urgent' },
  'Top Priority': { color: 'bg-orange-100 text-orange-800', label: 'Top Priority' },
  'Normal': { color: 'bg-blue-100 text-blue-800', label: 'Normal' },
  'Low': { color: 'bg-gray-100 text-gray-800', label: 'Low' },
  'Later': { color: 'bg-slate-100 text-slate-800', label: 'Later' }
}

export default function TicketDetailsPage() {
  const params = useParams()
  const ticketId = params.id as string
  const { user, tenant } = useAuth()
  const [newComment, setNewComment] = useState('')
  const [commentAttachments, setCommentAttachments] = useState<File[]>([])
  const [errorMessage, setErrorMessage] = useState('')
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null)
  const [editingBody, setEditingBody] = useState('')
  const [editingAttachments, setEditingAttachments] = useState<File[]>([])
  const [replyingToCommentId, setReplyingToCommentId] = useState<string | null>(null)
  const [replyBody, setReplyBody] = useState('')
  const [replyAttachments, setReplyAttachments] = useState<File[]>([])

  // Auto-dismiss error messages after 5 seconds if longer than 5 characters
  useEffect(() => {
    if (errorMessage && errorMessage.length > 5) {
      const timer = setTimeout(() => {
        setErrorMessage('')
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [errorMessage])

  const { data: ticket, isLoading, error } = useGetTicketByIdQuery(ticketId)
  const { data: statuses = [] } = useGetStatusesQuery(tenant?.tenantId || '', { skip: !tenant?.tenantId })
  const { data: commentsResponse, refetch: refetchComments } = useGetCommentsQuery({
    ticketId: ticket?._id || ticketId,
    params: { visibility: 'public', includeReplies: true }
  }, { skip: !ticket && !ticketId })
  const [createComment, { isLoading: isCreatingComment }] = useCreateCommentMutation()
  const [updateComment, { isLoading: isUpdatingComment }] = useUpdateCommentMutation()
  const [deleteComment, { isLoading: isDeletingComment }] = useDeleteCommentMutation()

  const getStatusConfig = (status: string) => {
    const statusObj = statuses.find(s => s.name === status)
    if (statusObj) {
      return {
        icon: AlertCircle,
        color: statusObj.color,
        label: statusObj.name
      }
    }
    return { icon: AlertCircle, color: '#6b7280', label: status }
  }

  const getPriorityConfig = (priority: string) => {
    return PRIORITY_CONFIG[priority as keyof typeof PRIORITY_CONFIG] || PRIORITY_CONFIG.Normal
  }

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm')
    } catch {
      return dateString
    }
  }

  const handleAddComment = async () => {
    if ((!newComment.trim() && commentAttachments.length === 0) || !user || !ticket) return

    // Validate minimum comment length when attachments are present
    if (commentAttachments.length > 0 && newComment.trim().length < 5) {
      setErrorMessage('Please enter at least 5 characters when adding attachments.')
      return
    }

    try {
      if (commentAttachments.length > 0) {
        const formData = new FormData()
        formData.append('body', newComment.trim())
        formData.append('visibility', 'public')
        formData.append('entityType', '0')
        formData.append('ticketOrSubticketId', ticket._id || ticketId)

        commentAttachments.forEach((file) => {
          formData.append('attachments', file)
        })

        await createComment({
          ticketId: ticket._id,
          data: formData as any,
        }).unwrap()
      } else {
        const commentData = {
          body: newComment.trim(),
          visibility: 'public' as const,
          entityType: '0',
          ticketOrSubticketId: ticket._id,
        }

        await createComment({
          ticketId: ticket._id,
          data: commentData,
        }).unwrap()
      }

      setNewComment('')
      setCommentAttachments([])
      setErrorMessage('')
      // Refetch comments to ensure the list is updated
      refetchComments()
    } catch (error) {
      console.error('Failed to add comment:', error)
      setErrorMessage('Failed to add comment. Please try again.')
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    const validFiles = files.filter(file => file.size <= 5 * 1024 * 1024)
    if (validFiles.length !== files.length) {
      setErrorMessage('Some files were too large. Maximum file size is 5MB.')
    }
    setCommentAttachments(prev => [...prev, ...validFiles].slice(0, 3))
    e.target.value = ''
  }

  const handleRemoveAttachment = (index: number) => {
    setCommentAttachments(prev => prev.filter((_, i) => i !== index))
  }

  // Organize comments hierarchically
  const organizeComments = (comments: any[]): any[] => {
    const commentMap = new Map<string, any>()
    const rootComments: any[] = []

    // First pass: create comment map
    comments.forEach(comment => {
      commentMap.set(comment._id, { ...comment, replies: [] })
    })

    // Second pass: organize into hierarchy
    comments.forEach(comment => {
      if (comment.parentCommentId) {
        const parent = commentMap.get(comment.parentCommentId)
        if (parent) {
          parent.replies = parent.replies || []
          parent.replies.push(commentMap.get(comment._id)!)
        }
      } else {
        rootComments.push(commentMap.get(comment._id)!)
      }
    })

    return rootComments
  }

  // Edit handlers
  const handleEditComment = (commentId: string, currentBody: string) => {
    setEditingCommentId(commentId)
    setEditingBody(currentBody)
    setEditingAttachments([])
  }

  const handleSaveEdit = async () => {
    if (!editingCommentId || !editingBody.trim() || !ticket) return

    try {
      if (editingAttachments.length > 0) {
        const formData = new FormData()
        formData.append('body', editingBody.trim())

        editingAttachments.forEach((file) => {
          formData.append('attachments', file)
        })

        await updateComment({
          commentId: editingCommentId,
          ticketId: ticket._id,
          data: formData as any,
        }).unwrap()
      } else {
        await updateComment({
          commentId: editingCommentId,
          ticketId: ticket._id,
          data: { body: editingBody.trim() },
        }).unwrap()
      }

      setEditingCommentId(null)
      setEditingBody('')
      setEditingAttachments([])
      refetchComments()
    } catch (error) {
      console.error('Failed to update comment:', error)
      setErrorMessage('Failed to update comment. Please try again.')
    }
  }

  const handleCancelEdit = () => {
    setEditingCommentId(null)
    setEditingBody('')
    setEditingAttachments([])
  }

  // Reply handlers
  const handleReplyToComment = (commentId: string) => {
    setReplyingToCommentId(commentId)
    setReplyBody('')
    setReplyAttachments([])
  }

  const handleCancelReply = () => {
    setReplyingToCommentId(null)
    setReplyBody('')
    setReplyAttachments([])
  }

  const handleSubmitReply = async (parentCommentId: string) => {
    if (!ticket || !replyBody.trim()) return

    try {
      if (replyAttachments.length > 0) {
        const formData = new FormData()
        formData.append('body', replyBody.trim())
        formData.append('visibility', 'public')
        formData.append('parentCommentId', parentCommentId)
        formData.append('entityType', '0')
        formData.append('ticketOrSubticketId', ticket._id)

        replyAttachments.forEach((file) => {
          formData.append('attachments', file)
        })

        await createComment({
          ticketId: ticket._id,
          data: formData as any,
        }).unwrap()
      } else {
        const replyData = {
          body: replyBody.trim(),
          visibility: 'public' as const,
          parentCommentId,
          entityType: '0',
          ticketOrSubticketId: ticket._id,
        }

        await createComment({
          ticketId: ticket._id,
          data: replyData,
        }).unwrap()
      }

      setReplyingToCommentId(null)
      setReplyBody('')
      setReplyAttachments([])
      refetchComments()
    } catch (error) {
      console.error('Failed to add reply:', error)
      setErrorMessage('Failed to add reply. Please try again.')
    }
  }

  // Delete handler
  const handleDeleteComment = async (commentId: string) => {
    if (!ticket || !confirm('Are you sure you want to delete this comment?')) return

    try {
      await deleteComment({
        commentId,
        ticketId: ticket._id
      }).unwrap()
      refetchComments()
    } catch (error) {
      console.error('Failed to delete comment:', error)
      setErrorMessage('Failed to delete comment. Please try again.')
    }
  }

  // Additional attachment handlers
  const handleEditFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setEditingAttachments(prev => [...prev, ...files])
    e.target.value = ''
  }

  const handleRemoveEditAttachment = (index: number) => {
    setEditingAttachments(prev => prev.filter((_, i) => i !== index))
  }

  const handleReplyFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setReplyAttachments(prev => [...prev, ...files])
    e.target.value = ''
  }

  const handleRemoveReplyAttachment = (index: number) => {
    setReplyAttachments(prev => prev.filter((_, i) => i !== index))
  }

  if (isLoading) {
    return (
      <div className="space-y-8 px-6 md:px-8 lg:px-12 max-w-4xl mx-auto">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="h-4 bg-muted rounded w-full"></div>
          <div className="h-4 bg-muted rounded w-2/3"></div>
        </div>
      </div>
    )
  }

  if (error || !ticket) {
    return (
      <div className="space-y-8 px-6 md:px-8 lg:px-12 max-w-4xl mx-auto">
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">Ticket not found</h3>
          <p className="text-muted-foreground">
            The ticket you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Button asChild className="mt-4">
            <Link href="/self-service/view-tickets">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Tickets
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  const statusConfig = getStatusConfig(ticket.status)
  const priorityConfig = getPriorityConfig(ticket.priority)
  const StatusIcon = statusConfig.icon

  const comments = commentsResponse?.data || []
  const organizedComments = organizeComments(comments)

  // Render comment with hierarchical display
  const renderComment = (comment: any, depth = 0) => {
    const isEditing = editingCommentId === comment._id
    const isReplying = replyingToCommentId === comment._id
    const maxDepth = 3 // Limit nesting depth

    return (
      <div key={comment._id} className={`${depth > 0 ? 'ml-8 border-l-2 border-muted-foreground/40 pl-6' : ''}`}>
        <div className="group py-3 hover:bg-muted/30 transition-colors rounded-lg px-2">
          {/* Comment Header */}
          <div className="flex items-start space-x-3 mb-2">
            <Avatar className="h-8 w-8 flex-shrink-0">
              <AvatarImage src={comment.author?.avatar} />
              <AvatarFallback className="text-xs">
                {comment.author?.name?.split(' ').map((n: string) => n[0]).join('') || 'U'}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <span className="font-medium text-sm text-foreground">
                  {comment.author?.name || 'Unknown'}
                </span>
                <span className="text-xs text-muted-foreground">
                  {formatDate(comment.createdAt)}
                </span>
                {comment.isEdited && (
                  <span className="text-xs text-muted-foreground">(edited)</span>
                )}
              </div>
            </div>

            {/* Comment Actions */}
            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {depth < maxDepth && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 p-0 text-muted-foreground hover:text-foreground"
                  onClick={() => handleReplyToComment(comment._id)}
                >
                  <Reply className="h-4 w-4" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0 text-muted-foreground hover:text-foreground"
                onClick={() => handleEditComment(comment._id, comment.body)}
              >
                <Edit2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0 text-destructive/70 hover:text-destructive"
                onClick={() => handleDeleteComment(comment._id)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Comment Body */}
          <div className="ml-11">
            {isEditing ? (
              <div className="space-y-3 bg-muted/30 p-3 rounded-lg border">
                <Textarea
                  value={editingBody}
                  onChange={(e) => setEditingBody(e.target.value)}
                  className="min-h-[80px] resize-none border-0 bg-transparent focus:ring-0"
                  placeholder="Edit your comment..."
                />

                {/* Edit Attachments */}
                <div className="flex items-center space-x-3 pt-2 border-t border-border/30">
                  <div className="relative">
                    <input
                      type="file"
                      id={`edit-file-upload-${comment._id}`}
                      className="hidden"
                      multiple
                      accept="image/*,.pdf,.doc,.docx"
                      onChange={handleEditFileSelect}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-8 px-3 text-xs text-muted-foreground hover:text-foreground hover:bg-muted/50"
                      onClick={() => document.getElementById(`edit-file-upload-${comment._id}`)?.click()}
                    >
                      <Paperclip className="h-3 w-3 mr-1" />
                      Add Files
                    </Button>
                  </div>

                  {editingAttachments.length > 0 && (
                    <span className="text-xs text-muted-foreground">
                      {editingAttachments.length} file{editingAttachments.length > 1 ? 's' : ''} selected
                    </span>
                  )}
                </div>

                {/* Edit Attachment Preview */}
                {editingAttachments.length > 0 && (
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Paperclip className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">
                        New Attachments ({editingAttachments.length})
                      </span>
                    </div>
                    <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                      {editingAttachments.map((file, index) => (
                        <div key={index} className="relative group">
                          {file.type.startsWith('image/') ? (
                            <div className="aspect-video relative overflow-hidden rounded-md border bg-muted max-h-16">
                              <img
                                src={URL.createObjectURL(file)}
                                alt={file.name}
                                className="w-full h-full object-cover"
                              />
                            </div>
                          ) : (
                            <div className="aspect-video relative overflow-hidden rounded-md border bg-muted flex items-center justify-center max-h-16">
                              <div className="text-center">
                                <div className="w-6 h-6 bg-muted-foreground/20 rounded mx-auto mb-1 flex items-center justify-center">
                                  <span className="text-xs font-medium text-muted-foreground">
                                    {file.name.split('.').pop()?.toUpperCase()}
                                  </span>
                                </div>
                                <p className="text-xs text-muted-foreground truncate px-1">
                                  {file.name}
                                </p>
                              </div>
                            </div>
                          )}
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => handleRemoveEditAttachment(index)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancelEdit}
                    disabled={isUpdatingComment}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSaveEdit}
                    disabled={isUpdatingComment || !editingBody.trim()}
                  >
                    {isUpdatingComment ? "Saving..." : "Save"}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="whitespace-pre-wrap text-sm text-foreground leading-relaxed">
                {comment.body}
              </div>
            )}

            {/* Comment Attachments - Image Preview */}
            {comment.attachments && comment.attachments.length > 0 && (
              <div className="mt-3">
                <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                  {comment.attachments.map((attachment: any, index: number) => (
                    <div
                      key={attachment._id}
                      className="relative group cursor-pointer"
                      onClick={() => {
                        // Open image viewer if available
                        if ((window as any).openImageViewer) {
                          (window as any).openImageViewer(comment.attachments, index)
                        }
                      }}
                    >
                      <div className="aspect-video relative overflow-hidden rounded-md border bg-muted max-h-16">
                        <img
                          src={attachment.url}
                          alt={attachment.filename}
                          className="w-full h-full object-cover transition-transform group-hover:scale-105"
                          loading="lazy"
                        />
                      </div>
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors rounded-md flex items-center justify-center">
                        <Eye className="h-3 w-3 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Reply Form */}
          {isReplying && (
            <div className="ml-11 mt-3 space-y-3 bg-muted/20 p-3 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Reply to {comment.author?.name}</span>
              </div>

              <Textarea
                value={replyBody}
                onChange={(e) => setReplyBody(e.target.value)}
                className="min-h-[80px] resize-none border-0 bg-background/50 focus:ring-0"
                placeholder="Write your reply..."
              />

              {/* Reply Attachments */}
              <div className="flex items-center space-x-3 pt-2 border-t border-border/30">
                <div className="relative">
                  <input
                    type="file"
                    id={`reply-file-upload-${comment._id}`}
                    className="hidden"
                    multiple
                    accept="image/*,.pdf,.doc,.docx"
                    onChange={handleReplyFileSelect}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-7 px-2 text-xs text-muted-foreground hover:text-foreground hover:bg-muted/50"
                    onClick={() => document.getElementById(`reply-file-upload-${comment._id}`)?.click()}
                  >
                    <Paperclip className="h-3 w-3 mr-1" />
                    Attach
                  </Button>
                </div>

                {replyAttachments.length > 0 && (
                  <span className="text-xs text-muted-foreground">
                    {replyAttachments.length} file{replyAttachments.length > 1 ? 's' : ''} selected
                  </span>
                )}
              </div>

              {/* Reply Attachment Preview */}
              {replyAttachments.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Paperclip className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium text-muted-foreground">
                      Attachments ({replyAttachments.length})
                    </span>
                  </div>
                  <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                    {replyAttachments.map((file, index) => (
                      <div key={index} className="relative group">
                        {file.type.startsWith('image/') ? (
                          <div className="aspect-video relative overflow-hidden rounded-md border bg-muted max-h-16">
                            <img
                              src={URL.createObjectURL(file)}
                              alt={file.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ) : (
                          <div className="aspect-video relative overflow-hidden rounded-md border bg-muted flex items-center justify-center max-h-16">
                            <div className="text-center">
                              <div className="w-6 h-6 bg-muted-foreground/20 rounded mx-auto mb-1 flex items-center justify-center">
                                <span className="text-xs font-medium text-muted-foreground">
                                  {file.name.split('.').pop()?.toUpperCase()}
                                </span>
                              </div>
                              <p className="text-xs text-muted-foreground truncate px-1">
                                {file.name}
                              </p>
                            </div>
                          </div>
                        )}
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => handleRemoveReplyAttachment(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancelReply}
                  disabled={isCreatingComment}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={() => handleSubmitReply(comment._id)}
                  disabled={isCreatingComment || (!replyBody.trim() && replyAttachments.length === 0)}
                >
                  {isCreatingComment ? "Sending..." : "Reply"}
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Render Replies */}
        {comment.replies && comment.replies.length > 0 && depth < maxDepth && (
          <div className="mt-2 space-y-2">
            {comment.replies.map((reply: any) => renderComment(reply, depth + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-8 px-6 md:px-8 lg:px-12 max-w-4xl mx-auto">
      {/* Navigation Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/self-service/view-tickets">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Tickets
          </Link>
        </Button>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/self-service">
            <Home className="h-4 w-4 mr-2" />
            Home
          </Link>
        </Button>
      </div>

      {/* Error Message */}
      {errorMessage && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-center gap-2">
          <AlertCircle className="h-4 w-4" />
          <span>{errorMessage}</span>
        </div>
      )}

      {/* Ticket Header */}
      <Card className="shadow-lg">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h1 className="text-2xl font-bold">{ticket.title}</h1>
                <Badge className="border font-semibold px-3 py-1" style={{ borderColor: statusConfig.color, color: statusConfig.color, backgroundColor: 'transparent' }}>
                  <StatusIcon className="h-4 w-4 mr-1" style={{ color: statusConfig.color }} />
                  {statusConfig.label}
                </Badge>
                <Badge className={priorityConfig.color}>
                  {priorityConfig.label}
                </Badge>
              </div>
              <p className="text-muted-foreground font-mono">Ticket ID: {ticket.ticketKey}</p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <User className="h-4 w-4" />
                <span className="font-medium">Requester:</span>
                <span>{ticket.requester?.name || 'Unknown'}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4" />
                <span className="font-medium">Created:</span>
                <span>{formatDate(ticket.createdAt)}</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4" />
                <span className="font-medium">Last Updated:</span>
                <span>{formatDate(ticket.lastActionAt || ticket.updatedAt)}</span>
              </div>
              {ticket.assigneeId && (
                <div className="flex items-center gap-2 text-sm">
                  <User className="h-4 w-4" />
                  <span className="font-medium">Assigned to:</span>
                  <span>{ticket.assigneeId}</span>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Ticket Description */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>Description</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="prose max-w-none">
            <p className="whitespace-pre-wrap">{ticket.description}</p>
          </div>
        </CardContent>
      </Card>

      {/* Comments Section */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>Comments</CardTitle>
          <CardDescription>
            Public comments on this ticket
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Existing comments */}
          {organizedComments.length > 0 ? (
            organizedComments.map((comment) => renderComment(comment))
          ) : (
            <p className="text-muted-foreground text-center py-4">
              No comments yet
            </p>
          )}

          {/* Add Comment */}
          <div className="border-t pt-4">
            <div className="space-y-3">
              <Textarea
                placeholder="Add a public comment..."
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                rows={3}
              />

              {/* Attachments */}
              <div className="flex items-center gap-2">
                <input
                  type="file"
                  id="comment-attachments"
                  className="hidden"
                  multiple
                  accept="image/*,.pdf,.doc,.docx"
                  onChange={handleFileSelect}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById('comment-attachments')?.click()}
                >
                  <Paperclip className="h-4 w-4 mr-2" />
                  Attach Files
                </Button>
                {commentAttachments.length > 0 && (
                  <span className="text-sm text-muted-foreground">
                    {commentAttachments.length} file{commentAttachments.length > 1 ? 's' : ''} selected
                  </span>
                )}
              </div>

              {/* Attachment Preview */}
              {commentAttachments.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Paperclip className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium text-muted-foreground">
                      Attachments ({commentAttachments.length})
                    </span>
                  </div>
                  <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                    {commentAttachments.map((file, index) => (
                      <div key={index} className="relative group">
                        {file.type.startsWith('image/') ? (
                          <div className="aspect-video relative overflow-hidden rounded-md border bg-muted max-h-16">
                            <img
                              src={URL.createObjectURL(file)}
                              alt={file.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ) : (
                          <div className="aspect-video relative overflow-hidden rounded-md border bg-muted flex items-center justify-center max-h-16">
                            <div className="text-center">
                              <div className="w-6 h-6 bg-muted-foreground/20 rounded mx-auto mb-1 flex items-center justify-center">
                                <span className="text-xs font-medium text-muted-foreground">
                                  {file.name.split('.').pop()?.toUpperCase()}
                                </span>
                              </div>
                              <p className="text-xs text-muted-foreground truncate px-1">
                                {file.name}
                              </p>
                            </div>
                          </div>
                        )}
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => handleRemoveAttachment(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <Button
                onClick={handleAddComment}
                disabled={(!newComment.trim() && commentAttachments.length === 0) || isCreatingComment}
                className="w-full sm:w-auto"
              >
                <Send className="h-4 w-4 mr-2" />
                {isCreatingComment ? 'Sending...' : 'Add Comment'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}