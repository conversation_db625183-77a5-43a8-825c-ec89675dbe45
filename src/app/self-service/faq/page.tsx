'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Separator } from "@/components/ui/separator"
import {
  Search,
  HelpCircle,
  ChevronDown,
  ChevronRight,
  MessageSquare,
  Phone,
  Mail,
  BookOpen,
  Users,
  Settings,
  Shield,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  ArrowLeft,
  Home
} from "lucide-react"
import Link from "next/link"

const FAQ_CATEGORIES = [
  {
    id: 'getting-started',
    title: 'Getting Started',
    icon: Users,
    color: 'bg-blue-100 text-blue-800',
    questions: [
      {
        id: 'what-is-tickflo',
        question: 'What is Tick<PERSON><PERSON>?',
        answer: 'Tick<PERSON>lo is a comprehensive multi-tenant ticket management system designed to help organizations efficiently manage customer support tickets, track issues, and provide excellent customer service. It supports multiple tenants (organizations) with isolated data and customizable workflows.'
      },
      {
        id: 'how-to-create-account',
        question: 'How do I create an account?',
        answer: 'To create an account, contact your organization administrator. They will provide you with login credentials and set up your access level based on your role within the organization. If you\'re an administrator, you can create accounts through the admin dashboard.'
      },
      {
        id: 'tenant-setup',
        question: 'How does multi-tenant support work?',
        answer: 'Each organization (tenant) has its own isolated environment with separate data, users, and configurations. This ensures complete privacy and security between different organizations using the same TickFlo instance.'
      }
    ]
  },
  {
    id: 'tickets',
    title: 'Ticket Management',
    icon: MessageSquare,
    color: 'bg-green-100 text-green-800',
    questions: [
      {
        id: 'create-ticket',
        question: 'How do I create a new ticket?',
        answer: 'You can create a ticket through the Self Service portal by clicking "Submit a Ticket". Fill in the required details including title, description, priority level, and any relevant tags. You can also attach files if needed.'
      },
      {
        id: 'ticket-priority',
        question: 'What are the ticket priority levels?',
        answer: 'Tickets can be set to: Urgent (critical issues needing immediate attention), Top Priority (high priority issues), Normal (standard priority), Low (minor issues), and Later (issues that can be addressed at a later time).'
      },
      {
        id: 'track-ticket',
        question: 'How can I track my ticket status?',
        answer: 'You can view all your tickets in the "View My Tickets" section. Each ticket shows its current status, priority, assignee, and last update. You\'ll also receive email notifications for status changes.'
      },
      {
        id: 'ticket-statuses',
        question: 'What do the different ticket statuses mean?',
        answer: 'Open: New ticket awaiting assignment • In Progress: Being worked on • Blocked: Waiting for external input • On Hold: Temporarily paused • Resolved: Issue fixed • Closed: Final status • Archived: Old ticket stored for reference.'
      }
    ]
  },
  {
    id: 'features',
    title: 'Features & Functionality',
    icon: Settings,
    color: 'bg-purple-100 text-purple-800',
    questions: [
      {
        id: 'knowledge-base',
        question: 'How do I use the Knowledge Base?',
        answer: 'The Knowledge Base contains articles, guides, and solutions to common issues. You can search by keywords, filter by category, and browse articles. Helpful articles are marked and you can vote on their usefulness.'
      },
      {
        id: 'merge-tickets',
        question: 'What is ticket merging?',
        answer: 'Ticket merging allows support agents to combine related tickets into one for better organization. This is useful when multiple reports are about the same issue or when follow-up tickets are created for the same problem.'
      },
      {
        id: 'attachments',
        question: 'Can I attach files to tickets?',
        answer: 'Yes, you can attach various file types to tickets including images, documents, and logs. File size limits and supported formats may vary based on your organization\'s configuration.'
      },
      {
        id: 'notifications',
        question: 'How do notifications work?',
        answer: 'You\'ll receive email notifications for ticket updates, assignments, and status changes. You can configure your notification preferences in your profile settings to control what you\'re notified about.'
      }
    ]
  },
  {
    id: 'security',
    title: 'Security & Privacy',
    icon: Shield,
    color: 'bg-red-100 text-red-800',
    questions: [
      {
        id: 'data-security',
        question: 'How is my data protected?',
        answer: 'TickFlo uses industry-standard encryption for data at rest and in transit. Each tenant\'s data is completely isolated, and access controls ensure users can only see data they\'re authorized to view.'
      },
      {
        id: 'gdpr-compliance',
        question: 'Is TickFlo GDPR compliant?',
        answer: 'Yes, TickFlo is designed with GDPR compliance in mind. We provide data export capabilities, audit logs, and proper data handling procedures to ensure compliance with privacy regulations.'
      },
      {
        id: 'access-controls',
        question: 'What access controls are in place?',
        answer: 'Role-based access control (RBAC) ensures users only have access to features and data appropriate for their role. Administrators can configure permissions at both tenant and user levels.'
      }
    ]
  },
  {
    id: 'billing',
    title: 'Billing & Support',
    icon: CreditCard,
    color: 'bg-yellow-100 text-yellow-800',
    questions: [
      {
        id: 'support-hours',
        question: 'What are your support hours?',
        answer: 'Our support team is available Monday through Friday, 9 AM to 6 PM EST. Email support is available 24/7, and critical issues receive immediate attention outside normal hours.'
      },
      {
        id: 'response-times',
        question: 'What are your response times?',
        answer: 'Urgent issues: Within 2 hours • High priority: Within 4 hours • Normal priority: Within 24 hours • Low priority: Within 48 hours. Response times may vary during peak periods.'
      },
      {
        id: 'contact-support',
        question: 'How do I contact support?',
        answer: 'You can reach us through phone (******-123-4567), email (<EMAIL>), or the live chat feature in your dashboard. For urgent issues, please call us directly.'
      }
    ]
  }
]

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const filteredCategories = FAQ_CATEGORIES.filter(category => {
    if (selectedCategory !== 'all' && category.id !== selectedCategory) return false

    if (!searchQuery) return true

    return category.questions.some(q =>
      q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      q.answer.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })

  const filteredQuestions = FAQ_CATEGORIES.flatMap(category =>
    category.questions.filter(q => {
      if (selectedCategory !== 'all' && category.id !== selectedCategory) return false
      if (!searchQuery) return true
      return q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
             q.answer.toLowerCase().includes(searchQuery.toLowerCase())
    })
  )

  const handleCategoryClick = (categoryId: string) => {
    setSelectedCategory(categoryId === selectedCategory ? 'all' : categoryId)
  }

  const toggleAccordion = (itemId: string) => {
    setExpandedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  return (
    <div className="space-y-8 px-6 md:px-8 lg:px-12 max-w-7xl mx-auto">
      {/* Navigation Header */}
      <div className="flex items-center justify-between">
        {/* <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/self-service">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Self Service
            </Link>
          </Button>
        </div> */}
        <Button variant="ghost" size="sm" asChild>
          <Link href="/self-service">
            <Home className="h-4 w-4 mr-2" />
            Home
          </Link>
        </Button>
      </div>

      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
          Frequently Asked Questions
        </h1>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          Find answers to common questions about TickFlo
        </p>
      </div>

      {/* Search and Categories */}
      <Card className="shadow-lg">
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search FAQs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 text-base"
              />
            </div>

            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory('all')}
              >
                All Categories
              </Button>
              {FAQ_CATEGORIES.map((category) => {
                const Icon = category.icon
                return (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleCategoryClick(category.id)}
                    className="flex items-center gap-2"
                  >
                    <Icon className="h-4 w-4" />
                    {category.title}
                  </Button>
                )
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* FAQ Content */}
      {searchQuery ? (
        // Search Results View
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold">
              Search Results ({filteredQuestions.length})
            </h2>
          </div>

          <Accordion type="multiple" value={expandedItems} onValueChange={setExpandedItems} className="space-y-4">
            {filteredQuestions.map((question) => {
              const category = FAQ_CATEGORIES.find(cat =>
                cat.questions.some(q => q.id === question.id)
              )
              const Icon = category?.icon

              return (
                <Card key={question.id} className="shadow-sm">
                  <AccordionItem value={question.id} className="border-none">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                      <div className="flex items-start gap-3 text-left">
                        {Icon && (
                          <div className={`p-2 rounded-lg ${category?.color} flex-shrink-0 mt-1`}>
                            <Icon className="h-4 w-4" />
                          </div>
                        )}
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold text-lg">{question.question}</h3>
                            <Badge variant="outline" className="text-xs">
                              {category?.title}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-4">
                      <div className="ml-11">
                        <p className="text-muted-foreground leading-relaxed">
                          {question.answer}
                        </p>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Card>
              )
            })}
          </Accordion>
        </div>
      ) : (
        // Category View
        <div className="space-y-8">
          {filteredCategories.map((category) => {
            const Icon = category.icon
            return (
              <Card key={category.id} className="shadow-lg">
                <CardHeader>
                  <CardTitle className="text-2xl flex items-center gap-3">
                    <div className={`p-3 rounded-xl ${category.color}`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    {category.title}
                    <Badge variant="secondary" className="ml-auto">
                      {category.questions.length} questions
                    </Badge>
                  </CardTitle>
                  <CardDescription>
                    {category.id === 'getting-started' && 'Learn the basics of using TickFlo'}
                    {category.id === 'tickets' && 'Everything about creating and managing tickets'}
                    {category.id === 'features' && 'Explore TickFlo\'s powerful features'}
                    {category.id === 'security' && 'Your data security and privacy'}
                    {category.id === 'billing' && 'Support hours and contact information'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Accordion type="multiple" value={expandedItems} onValueChange={setExpandedItems} className="space-y-2">
                    {category.questions.map((question) => (
                      <AccordionItem key={question.id} value={question.id} className="border rounded-lg px-4">
                        <AccordionTrigger className="hover:no-underline py-4">
                          <span className="font-medium text-left">{question.question}</span>
                        </AccordionTrigger>
                        <AccordionContent className="pb-4">
                          <p className="text-muted-foreground leading-relaxed">
                            {question.answer}
                          </p>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}

      {/* Contact Support CTA */}
      <Card className="shadow-lg bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <HelpCircle className="h-12 w-12 text-primary mx-auto" />
            <div>
              <h3 className="text-xl font-semibold mb-2">Still need help?</h3>
              <p className="text-muted-foreground mb-4">
                Can't find the answer you're looking for? Our support team is here to help.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild>
                  <a href="/self-service/contact-support">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Contact Support
                  </a>
                </Button>
                <Button variant="outline" asChild>
                  <a href="/self-service/submit-ticket">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Submit a Ticket
                  </a>
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}