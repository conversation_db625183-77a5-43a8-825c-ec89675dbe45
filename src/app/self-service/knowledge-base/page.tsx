'use client'

import React, { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Search,
  BookOpen,
  Calendar,
  User,
  Eye,
  ThumbsUp,
  Tag,
  Filter,
  Clock,
  ArrowRight,
  FileText,
  ArrowLeft,
  Home
} from "lucide-react"
import {
  useGetKnowledgeArticlesQuery,
  useGetKnowledgeCategoriesQuery,
  useSearchKnowledgeArticlesQuery
} from "@/services/api/knowledge"
import { KnowledgeArticle } from "@/types"
import { toast } from "sonner"
import Link from "next/link"

export default function KnowledgeBasePage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedArticle, setSelectedArticle] = useState<KnowledgeArticle | null>(null)
  const [sortBy, setSortBy] = useState<'createdAt' | 'updatedAt' | 'title' | 'views' | 'helpful'>('updatedAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // Fetch categories
  const { data: categoriesData } = useGetKnowledgeCategoriesQuery()

  // Fetch articles based on search or regular listing
  const {
    data: articlesData,
    isLoading: articlesLoading,
    error: articlesError
  } = useGetKnowledgeArticlesQuery(
    searchQuery ? {
      search: searchQuery,
      category: selectedCategory !== 'all' ? selectedCategory : undefined,
      limit: 50
    } : {
      category: selectedCategory !== 'all' ? selectedCategory : undefined,
      status: 'published',
      sortBy,
      sortOrder,
      limit: 50
    },
    {
      skip: false
    }
  )

  const articles = articlesData?.data?.articles || []
  const categories = categoriesData?.data || []

  // Filter articles based on search if using search API
  const filteredArticles = useMemo(() => {
    if (!searchQuery) return articles

    return articles.filter(article =>
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    )
  }, [articles, searchQuery])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
  }

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category)
    setSearchQuery('') // Clear search when changing category
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getHelpfulPercentage = (helpful: number, notHelpful: number) => {
    const total = helpful + notHelpful
    return total > 0 ? Math.round((helpful / total) * 100) : 0
  }

  if (selectedArticle) {
    return (
      <div className="space-y-8 px-6 md:px-8 lg:px-12 max-w-4xl mx-auto">
        {/* Navigation Header */}
        <div className="flex items-center justify-between">
          {/* <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => setSelectedArticle(null)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Knowledge Base
            </Button>
          </div> */}
          <Button variant="ghost" size="sm" asChild>
            <Link href="/self-service">
              <Home className="h-4 w-4 mr-2" />
              Home
            </Link>
          </Button>
        </div>

        <Card className="shadow-lg">
          <CardHeader className="space-y-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <CardTitle className="text-2xl font-bold mb-2 leading-tight">
                  {selectedArticle.title}
                </CardTitle>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    <span>{selectedArticle.author.name}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(selectedArticle.createdAt)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    <span>{selectedArticle.views} views</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <ThumbsUp className="h-4 w-4" />
                    <span>{getHelpfulPercentage(selectedArticle.helpful, selectedArticle.notHelpful)}% helpful</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{selectedArticle.category}</Badge>
                {selectedArticle.isPinned && (
                  <Badge variant="outline">📌 Pinned</Badge>
                )}
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            <ScrollArea className="h-[60vh] pr-4">
              <div className="prose max-w-none">
                <div className="whitespace-pre-wrap text-base leading-relaxed">
                  {selectedArticle.content}
                </div>
              </div>

              {selectedArticle.tags.length > 0 && (
                <div className="mt-6">
                  <Separator className="mb-3" />
                  <div className="flex items-center gap-2 flex-wrap">
                    <Tag className="h-4 w-4 text-muted-foreground" />
                    {selectedArticle.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </ScrollArea>

            <Separator />

            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>Last updated: {formatDate(selectedArticle.updatedAt)}</span>
              <div className="flex items-center gap-4">
                <span>{selectedArticle.helpful} helpful</span>
                <span>{selectedArticle.notHelpful} not helpful</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-8 px-6 md:px-8 lg:px-12 max-w-7xl mx-auto">
      {/* Navigation Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/self-service">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Self Service
            </Link>
          </Button>
        </div>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/self-service">
            <Home className="h-4 w-4 mr-2" />
            Home
          </Link>
        </Button>
      </div>

      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
          Knowledge Base
        </h1>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          Find answers to common questions and solutions to known issues
        </p>
      </div>

      {/* Search and Filters */}
      <Card className="shadow-lg">
        <CardContent className="pt-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search articles by title, content, or tags..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10 text-base"
              />
            </div>

            <div className="flex gap-2">
              <Select value={selectedCategory} onValueChange={handleCategoryChange}>
                <SelectTrigger className="w-[180px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.category} value={category.category}>
                      {category.category} ({category.count})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="updatedAt">Last Updated</SelectItem>
                  <SelectItem value="createdAt">Date Created</SelectItem>
                  <SelectItem value="title">Title</SelectItem>
                  <SelectItem value="views">Most Viewed</SelectItem>
                  <SelectItem value="helpful">Most Helpful</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Articles Grid */}
      {articlesLoading ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-3 bg-muted rounded w-full mb-2"></div>
                <div className="h-3 bg-muted rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredArticles.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No articles found</h3>
            <p className="text-muted-foreground">
              {searchQuery
                ? "Try adjusting your search terms or browse all articles."
                : "No articles available in this category yet."
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredArticles.map((article) => (
            <Card
              key={article.id}
              className="group hover:shadow-lg hover:shadow-primary/5 transition-all duration-300 cursor-pointer border-2 hover:border-primary/20 h-full flex flex-col"
            >
              <CardHeader className="pb-3 flex-shrink-0">
                <div className="flex items-start justify-between mb-2">
                  <Badge variant="secondary" className="text-xs">
                    {article.category}
                  </Badge>
                  {article.isPinned && (
                    <Badge variant="outline" className="text-xs">📌</Badge>
                  )}
                </div>
                <CardTitle className="text-lg font-semibold group-hover:text-primary transition-colors leading-tight line-clamp-2">
                  {article.title}
                </CardTitle>
              </CardHeader>

              <CardContent className="pt-0 flex-1 flex flex-col justify-between">
                <div className="space-y-3 flex-1">
                  <CardDescription className="text-sm leading-relaxed line-clamp-3">
                    {article.content.substring(0, 150)}...
                  </CardDescription>

                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Eye className="h-3 w-3" />
                      <span>{article.views}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <ThumbsUp className="h-3 w-3" />
                      <span>{getHelpfulPercentage(article.helpful, article.notHelpful)}%</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(article.updatedAt)}</span>
                    </div>
                  </div>

                  {article.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {article.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {article.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{article.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>

                <Button
                  variant="ghost"
                  className="w-full mt-4 group-hover:bg-primary group-hover:text-primary-foreground transition-colors"
                  onClick={() => setSelectedArticle(article)}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Read Article
                  <ArrowRight className="h-4 w-4 ml-auto" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Stats */}
      {!articlesLoading && filteredArticles.length > 0 && (
        <div className="text-center text-muted-foreground">
          <p>Showing {filteredArticles.length} article{filteredArticles.length !== 1 ? 's' : ''}</p>
        </div>
      )}
    </div>
  )
}