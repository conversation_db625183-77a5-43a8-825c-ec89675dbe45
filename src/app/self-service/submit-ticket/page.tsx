'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AlertCircle, CheckCircle, FileText, Send, Tag, User, Clock, ArrowLeft, Home, Upload, File, X } from "lucide-react"
import { useCreateTicketMutation } from "@/services/api/tickets"
import { toast } from "sonner"
import { TicketPriority } from "@/lib/enums"
import Link from "next/link"

const PRIORITY_OPTIONS = [
  { value: "Low", label: "Low", color: "bg-blue-100 text-blue-800" },
  { value: "Normal", label: "Normal", color: "bg-green-100 text-green-800" },
  { value: "Top Priority", label: "Top Priority", color: "bg-yellow-100 text-yellow-800" },
  { value: "Urgent", label: "Urgent", color: "bg-red-100 text-red-800" },
  { value: "Later", label: "Later", color: "bg-gray-100 text-gray-800" },
]

export default function SubmitTicketPage() {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'Normal' as TicketPriority,
    tags: [] as string[],
    tagInput: '',
    attachments: [] as File[],
  })

  const [createTicket, { isLoading }] = useCreateTicketMutation()

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleAddTag = () => {
    if (formData.tagInput.trim() && !formData.tags.includes(formData.tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, prev.tagInput.trim()],
        tagInput: ''
      }))
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddTag()
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    const imageFiles = files.filter(file => file.type.startsWith('image/'))
    
    if (imageFiles.length !== files.length) {
      toast.error("Only image files are allowed")
      return
    }
    
    // Check file size (max 5MB per file)
    const maxSize = 5 * 1024 * 1024 // 5MB
    const oversizedFiles = imageFiles.filter(file => file.size > maxSize)
    
    if (oversizedFiles.length > 0) {
      toast.error("Each file must be less than 5MB")
      return
    }
    
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...imageFiles]
    }))
    // Reset input
    e.target.value = ''
  }

  const removeAttachment = (index: number) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.title.trim()) {
      toast.error("Please enter a ticket title")
      return
    }

    if (!formData.description.trim()) {
      toast.error("Please enter a ticket description")
      return
    }

    try {
      const result = await createTicket({
        title: formData.title.trim(),
        description: formData.description.trim(),
        priority: formData.priority,
        tags: formData.tags,
        ...(formData.attachments.length > 0 && { attachments: formData.attachments }),
      })

      if ('error' in result) {
        throw result.error
      }

      toast.success("Ticket created successfully!")
      // Reset form
      setFormData({
        title: '',
        description: '',
        priority: 'Normal',
        tags: [],
        tagInput: '',
        attachments: [],
      })
    } catch (error: any) {
      console.error("Failed to create ticket:", error)
      toast.error(error?.data?.message || error?.message || "Failed to create ticket")
    }
  }

  const selectedPriority = PRIORITY_OPTIONS.find(p => p.value === formData.priority)

  return (
    <div className="space-y-8 px-6 md:px-8 lg:px-12 max-w-4xl mx-auto">
      {/* Navigation Header */}
      <div className="flex items-center justify-between">
        {/* <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/self-service">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Self Service
            </Link>
          </Button>
        </div> */}
        <Button variant="ghost" size="sm" asChild>
          <Link href="/self-service">
            <Home className="h-4 w-4 mr-2" />
            Home
          </Link>
        </Button>
      </div>


      <Card className="shadow-lg border-2">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl flex items-center gap-2">
            <FileText className="h-6 w-6 text-primary" />
            Ticket Details
          </CardTitle>
          <CardDescription>
            Fill out the form below to create your support ticket
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="title" className="text-base font-medium">
                Ticket Title *
              </Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Brief description of your issue"
                className="text-base"
                required
              />
              <p className="text-sm text-muted-foreground">
                Choose a clear, descriptive title that summarizes your issue
              </p>
            </div>

            {/* Priority */}
            <div className="space-y-2">
              <Label className="text-base font-medium">Priority Level</Label>
              <Select
                value={formData.priority}
                onValueChange={(value: TicketPriority) => handleInputChange('priority', value)}
              >
                <SelectTrigger className="text-base">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {PRIORITY_OPTIONS.map((priority) => (
                    <SelectItem key={priority.value} value={priority.value}>
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${priority.color.split(' ')[0]}`} />
                        {priority.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {selectedPriority && (
                <div className="flex items-center gap-2">
                  <Badge className={selectedPriority.color}>
                    {selectedPriority.label}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    {formData.priority === 'Urgent' && 'Critical issue requiring immediate attention'}
                    {formData.priority === 'Top Priority' && 'High priority issue that should be addressed soon'}
                    {formData.priority === 'Normal' && 'Standard priority issue'}
                    {formData.priority === 'Low' && 'Low priority issue for future consideration'}
                    {formData.priority === 'Later' && 'Issue that can be addressed at a later time'}
                  </span>
                </div>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-base font-medium">
                Description *
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Please provide detailed information about your issue, including steps to reproduce, expected behavior, and any error messages..."
                className="min-h-[120px] text-base resize-none"
                required
              />
              <p className="text-sm text-muted-foreground">
                The more details you provide, the better we can help you
              </p>
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <Label className="text-base font-medium">Tags (Optional)</Label>
              <div className="flex gap-2">
                <Input
                  value={formData.tagInput}
                  onChange={(e) => handleInputChange('tagInput', e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Add relevant tags (e.g., bug, feature, billing)"
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleAddTag}
                  disabled={!formData.tagInput.trim()}
                >
                  <Tag className="h-4 w-4" />
                </Button>
              </div>
              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.tags.map((tag) => (
                    <Badge
                      key={tag}
                      variant="secondary"
                      className="cursor-pointer hover:bg-destructive hover:text-destructive-foreground"
                      onClick={() => handleRemoveTag(tag)}
                    >
                      {tag} ×
                    </Badge>
                  ))}
                </div>
              )}
              <p className="text-sm text-muted-foreground">
                Tags help categorize your ticket and make it easier to find
              </p>
            </div>

            {/* Attachments */}
            <div className="space-y-2">
              <Label className="text-base font-medium">Attachments (Images)</Label>
              <div className="space-y-3">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                    id="file-upload"
                  />
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <p className="text-sm text-gray-600">
                      Click to upload images or drag and drop
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      PNG, JPG, GIF up to 5MB each
                    </p>
                  </label>
                </div>
                
                {/* Selected Attachments */}
                {formData.attachments.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Selected images:</p>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {formData.attachments.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg"
                        >
                          <File className="h-8 w-8 text-gray-500 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {file.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeAttachment(index)}
                            className="p-1 hover:bg-red-100 rounded-full transition-colors"
                          >
                            <X className="h-4 w-4 text-red-500" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <p className="text-sm text-muted-foreground">
                Attach screenshots or relevant images to help explain your issue
              </p>
            </div>

            <Separator />

            {/* Submit Button */}
            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <Button
                type="submit"
                size="lg"
                className="flex-1"
                disabled={isLoading || !formData.title.trim() || !formData.description.trim()}
              >
                {isLoading ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    Creating Ticket...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Submit Ticket
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                size="lg"
                asChild
                className="sm:w-auto"
              >
                <Link href="/self-service">Cancel</Link>
              </Button>
            </div>
          </form>

          {/* Help Text */}
          <div className="bg-muted/50 rounded-lg p-4 space-y-3">
            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-sm">What happens next?</h4>
                <p className="text-sm text-muted-foreground">
                  Our support team will review your ticket and respond within 24 hours for urgent issues,
                  or within 48 hours for normal priority tickets.
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-sm">Before submitting:</h4>
                <p className="text-sm text-muted-foreground">
                  Check our Knowledge Base for existing solutions, and ensure you've provided all relevant details
                  including error messages, screenshots, and steps to reproduce the issue.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}