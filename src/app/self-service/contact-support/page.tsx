'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import {
  Phone,
  Mail,
  MessageSquare,
  Clock,
  MapPin,
  Send,
  CheckCircle,
  AlertCircle,
  Headphones,
  Users,
  Zap,
  Shield,
  ArrowLeft,
  Home
} from "lucide-react"
import { useThemeClasses } from "@/components/theme-provider"
import Link from "next/link"
import { toast } from "sonner"
import axios from "@/lib/axios"
import axiosInstance from '@/lib/axios'
const CONTACT_METHODS = [
  {
    icon: Phone,
    title: "Phone Support",
    description: "Speak directly with our support team",
    contact: "+****************",
    availability: "Mon-Fri 9AM-6PM EST",
    priority: "urgent"
  },
  {
    icon: Mail,
    title: "Email Support",
    description: "Send us a detailed message",
    contact: "<EMAIL>",
    availability: "24/7 response within 24 hours",
    priority: "normal"
  },
  {
    icon: MessageSquare,
    title: "Live Chat",
    description: "Get instant help via chat",
    contact: "Available in dashboard",
    availability: "Mon-Fri 9AM-6PM EST",
    priority: "immediate"
  }
]

const SUPPORT_CATEGORIES = [
  "Technical Issues",
  "Billing & Payments",
  "Account Management",
  "Feature Requests",
  "Bug Reports",
  "General Inquiry",
  "Training & Onboarding",
  "Security Concerns"
]

const iconMap = {
  phone: Phone,
  email: Mail,
  chat: MessageSquare,
  Users: Users,
  Zap: Zap,
  Shield: Shield
}

export default function ContactSupportPage() {
  const themeClasses = useThemeClasses();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    category: '',
    subject: '',
    message: '',
    priority: 'normal'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [contactData, setContactData] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchContactData = async () => {
      try {
        const response = await axiosInstance.get(`/tenant/contact-support-info`)
        setContactData(response.data.data)
      } catch (error) {
        console.error('Failed to fetch contact data', error)
        // Fallback to dummy data
        setContactData({
          contactMethods: CONTACT_METHODS,
          supportFeatures: [
            {
              icon: 'Users',
              title: 'Expert Team',
              description: 'Our support team consists of experienced professionals familiar with your industry.'
            },
            {
              icon: 'Zap',
              title: 'Fast Response Times',
              description: 'Urgent issues get immediate attention, with most inquiries resolved within 24 hours.'
            },
            {
              icon: 'Shield',
              title: 'Secure & Confidential',
              description: 'All communications are encrypted and handled with the utmost confidentiality.'
            }
          ],
          officeHours: {
            mondayFriday: '9:00 AM - 6:00 PM EST',
            saturday: '10:00 AM - 4:00 PM EST',
            sunday: 'Closed',
            notes: ['📧 Email support available 24/7', '🚨 Emergency support for critical issues']
          }
        })
      } finally {
        setLoading(false)
      }
    }
    fetchContactData()
  }, [])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name.trim() || !formData.email.trim() || !formData.message.trim()) {
      toast.error("Please fill in all required fields")
      return
    }

    setIsSubmitting(true)

    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000))
      toast.success("Your message has been sent successfully! We'll get back to you within 24 hours.")
      setFormData({
        name: '',
        email: '',
        category: '',
        subject: '',
        message: '',
        priority: 'normal'
      })
    } catch (error) {
      toast.error("Failed to send message. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-8 px-6 md:px-8 lg:px-12 max-w-7xl mx-auto">
      {/* Navigation Header */}
      <div className="flex items-center justify-between">
        {/* <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/self-service">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Self Service
            </Link>
          </Button>
        </div> */}
        <Button variant="ghost" size="sm" asChild className={themeClasses.accent}>
          <Link href="/self-service">
            <Home className="h-4 w-4 mr-2" />
            Home
          </Link>
        </Button>
      </div>

      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
          Contact Support
        </h1>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          Get in touch with our support team through multiple channels
        </p>
      </div>

      <div className="grid gap-8">
        {/* Contact Methods */}
        <div className="space-y-6">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-2xl flex items-center gap-2">
                <Headphones className="h-6 w-6 text-primary" />
                Contact Methods
              </CardTitle>
              <CardDescription>
                Choose the best way to reach our support team
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {contactData?.contactMethods?.map((method, index) => {
                const Icon = iconMap[method.type] || Phone
                return (
                  <div
                    key={index}
                    className="flex items-start gap-4 p-4 rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer"
                  >
                    <div className="p-3 bg-primary/10 rounded-xl flex-shrink-0">
                      <Icon className="h-6 w-6 text-primary" />
                    </div>
                    <div className="flex-1 space-y-1">
                      <h3 className="font-semibold text-lg">{method.title}</h3>
                      <p className="text-muted-foreground text-sm">{method.description}</p>
                      <div className="space-y-1">
                        <p className="font-medium text-sm">{method.contact}</p>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          <span>{method.availability}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </CardContent>
          </Card>

          {/* Support Features */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl">Why Choose Our Support?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {contactData?.supportFeatures?.map((feature, index) => {
                const Icon = iconMap[feature.icon] || Users
                return (
                  <div key={index} className="flex items-start gap-3">
                    <Icon className={`h-5 w-5 text-green-600 mt-0.5 flex-shrink-0 ${feature.icon === 'Zap' ? 'text-yellow-600' : feature.icon === 'Shield' ? 'text-blue-600' : 'text-green-600'}`} />
                    <div>
                      <h4 className="font-medium">{feature.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                )
              })}
            </CardContent>
          </Card>

          {/* Office Hours */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Office Hours
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="font-medium">Monday - Friday</span>
                <span className="text-muted-foreground">{contactData?.officeHours?.mondayFriday || '9:00 AM - 6:00 PM EST'}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Saturday</span>
                <span className="text-muted-foreground">{contactData?.officeHours?.saturday || '10:00 AM - 4:00 PM EST'}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Sunday</span>
                <span className="text-muted-foreground">{contactData?.officeHours?.sunday || 'Closed'}</span>
              </div>
              <Separator />
              <div className="text-sm text-muted-foreground">
                {contactData?.officeHours?.notes?.map((note, index) => (
                  <p key={index}>{note}</p>
                )) || (
                  <>
                    <p>📧 Email support available 24/7</p>
                    <p>🚨 Emergency support for critical issues</p>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>


      </div>
    </div>
  )
}