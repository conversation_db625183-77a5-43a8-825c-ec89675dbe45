@import "tailwindcss";
@import "tw-animate-css";
@import "react-datepicker/dist/react-datepicker.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.375rem;
  --background: oklch(0.98 0.002 247.839);
  --foreground: oklch(0.15 0.025 261.692);
  --card: oklch(0.99 0.001 247.839);
  --card-foreground: oklch(0.15 0.025 261.692);
  --popover: oklch(0.99 0.001 247.839);
  --popover-foreground: oklch(0.15 0.025 261.692);
  --primary: oklch(0.35 0.08 232);
  --primary-foreground: oklch(0.98 0.002 247.839);
  --secondary: oklch(0.94 0.008 264);
  --secondary-foreground: oklch(0.35 0.08 232);
  --muted: oklch(0.95 0.005 264);
  --muted-foreground: oklch(0.45 0.035 264);
  --accent: oklch(0.94 0.008 264);
  --accent-foreground: oklch(0.35 0.08 232);
  --destructive: oklch(0.55 0.18 15);
  --border: oklch(0.88 0.01 264);
  --input: oklch(0.88 0.01 264);
  --ring: oklch(0.35 0.08 232);
  --chart-1: oklch(0.45 0.15 232);
  --chart-2: oklch(0.5 0.12 180);
  --chart-3: oklch(0.52 0.14 280);
  --chart-4: oklch(0.48 0.16 120);
  --chart-5: oklch(0.46 0.18 40);
  --sidebar: oklch(0.97 0.003 247.839);
  --sidebar-foreground: oklch(0.15 0.025 261.692);
  --sidebar-primary: oklch(0.35 0.08 232);
  --sidebar-primary-foreground: oklch(0.98 0.002 247.839);
  --sidebar-accent: oklch(0.94 0.008 264);
  --sidebar-accent-foreground: oklch(0.35 0.08 232);
  --sidebar-border: oklch(0.88 0.01 264);
  --sidebar-ring: oklch(0.35 0.08 232);
}

.dark {
  --background: oklch(0.09 0.015 232);
  --foreground: oklch(0.92 0.008 247.839);
  --card: oklch(0.12 0.02 232);
  --card-foreground: oklch(0.92 0.008 247.839);
  --popover: oklch(0.12 0.02 232);
  --popover-foreground: oklch(0.92 0.008 247.839);
  --primary: oklch(0.65 0.15 232);
  --primary-foreground: oklch(0.09 0.015 232);
  --secondary: oklch(0.18 0.025 232);
  --secondary-foreground: oklch(0.92 0.008 247.839);
  --muted: oklch(0.18 0.025 232);
  --muted-foreground: oklch(0.65 0.02 247);
  --accent: oklch(0.18 0.025 232);
  --accent-foreground: oklch(0.92 0.008 247.839);
  --destructive: oklch(0.65 0.15 15);
  --border: oklch(0.25 0.03 232);
  --input: oklch(0.25 0.03 232);
  --ring: oklch(0.65 0.15 232);
  --chart-1: oklch(0.6 0.18 232);
  --chart-2: oklch(0.58 0.15 180);
  --chart-3: oklch(0.62 0.17 280);
  --chart-4: oklch(0.56 0.16 120);
  --chart-5: oklch(0.54 0.18 40);
  --sidebar: oklch(0.12 0.02 232);
  --sidebar-foreground: oklch(0.92 0.008 247.839);
  --sidebar-primary: oklch(0.65 0.15 232);
  --sidebar-primary-foreground: oklch(0.09 0.015 232);
  --sidebar-accent: oklch(0.18 0.025 232);
  --sidebar-accent-foreground: oklch(0.92 0.008 247.839);
  --sidebar-border: oklch(0.25 0.03 232);
  --sidebar-ring: oklch(0.65 0.15 232);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Fix hover text visibility issues */
@layer utilities {
  /* Override problematic hover states for better visibility */
  .hover\:text-accent-foreground:hover,
  .hover\:bg-accent:hover,
  .hover\:bg-accent:hover *,
  .hover\:bg-sidebar-accent:hover,
  .hover\:bg-sidebar-accent:hover * {
    color: var(--foreground) !important;
  }
  
  /* Ensure muted text becomes visible on hover */
  .text-muted-foreground:hover,
  [class*="text-muted"]:hover {
    color: var(--foreground) !important;
  }
  
  /* Fix card and notification hover states */
  .hover\:bg-accent\/10:hover,
  .hover\:bg-accent\/10:hover *,
  .hover\:bg-accent\/50:hover,
  .hover\:bg-accent\/50:hover *,
  .hover\:bg-muted:hover,
  .hover\:bg-muted:hover *,
  .hover\:bg-muted\/50:hover,
  .hover\:bg-muted\/50:hover * {
    color: var(--foreground) !important;
  }
  
  /* Fix table row hovers */
  tbody tr:hover,
  tbody tr:hover *,
  .table-row:hover,
  .table-row:hover * {
    color: var(--foreground) !important;
  }
  
  /* Dark mode specific fixes */
  .dark .hover\:bg-accent\/50:hover,
  .dark .hover\:bg-accent\/50:hover *,
  .dark .hover\:bg-input\/50:hover,
  .dark .hover\:bg-input\/50:hover * {
    color: var(--foreground) !important;
  }
  
  /* General hover fix for any element with background hover */
  [class*="hover:bg-"]:hover {
    color: var(--foreground) !important;
  }
  
  [class*="hover:bg-"]:hover * {
    color: var(--foreground) !important;
  }
  
  /* Notification-like elements */
  .notification:hover,
  .notification:hover *,
  [role="alert"]:hover,
  [role="alert"]:hover *,
  .alert:hover,
  .alert:hover * {
    color: var(--foreground) !important;
  }
}

/* Grid pattern for login background */
.bg-grid-pattern {
  background-image: 
    linear-gradient(to right, hsl(var(--border)) 1px, transparent 1px),
    linear-gradient(to bottom, hsl(var(--border)) 1px, transparent 1px);
  background-size: 32px 32px;
}


/* Scrollbar base */
::-webkit-scrollbar {
  width: 8px;
  height: 10px;
}

/* Scrollbar track */
::-webkit-scrollbar-track {
  background: #f8f9fa; 
  border-radius: 4px;
}

/* Scrollbar handle */
::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #64748b, #475569);
  border-radius: 4px;
  border: 1px solid #f8f9fa;
}

/* Handle hover */
::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #475569, #334155);
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #475569, #334155);
  border: 1px solid #1e293b;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #334155, #1e293b);
}

/* React DatePicker Styles */
.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker__input-container input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid hsl(var(--border));
  border-radius: calc(var(--radius) - 2px);
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.react-datepicker__input-container input:focus {
  outline: none;
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

.dark .react-datepicker__input-container input {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
}

.react-datepicker {
  font-family: inherit;
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  background-color: hsl(var(--popover));
  color: hsl(var(--popover-foreground));
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.react-datepicker__triangle {
  display: none;
}

.react-datepicker__header {
  background-color: hsl(var(--muted));
  border-bottom: 1px solid hsl(var(--border));
  border-radius: calc(var(--radius) - 1px) calc(var(--radius) - 1px) 0 0;
  padding: 0.5rem;
}

.react-datepicker__current-month {
  color: hsl(var(--foreground));
  font-weight: 600;
  font-size: 0.875rem;
}

.react-datepicker__day-names {
  background-color: hsl(var(--muted));
  border-bottom: 1px solid hsl(var(--border));
}

.react-datepicker__day-name {
  color: hsl(var(--muted-foreground));
  font-size: 0.75rem;
  font-weight: 500;
  width: 2rem;
  line-height: 2rem;
}

.react-datepicker__day {
  color: hsl(var(--foreground));
  width: 2rem;
  line-height: 2rem;
  margin: 0.125rem;
  border-radius: calc(var(--radius) - 2px);
  font-size: 0.875rem;
}

.react-datepicker__day:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.react-datepicker__day--selected {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.react-datepicker__day--selected:hover {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.react-datepicker__day--keyboard-selected {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.react-datepicker__day--today {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  font-weight: 600;
}

.react-datepicker__day--disabled {
  color: hsl(var(--muted-foreground));
  cursor: not-allowed;
}

.react-datepicker__navigation {
  background: none;
  border: none;
  cursor: pointer;
  outline: none;
  top: 1rem;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: calc(var(--radius) - 2px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.react-datepicker__navigation:hover {
  background-color: hsl(var(--accent));
}

.react-datepicker__navigation-icon::before {
  border-color: hsl(var(--foreground));
  border-width: 2px 2px 0 0;
}

.react-datepicker__time-container {
  border-left: 1px solid hsl(var(--border));
}

.react-datepicker__time-container .react-datepicker__time {
  background-color: hsl(var(--popover));
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box {
  width: 100%;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list {
  background-color: hsl(var(--popover));
  padding: 0;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item {
  color: hsl(var(--foreground));
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected:hover {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}