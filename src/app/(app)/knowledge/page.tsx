"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { Plus, <PERSON><PERSON><PERSON>, ThumbsUp, ThumbsDown, Eye } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  SectionHeader,
  FilterBar,
  AppEmpty,
  AppBadge,
  Separator,
} from "@/components/ui-toolkit";
import {
  useGetKnowledgeArticlesQuery,
  useGetKnowledgeCategoriesQuery,
  useGetPopularKnowledgeArticlesQuery,
  useVoteKnowledgeArticleMutation
} from "@/services/api/knowledge";
import { type KnowledgeArticle } from "@/types";
import { CreateKnowledgeArticleDialog } from "@/components/knowledge/CreateKnowledgeArticleDialog";
import { toast } from "sonner";

export default function KnowledgePage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = React.useState("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = React.useState("");
  const [selectedCategory, setSelectedCategory] = React.useState<string | null>(null);
  const [page, setPage] = React.useState(1);

  // Debounce search query for better performance
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const [voteArticle] = useVoteKnowledgeArticleMutation();

  // RTK Query hooks
  const {
    data: articlesResponse,
    isLoading: articlesLoading,
    error: articlesError
  } = useGetKnowledgeArticlesQuery({
    category: selectedCategory || undefined,
    search: debouncedSearchQuery || undefined,
    page,
    limit: 12,
    status: 'published'
  });

  const {
    data: categoriesResponse,
    isLoading: categoriesLoading
  } = useGetKnowledgeCategoriesQuery();

  const {
    data: popularResponse,
    isLoading: popularLoading
  } = useGetPopularKnowledgeArticlesQuery({ limit: 5 });

  const articles = articlesResponse?.data.articles || [];
  const categoryStats = categoriesResponse?.data || [];
  const popularArticles = popularResponse?.data || [];
  
  // Extract unique categories from the API response
  const categories = categoryStats.map(cat => cat.category);

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(selectedCategory === category ? null : category);
    setPage(1); // Reset to first page when category changes
  };

  const handleArticleClick = (article: KnowledgeArticle) => {
    router.push(`/knowledge/${article.id}`);
  };

  const handleQuickVote = async (article: KnowledgeArticle, isHelpful: boolean, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent opening the article

    try {
      const result = await voteArticle({
        id: article.id,
        isHelpful
      });

      if ('error' in result) {
        throw result.error;
      }

      toast.success(`Vote recorded as ${isHelpful ? 'helpful' : 'not helpful'}`);
    } catch (error: any) {
      console.error("Failed to vote:", error);
      toast.error(error?.data?.message || error?.message || "Failed to record vote");
    }
  };

  React.useEffect(() => {
    setPage(1); // Reset to first page when search changes
  }, [debouncedSearchQuery]);

  if (articlesLoading && page === 1) {
    return (
      <div className="space-y-4 p-4">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="grid gap-4 md:grid-cols-4">
            <div className="h-64 bg-muted rounded"></div>
            <div className="md:col-span-3 h-64 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 p-4">
      <SectionHeader
        title="Knowledge Base"
        description="Find answers and learn about common support topics"
        actions={
          <CreateKnowledgeArticleDialog>
            <AppButton icon={<Plus className="h-4 w-4" />}>
              New Article
            </AppButton>
          </CreateKnowledgeArticleDialog>
        }
      />

      <FilterBar
        searchValue={searchQuery}
        onSearchChange={setSearchQuery}
        searchPlaceholder="Search articles..."
      />

      <div className="grid gap-6 md:grid-cols-4">
        {/* Categories Sidebar */}
        <div className="space-y-4">
          <AppCard>
            <AppCardHeader>
              <AppCardTitle className="text-lg">Categories</AppCardTitle>
            </AppCardHeader>
            <AppCardContent className="space-y-2">
              <button
                onClick={() => setSelectedCategory(null)}
                className={`w-full flex items-center justify-between p-2 rounded-lg text-left transition-colors hover:bg-accent/10 ${selectedCategory === null
                    ? 'bg-accent text-accent-foreground'
                    : 'text-foreground'
                  }`}
              >
                <span className="text-sm">All Categories</span>
                <AppBadge variant="secondary" className="text-xs">
                  {articlesResponse?.data?.pagination?.total || 0}
                </AppBadge>
              </button>
              {categories.map((category) => {
                const categoryData = categoryStats.find(c => c.category === category);
                const articleCount = categoryData?.count || 0;
                return (
                  <button
                    key={category}
                    onClick={() => handleCategorySelect(category)}
                    className={`w-full flex items-center justify-between p-2 rounded-lg text-left transition-colors hover:bg-accent/10 ${selectedCategory === category
                        ? 'bg-accent text-accent-foreground'
                        : 'text-foreground'
                      }`}
                  >
                    <span className="text-sm">{category}</span>
                    <AppBadge variant="secondary" className="text-xs">
                      {articleCount}
                    </AppBadge>
                  </button>
                );
              })}
            </AppCardContent>
          </AppCard>

          <AppCard>
            <AppCardHeader>
              <AppCardTitle className="text-lg">Popular Articles</AppCardTitle>
            </AppCardHeader>
            <AppCardContent className="space-y-3">
              {popularLoading ? (
                <div className="space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="space-y-1">
                      <div className="h-4 bg-muted rounded w-full"></div>
                      <div className="h-3 bg-muted rounded w-3/4"></div>
                    </div>
                  ))}
                </div>
              ) : (
                popularArticles.map((article) => (
                  <div
                    key={article.id}
                    className="space-y-1 cursor-pointer hover:bg-accent/5 p-2 rounded -m-2"
                    onClick={() => handleArticleClick(article)}
                  >
                    <p className="text-sm font-medium leading-tight">{article.title}</p>
                    <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Eye className="h-3 w-3" />
                        <span>{article.views}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <ThumbsUp className="h-3 w-3" />
                        <span>{article.helpful}</span>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </AppCardContent>
          </AppCard>
        </div>

        {/* Articles Grid */}
        <div className="md:col-span-3">
          {articlesLoading ? (
            <div className="flex items-center justify-center py-16">
              <div className="text-muted-foreground">Loading articles...</div>
            </div>
          ) : articlesError ? (
            <div className="flex items-center justify-center py-16">
              <div className="text-red-500">Failed to load articles. Please try again.</div>
            </div>
          ) : !articlesResponse || articlesResponse.data.articles.length === 0 ? (
            <div className="flex flex-col items-center justify-center text-center py-16 px-6">
              <div className="mb-4 text-muted-foreground">
                <BookOpen className="h-12 w-12" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No articles found</h3>
              <p className="text-sm text-muted-foreground mb-6 max-w-md">
                {debouncedSearchQuery || selectedCategory
                  ? "No articles match your current search or filter criteria."
                  : "No articles have been created yet."}
              </p>
              <CreateKnowledgeArticleDialog>
                <AppButton icon={<Plus className="h-4 w-4" />}>
                  Create First Article
                </AppButton>
              </CreateKnowledgeArticleDialog>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {articlesResponse.data.articles.map((article: KnowledgeArticle) => (
                <AppCard
                  key={article.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => handleArticleClick(article)}
                >
                  <AppCardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <AppBadge variant="secondary" className="text-xs mb-2">
                        {article.category}
                      </AppBadge>
                    </div>
                    <AppCardTitle className="text-base line-clamp-2">
                      {article.title}
                    </AppCardTitle>
                  </AppCardHeader>
                  <AppCardContent>
                    <AppCardDescription className="line-clamp-3 mb-4">
                      {article.content.substring(0, 120)}...
                    </AppCardDescription>

                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2 text-muted-foreground">
                        <span>{article.author.name}</span>
                        <span>•</span>
                        <span>{new Date(article.createdAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={(e) => handleQuickVote(article, true, e)}
                          className="flex items-center space-x-1 text-gray-500 hover:text-green-600 transition-colors"
                        >
                          <ThumbsUp className="h-3 w-3" />
                          <span className="text-xs">{article.helpful}</span>
                        </button>
                        <button
                          onClick={(e) => handleQuickVote(article, false, e)}
                          className="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors"
                        >
                          <ThumbsDown className="h-3 w-3" />
                          <span className="text-xs">{article.notHelpful}</span>
                        </button>
                      </div>
                    </div>

                    <Separator className="my-3" />

                    <div className="flex flex-wrap gap-1">
                      {article.tags.slice(0, 3).map((tag) => (
                        <AppBadge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </AppBadge>
                      ))}
                      {article.tags.length > 3 && (
                        <AppBadge variant="outline" className="text-xs">
                          +{article.tags.length - 3}
                        </AppBadge>
                      )}
                    </div>
                  </AppCardContent>
                </AppCard>
              ))}
            </div>
          )}

          {/* Pagination */}
          {articlesResponse && articlesResponse.data.pagination.pages > 1 && (
            <div className="flex items-center justify-center space-x-2 mt-8">
              <AppButton
                variant="outline"
                size="sm"
                onClick={() => setPage(page - 1)}
                disabled={page <= 1}
              >
                Previous
              </AppButton>

              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, articlesResponse.data.pagination.pages) }, (_, i) => {
                  const pageNum = page - 2 + i;
                  if (pageNum < 1 || pageNum > articlesResponse.data.pagination.pages) return null;

                  return (
                    <AppButton
                      key={pageNum}
                      size="sm"
                      onClick={() => setPage(pageNum)}
                    >
                      {pageNum}
                    </AppButton>
                  );
                })}
              </div>

              <AppButton
                variant="outline"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={page >= articlesResponse.data.pagination.pages}
              >
                Next
              </AppButton>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
