"use client";

import * as React from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { 
  <PERSON><PERSON>ef<PERSON>, 
  ThumbsUp, 
  ThumbsDown, 
  Edit, 
  Trash2, 
  Calendar, 
  User, 
  Tag, 
  Eye,
  Loader2
} from "lucide-react";
import {
  App<PERSON><PERSON>,
  App<PERSON><PERSON><PERSON>ontent,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  AppBadge,
  Separator,
} from "@/components/ui-toolkit";
import {
  useGetKnowledgeArticleQuery,
  useVoteKnowledgeArticleMutation,
  useDeleteKnowledgeArticleMutation
} from "@/services/api/knowledge";
import { EditKnowledgeArticleDialog } from "@/components/knowledge/EditKnowledgeArticleDialog";
import { toast } from "sonner";

export default function ArticleDetailPage() {
  const params = useParams();
  const router = useRouter();
  const articleId = params.id as string;
  
  const [userVote, setUserVote] = React.useState<boolean | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = React.useState(false);

  const {
    data: articleResponse,
    isLoading: articleLoading,
    error: articleError,
    refetch
  } = useGetKnowledgeArticleQuery(articleId, {
    skip: !articleId
  });

  const [voteArticle, { isLoading: voteLoading }] = useVoteKnowledgeArticleMutation();
  const [deleteArticle, { isLoading: deleteLoading }] = useDeleteKnowledgeArticleMutation();

  const article = articleResponse?.data;

  // Initialize userVote from the article data when it loads
  React.useEffect(() => {
    if (article?.userVote !== undefined) {
      setUserVote(article.userVote);
    }
  }, [article?.userVote]);

  const handleVote = async (isHelpful: boolean) => {
    if (!article) return;

    try {
      const result = await voteArticle({
        id: article.id,
        isHelpful
      });

      console.log("Vote result:", result); // Debug log

      if ('error' in result) {
        // Handle RTK Query error
        const error = result.error as any;
        console.error("Failed to vote:", error);
        toast.error(error?.data?.message || error?.message || "Failed to record vote");
        return;
      }

      const response = result.data;
      
      // Update local state based on the response
      setUserVote(response.data.userVote);
      
      // Show appropriate message
      toast.success(response.message);
      
      // Refetch to get updated vote counts
      refetch();
    } catch (error: any) {
      console.error("Failed to vote:", error);
      toast.error(error?.data?.message || error?.message || "Failed to record vote");
    }
  };

  const handleDelete = async () => {
    if (!article) return;

    if (!confirm("Are you sure you want to delete this article? This action cannot be undone.")) {
      return;
    }

    try {
      const result = await deleteArticle(article.id);

      if ('error' in result) {
        throw result.error;
      }

      toast.success("Article deleted successfully");
      router.push("/knowledge");
    } catch (error: any) {
      console.error("Failed to delete article:", error);
      toast.error(error?.data?.message || error?.message || "Failed to delete article");
    }
  };

  const handleEdit = () => {
    setIsEditDialogOpen(true);
  };

  const handleBack = () => {
    router.back();
  };

  if (articleLoading) {
    return (
      <div className="space-y-6 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-32"></div>
          <div className="space-y-4">
            <div className="h-8 bg-muted rounded w-3/4"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
            <div className="h-64 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (articleError || !article) {
    return (
      <div className="space-y-6 p-6">
        <AppButton
          variant="outline"
          onClick={handleBack}
          icon={<ArrowLeft className="h-4 w-4" />}
        >
          Back
        </AppButton>
        <div className="flex flex-col items-center justify-center text-center py-16 px-6">
          <h3 className="text-lg font-semibold mb-2">Article not found</h3>
          <p className="text-sm text-muted-foreground mb-6">
            The article you're looking for doesn't exist or has been removed.
          </p>
          <AppButton onClick={() => router.push("/knowledge")}>
            Go to Knowledge Base
          </AppButton>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6 p-6 max-w-4xl mx-auto">
        {/* Header with back button */}
        <div className="flex items-center justify-between">
          <AppButton
            variant="outline"
            onClick={handleBack}
            icon={<ArrowLeft className="h-4 w-4" />}
          >
            Back
          </AppButton>
          
          {/* Action buttons */}
          <div className="flex items-center gap-2">
            <AppButton
              variant="outline"
              size="sm"
              onClick={handleEdit}
              disabled={deleteLoading}
              icon={<Edit className="h-4 w-4" />}
            >
              Edit
            </AppButton>
            <AppButton
              variant="destructive"
              size="sm"
              onClick={handleDelete}
              disabled={deleteLoading}
              icon={deleteLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
            >
              {deleteLoading ? "Deleting..." : "Delete"}
            </AppButton>
          </div>
        </div>

        {/* Article content */}
        <AppCard>
          <AppCardHeader className="space-y-4">
            {/* Title and badges */}
            <div className="space-y-3">
              <div className="flex items-start gap-3 flex-wrap">
                <AppBadge variant="secondary">{article.category}</AppBadge>
                {article.isPinned && (
                  <AppBadge variant="outline">Pinned</AppBadge>
                )}
                {article.isInternal && (
                  <AppBadge variant="outline">Internal</AppBadge>
                )}
                <AppBadge 
                  variant={article.status === 'published' ? 'default' : 'secondary'}
                >
                  {article.status}
                </AppBadge>
              </div>
              
              <AppCardTitle className="text-2xl font-bold leading-tight">
                {article.title}
              </AppCardTitle>
            </div>

            {/* Meta information */}
            <div className="flex items-center gap-6 text-sm text-muted-foreground flex-wrap">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>{article.author.name}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>{new Date(article.createdAt).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                <span>{article.views} views</span>
              </div>
            </div>
          </AppCardHeader>

          <AppCardContent className="space-y-6">
            {/* Article content */}
            <div className="prose max-w-none">
              <div className="whitespace-pre-wrap text-sm leading-relaxed">
                {article.content}
              </div>
            </div>

            {/* Tags */}
            {article.tags.length > 0 && (
              <div>
                <Separator className="mb-4" />
                <div className="flex items-center gap-2 flex-wrap">
                  <Tag className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">Tags:</span>
                  {article.tags.map((tag) => (
                    <AppBadge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </AppBadge>
                  ))}
                </div>
              </div>
            )}

            {/* Voting section */}
            <div>
              <Separator className="mb-4" />
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  Was this article helpful?
                </div>
                <div className="flex items-center gap-3">
                  <AppButton
                    size="sm"
                    variant={userVote === true ? "solid" : "outline"}
                    onClick={() => handleVote(true)}
                    disabled={voteLoading}
                    icon={voteLoading && userVote === true ? 
                      <Loader2 className="h-4 w-4 animate-spin" /> : 
                      <ThumbsUp className="h-4 w-4" />
                    }
                  >
                    {article.helpful}
                  </AppButton>
                  <AppButton
                    size="sm"
                    variant={userVote === false ? "solid" : "outline"}
                    onClick={() => handleVote(false)}
                    disabled={voteLoading}
                    icon={voteLoading && userVote === false ? 
                      <Loader2 className="h-4 w-4 animate-spin" /> : 
                      <ThumbsDown className="h-4 w-4" />
                    }
                  >
                    {article.notHelpful}
                  </AppButton>
                </div>
              </div>
            </div>

            {/* Footer with update info */}
            <div>
              <Separator className="mb-4" />
              <div className="text-xs text-muted-foreground text-center">
                Last updated: {new Date(article.updatedAt).toLocaleDateString()}
              </div>
            </div>
          </AppCardContent>
        </AppCard>
      </div>

      {/* Edit Dialog */}
      <EditKnowledgeArticleDialog
        article={article}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
      />
    </>
  );
}
