"use client";

import * as React from "react";
import { 
  Ticket, 
  Clock, 
  CheckCircle, 
  Plus,
  TrendingUp,
} from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppStat,
  AppButton,
  SectionHeader,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  AppBadge,
} from "@/components/ui-toolkit";
import { AppChart } from "@/components/ui-toolkit/app-chart";
import { dashboardService, ticketService } from "@/lib/demo/api";
import type { Ticket as TicketType } from "@/lib/demo/types";

export default function DashboardPage() {
  const [stats, setStats] = React.useState({
    totalTickets: 0,
    openTickets: 0,
    resolvedToday: 0,
    avgResolutionTime: "0 hours",
  });
  const [chartData, setChartData] = React.useState<{
    ticketVolume: { name: string; value: number }[];
    slaCompliance: { name: string; value: number }[];
    agentWorkload: { name: string; value: number }[];
  }>({
    ticketVolume: [],
    slaCompliance: [],
    agentWorkload: [],
  });
  const [recentTickets, setRecentTickets] = React.useState<TicketType[]>([]);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const loadDashboardData = async () => {
      try {
        const [statsData, chartsData, tickets] = await Promise.all([
          dashboardService.getStats(),
          dashboardService.getChartData(),
          ticketService.getAll(),
        ]);
        
        setStats(statsData);
        setChartData(chartsData);
        setRecentTickets(tickets.slice(0, 5));
      } catch (error) {
        console.error("Failed to load dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <SectionHeader
        title="Dashboard"
        description="Overview of your ticket system performance"

      />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <AppStat
          title="Total Tickets"
          value={stats.totalTickets}
          icon={<Ticket className="h-4 w-4" />}
          delta={{
            value: 12,
            type: "positive",
            label: "vs last month",
          }}
        />
        <AppStat
          title="Open Tickets"
          value={stats.openTickets}
          icon={<Clock className="h-4 w-4" />}
          delta={{
            value: -8,
            type: "positive",
            label: "vs last week",
          }}
        />
        <AppStat
          title="Resolved Today"
          value={stats.resolvedToday}
          icon={<CheckCircle className="h-4 w-4" />}
          delta={{
            value: 15,
            type: "positive",
            label: "vs yesterday",
          }}
        />
        <AppStat
          title="Avg Resolution Time"
          value={stats.avgResolutionTime}
          icon={<TrendingUp className="h-4 w-4" />}
          delta={{
            value: -5,
            type: "positive",
            label: "vs last month",
          }}
        />
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <AppCard>
          <AppCardHeader>
            <AppCardTitle>Ticket Volume</AppCardTitle>
            <AppCardDescription>
              Number of tickets created over the last 6 months
            </AppCardDescription>
          </AppCardHeader>
          <AppCardContent>
            <AppChart
              type="area"
              data={chartData.ticketVolume}
              height={300}
              dataKey="value"
              nameKey="name"
            />
          </AppCardContent>
        </AppCard>

        <AppCard>
          <AppCardHeader>
            <AppCardTitle>SLA Compliance</AppCardTitle>
            <AppCardDescription>
              Percentage of tickets resolved within SLA
            </AppCardDescription>
          </AppCardHeader>
          <AppCardContent>
            <AppChart
              type="pie"
              data={chartData.slaCompliance}
              height={300}
              dataKey="value"
              nameKey="name"
            />
          </AppCardContent>
        </AppCard>
      </div>

      {/* Recent Tickets */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Recent Tickets</AppCardTitle>
          <AppCardDescription>
            Latest tickets that need attention
          </AppCardDescription>
        </AppCardHeader>
        <AppCardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Ticket</TableHead>
                <TableHead>Subject</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Assignee</TableHead>
                <TableHead>Created</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentTickets.map((ticket) => (
                <TableRow key={ticket.id}>
                  <TableCell className="font-medium">{ticket.number}</TableCell>
                  <TableCell>{ticket.subject}</TableCell>
                  <TableCell>
                    <AppBadge status={ticket.status}>
                      {ticket.status.replace("_", " ")}
                    </AppBadge>
                  </TableCell>
                  <TableCell>
                    <AppBadge priority={ticket.priority}>
                      {ticket.priority}
                    </AppBadge>
                  </TableCell>
                  <TableCell>
                    {ticket.assignee ? ticket.assignee.name : "Unassigned"}
                  </TableCell>
                  <TableCell>
                    {ticket.createdAt.toLocaleDateString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </AppCardContent>
      </AppCard>
    </div>
  );
}
