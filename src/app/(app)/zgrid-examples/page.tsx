"use client";

import * as React from "react";
import { <PERSON>, <PERSON>, Trash2, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, ChevronR<PERSON>, ChevronDown } from "lucide-react";
import { ZGrid, ZGridColumn } from "@/shared";
import {
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>pp<PERSON>ard<PERSON><PERSON><PERSON>,
  SectionHeader,
  AppBadge,
  AppButton
} from "@/components/ui-toolkit";

// Dummy data for different examples
const usersData = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    status: "Active",
    joinDate: "2024-01-15",
    lastLogin: "2024-08-30"
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    joinDate: "2024-02-20",
    lastLogin: "2024-08-29"
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Manager",
    status: "Inactive",
    joinDate: "2024-01-10",
    lastLogin: "2024-08-25"
  },
  {
    id: "4",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    joinDate: "2024-03-05",
    lastLogin: "2024-08-30"
  },
  {
    id: "5",
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    status: "Pending",
    joinDate: "2024-08-28",
    lastLogin: "Never"
  },
  {
    id: "6",
    name: "<PERSON> Ross",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    joinDate: "2024-06-12",
    lastLogin: "2024-08-28"
  },
  {
    id: "7",
    name: "Frank Miller",
    email: "<EMAIL>",
    role: "Manager",
    status: "Inactive",
    joinDate: "2024-04-18",
    lastLogin: "2024-08-20"
  }
];

const ticketsData = [
  {
    id: "TK-001",
    number: "TK-001",
    subject: "Login Issue - User unable to access dashboard",
    description: "User unable to login to the system",
    priority: "High",
    status: "Open",
    assignee: "John Doe",
    requester: "Customer A",
    createdAt: "2024-08-28",
    updatedAt: "2024-08-30"
  },
  {
    id: "TK-002",
    number: "TK-002",
    subject: "Email Configuration Setup",
    description: "Setup email notifications for new tickets",
    priority: "Medium",
    status: "In Progress",
    assignee: "Jane Smith",
    requester: "Customer B",
    createdAt: "2024-08-27",
    updatedAt: "2024-08-29"
  },
  {
    id: "TK-003",
    number: "TK-003",
    subject: "Database Backup Implementation",
    description: "Implement automated database backup solution",
    priority: "Low",
    status: "Resolved",
    assignee: "Bob Johnson",
    requester: "Internal",
    createdAt: "2024-08-25",
    updatedAt: "2024-08-28"
  },
  {
    id: "TK-004",
    number: "TK-004",
    subject: "UI Bug Fix - Button Alignment",
    description: "Fix button alignment in dashboard",
    priority: "Medium",
    status: "Open",
    assignee: "Alice Brown",
    requester: "Customer C",
    createdAt: "2024-08-29",
    updatedAt: "2024-08-30"
  },
  {
    id: "TK-005",
    number: "TK-005",
    subject: "Performance Optimization",
    description: "Optimize API response time",
    priority: "High",
    status: "In Progress",
    assignee: "Charlie Wilson",
    requester: "Customer D",
    createdAt: "2024-08-26",
    updatedAt: "2024-08-30"
  }
];

// Nested/Hierarchical data example
const nestedCustomersData = [
  {
    id: "1",
    customer: "Tech Corp",
    email: "<EMAIL>",
    status: "Active",
    totalOrders: 25,
    children: [
      {
        id: "1-1",
        orderNumber: "ORD-001",
        amount: 1500,
        date: "2024-08-25",
        status: "Completed",
        items: [
          { id: "1-1-1", product: "Laptop", quantity: 2, price: 750 },
          { id: "1-1-2", product: "Mouse", quantity: 3, price: 25 }
        ]
      },
      {
        id: "1-2", 
        orderNumber: "ORD-002",
        amount: 2300,
        date: "2024-08-28",
        status: "Processing",
        items: [
          { id: "1-2-1", product: "Monitor", quantity: 1, price: 800 },
          { id: "1-2-2", product: "Keyboard", quantity: 2, price: 150 },
          { id: "1-2-3", product: "Webcam", quantity: 5, price: 100 }
        ]
      }
    ]
  },
  {
    id: "2",
    customer: "Digital Solutions",
    email: "<EMAIL>",
    status: "Active", 
    totalOrders: 18,
    children: [
      {
        id: "2-1",
        orderNumber: "ORD-003",
        amount: 850,
        date: "2024-08-20",
        status: "Shipped",
        items: [
          { id: "2-1-1", product: "Tablet", quantity: 1, price: 500 },
          { id: "2-1-2", product: "Case", quantity: 2, price: 175 }
        ]
      }
    ]
  },
  {
    id: "3",
    customer: "StartUp Inc",
    email: "<EMAIL>", 
    status: "Pending",
    totalOrders: 5,
    children: [
      {
        id: "3-1",
        orderNumber: "ORD-004", 
        amount: 1200,
        date: "2024-08-30",
        status: "Pending",
        items: [
          { id: "3-1-1", product: "Phone", quantity: 2, price: 400 },
          { id: "3-1-2", product: "Charger", quantity: 4, price: 100 }
        ]
      },
      {
        id: "3-2",
        orderNumber: "ORD-005",
        amount: 950,
        date: "2024-08-29",
        status: "Cancelled",
        items: [
          { id: "3-2-1", product: "Speaker", quantity: 1, price: 300 }
        ]
      }
    ]
  }
];

// Complex nested data with API-like structure
const apiCustomersData = [
  {
    id: 1,
    customer: "Zunnoorain Sheikh",
    email: "<EMAIL>",
    address: {
      country: "Pakistan",
      city: "Lahore", 
      zipcode: 54000,
      street: "Main Road"
    },
    phoneNumbers: {
      phone1: "0301-1234567",
      phone2: "042-12345678"
    },
    orders: [
      {
        id: 101,
        orderNumber: "ORD-2024-001", 
        total: 2500,
        date: "2024-08-25",
        status: "Delivered",
        products: [
          { id: 1001, name: "Gaming Laptop", price: 1500, quantity: 1 },
          { id: 1002, name: "Wireless Mouse", price: 50, quantity: 2 }
        ]
      }
    ]
  },
  {
    id: 2,
    customer: "Ahmad Ali",
    email: "<EMAIL>",
    address: {
      country: "Pakistan", 
      city: "Karachi",
      zipcode: 75000,
      street: "Clifton Block"
    },
    phoneNumbers: {
      phone1: "0300-9876543",
      phone2: "021-87654321"
    },
    orders: [
      {
        id: 102,
        orderNumber: "ORD-2024-002",
        total: 1800,
        date: "2024-08-28",
        status: "Processing", 
        products: [
          { id: 1003, name: "Smartphone", price: 800, quantity: 1 },
          { id: 1004, name: "Phone Case", price: 25, quantity: 4 }
        ]
      },
      {
        id: 103,
        orderNumber: "ORD-2024-003",
        total: 3200,
        date: "2024-08-30",
        status: "Shipped",
        products: [
          { id: 1005, name: "4K Monitor", price: 600, quantity: 2 },
          { id: 1006, name: "HDMI Cable", price: 15, quantity: 8 }
        ]
      }
    ]
  }
];

// Column definitions for nested data examples
const nestedCustomerColumns: ZGridColumn[] = [
  { 
    field: 'customer', 
    headerName: 'Customer Name', 
    width: '200px', 
    sortable: true,
    isExpanderColumn: true, // This indicates the expand/collapse column
    filterable: true 
  },
  { 
    field: 'email', 
    headerName: 'Email', 
    width: '200px', 
    sortable: true,
    filterable: true 
  },
  { 
    field: 'totalOrders', 
    headerName: 'Orders', 
    width: '100px',
    cellRenderer: (value) => value || 0
  },
  { 
    field: 'totalSpent', 
    headerName: 'Total Spent', 
    width: '120px',
    cellRenderer: (value) => value ? `$${value.toLocaleString()}` : '$0'
  }
];

// Child columns for order details (shown when customer row is expanded)
const orderChildColumns: ZGridColumn[] = [
  { 
    field: 'orderNumber', 
    headerName: 'Order #', 
    width: '150px'
  },
  { 
    field: 'total', 
    headerName: 'Amount', 
    width: '120px',
    cellRenderer: (value) => `$${value}`
  },
  { 
    field: 'date', 
    headerName: 'Date', 
    width: '120px'
  },
  { 
    field: 'status', 
    headerName: 'Status', 
    width: '120px',
    cellRenderer: (value) => (
      <AppBadge variant={value === 'Delivered' ? 'default' : 
                         value === 'Shipped' ? 'secondary' : 
                         value === 'Processing' ? 'outline' : 'secondary'}>
        {value}
      </AppBadge>
    )
  }
];

// API-like complex data columns
const apiCustomerColumns: ZGridColumn[] = [
  { 
    field: 'customer', 
    headerName: 'Customer', 
    width: '180px', 
    isExpanderColumn: true,
    sortable: true, 
    filterable: true 
  },
  { 
    field: 'email', 
    headerName: 'Email', 
    width: '200px', 
    sortable: true, 
    filterable: true 
  },
  { 
    field: 'address.city', // Nested field access
    headerName: 'City', 
    width: '120px'
    // nestedField will be handled by data flattening in ZGrid
  },
  { 
    field: 'address.country', 
    headerName: 'Country', 
    width: '120px'
  },
  { 
    field: 'phoneNumbers.phone1', 
    headerName: 'Primary Phone', 
    width: '140px'
  }
];

// Child columns for API orders
const apiOrderChildColumns: ZGridColumn[] = [
  { 
    field: 'orderNumber', 
    headerName: 'Order Number', 
    width: '140px'
  },
  { 
    field: 'total', 
    headerName: 'Total', 
    width: '100px',
    cellRenderer: (value) => `$${value}`
  },
  { 
    field: 'date', 
    headerName: 'Order Date', 
    width: '120px'
  },
  { 
    field: 'status', 
    headerName: 'Status', 
    width: '100px',
    cellRenderer: (value) => (
      <AppBadge variant={value === 'Delivered' ? 'default' : 
                         value === 'Shipped' ? 'secondary' : 
                         'outline'}>
        {value}
      </AppBadge>
    )
  }
];

export default function ZGridExamplesPage() {
  const [selectedUsers, setSelectedUsers] = React.useState<string[]>([]);
  const [selectedTickets, setSelectedTickets] = React.useState<string[]>([]);
  const [loading, setLoading] = React.useState(false);

  // Status badge renderer
  const statusBadge = (value: string) => {
    const statusMap: Record<string, { variant: any; className: string }> = {
      'Active': { variant: 'default', className: 'bg-green-100 text-green-800 border-green-200' },
      'Inactive': { variant: 'destructive', className: 'bg-red-100 text-red-800 border-red-200' },
      'Pending': { variant: 'secondary', className: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
      'Open': { variant: 'default', className: 'bg-blue-100 text-blue-800 border-blue-200' },
      'In Progress': { variant: 'default', className: 'bg-orange-100 text-orange-800 border-orange-200' },
      'Resolved': { variant: 'default', className: 'bg-green-100 text-green-800 border-green-200' },
    };

    const config = statusMap[value] || { variant: 'default', className: 'bg-gray-100 text-gray-800' };
    
    return (
      <AppBadge variant={config.variant} className={config.className}>
        {value.replace('_', ' ')}
      </AppBadge>
    );
  };

  // Priority badge renderer
  const priorityBadge = (value: string) => {
    const priorityMap: Record<string, { variant: any; className: string }> = {
      'High': { variant: 'destructive', className: 'bg-red-100 text-red-800 border-red-200' },
      'Medium': { variant: 'default', className: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
      'Low': { variant: 'secondary', className: 'bg-green-100 text-green-800 border-green-200' },
    };

    const config = priorityMap[value] || { variant: 'default', className: 'bg-gray-100 text-gray-800' };
    
    return (
      <AppBadge variant={config.variant} className={config.className}>
        {value}
      </AppBadge>
    );
  };

  // Actions renderer
  const actionsRenderer = (value: any, row: any, rowIndex: number) => {
    return (
      <div className="flex items-center space-x-1">
        <AppButton
          size="sm"
          variant="ghost"
          onClick={(e) => {
            e.stopPropagation();
            console.log('View', row);
          }}
        >
          <Eye className="w-4 h-4" />
        </AppButton>
        <AppButton
          size="sm"
          variant="ghost"
          onClick={(e) => {
            e.stopPropagation();
            console.log('Edit', row);
          }}
        >
          <Edit className="w-4 h-4" />
        </AppButton>
        <AppButton
          size="sm"
          variant="ghost"
          onClick={(e) => {
            e.stopPropagation();
            console.log('Delete', row);
          }}
        >
          <Trash2 className="w-4 h-4" />
        </AppButton>
      </div>
    );
  };

  // Ticket number renderer
  const ticketNumberRenderer = (value: string, row: any) => {
    return (
      <button
        className="text-primary hover:underline font-medium"
        onClick={(e) => {
          e.stopPropagation();
          console.log('Open ticket:', row);
        }}
      >
        {value}
      </button>
    );
  };

  // Subject renderer with truncation
  const subjectRenderer = (value: string) => {
    return (
      <div className="max-w-xs">
        <div className="font-medium truncate" title={value}>
          {value}
        </div>
      </div>
    );
  };

  // Column definitions with advanced styling
  const userColumns: ZGridColumn[] = [
    { field: 'id', headerName: 'ID', width: '80px', sortable: true },
    { 
      field: 'name', 
      headerName: 'Name', 
      width: '150px', 
      sortable: true, 
      filterable: true,
      headerClassName: 'font-bold text-blue-600',
      cellClassName: 'font-semibold'
    },
    { 
      field: 'email', 
      headerName: 'Email', 
      width: '200px', 
      sortable: true, 
      filterable: true,
      cellClassName: 'text-blue-500 hover:underline cursor-pointer'
    },
    { 
      field: 'role', 
      headerName: 'Role', 
      width: '120px', 
      sortable: true, 
      filterable: true,
      filterType: 'select',
      filterOptions: [
        { label: 'Admin', value: 'Admin' },
        { label: 'User', value: 'User' },
        { label: 'Manager', value: 'Manager' }
      ]
    },
    { 
      field: 'status', 
      headerName: 'Status', 
      width: '120px', 
      sortable: true, 
      filterable: true,
      cellRenderer: statusBadge,
      filterType: 'select',
      filterOptions: [
        { label: 'Active', value: 'Active' },
        { label: 'Inactive', value: 'Inactive' },
        { label: 'Pending', value: 'Pending' }
      ]
    },
    { field: 'joinDate', headerName: 'Join Date', width: '120px', sortable: true, type: 'date', filterType: 'date' },
    { field: 'lastLogin', headerName: 'Last Login', width: '120px', sortable: true },
    { field: 'actions', headerName: 'Actions', width: '120px', cellRenderer: actionsRenderer }
  ];

  const ticketColumns: ZGridColumn[] = [
    { 
      field: 'number', 
      headerName: 'Ticket', 
      width: '100px', 
      sortable: true, 
      filterable: true,
      cellRenderer: ticketNumberRenderer,
      headerClassName: 'bg-blue-50 font-bold'
    },
    { 
      field: 'subject', 
      headerName: 'Subject', 
      width: '300px', 
      sortable: true, 
      filterable: true,
      cellRenderer: subjectRenderer,
      headerClassName: 'bg-blue-50'
    },
    { 
      field: 'status', 
      headerName: 'Status', 
      width: '120px', 
      sortable: true, 
      filterable: true,
      cellRenderer: statusBadge,
      filterType: 'select',
      filterOptions: [
        { label: 'Open', value: 'Open' },
        { label: 'In Progress', value: 'In Progress' },
        { label: 'Resolved', value: 'Resolved' }
      ],
      headerClassName: 'bg-blue-50'
    },
    { 
      field: 'priority', 
      headerName: 'Priority', 
      width: '100px', 
      sortable: true, 
      filterable: true,
      cellRenderer: priorityBadge,
      filterType: 'select',
      filterOptions: [
        { label: 'High', value: 'High' },
        { label: 'Medium', value: 'Medium' },
        { label: 'Low', value: 'Low' }
      ],
      headerClassName: 'bg-blue-50'
    },
    { field: 'requester', headerName: 'Requester', width: '150px', sortable: true, filterable: true, headerClassName: 'bg-blue-50' },
    { field: 'assignee', headerName: 'Assignee', width: '150px', sortable: true, filterable: true, headerClassName: 'bg-blue-50' },
    { field: 'createdAt', headerName: 'Created', width: '120px', sortable: true, type: 'date', filterType: 'date', headerClassName: 'bg-blue-50' },
    { field: 'actions', headerName: 'Actions', width: '120px', cellRenderer: actionsRenderer, headerClassName: 'bg-blue-50' }
  ];

  // Nested customers columns (parent level)
  const nestedCustomerColumns: ZGridColumn[] = [
    { 
      field: 'customer', 
      headerName: 'Customer', 
      width: '200px', 
      sortable: true, 
      filterable: true,
      isExpanderColumn: true, // This makes it the expand/collapse column
      headerClassName: 'bg-green-50 font-bold'
    },
    { field: 'email', headerName: 'Email', width: '200px', sortable: true, filterable: true, headerClassName: 'bg-green-50' },
    { 
      field: 'status', 
      headerName: 'Status', 
      width: '120px', 
      sortable: true, 
      cellRenderer: statusBadge,
      headerClassName: 'bg-green-50'
    },
    { field: 'totalOrders', headerName: 'Total Orders', width: '120px', sortable: true, type: 'number', headerClassName: 'bg-green-50' }
  ];

  // Child columns for orders
  const orderChildColumns: ZGridColumn[] = [
    { field: 'orderNumber', headerName: 'Order #', width: '120px', sortable: true, filterable: true },
    { 
      field: 'amount', 
      headerName: 'Amount', 
      width: '100px', 
      sortable: true, 
      type: 'number',
      cellRenderer: (value) => `$${value}`
    },
    { field: 'date', headerName: 'Date', width: '120px', sortable: true, type: 'date' },
    { 
      field: 'status', 
      headerName: 'Status', 
      width: '120px', 
      cellRenderer: statusBadge
    }
  ];

  // API-like nested data columns
  const apiCustomerColumns: ZGridColumn[] = [
    { 
      field: 'customer', 
      headerName: 'Customer Name', 
      width: '180px', 
      sortable: true, 
      filterable: true,
      isExpanderColumn: true,
      headerClassName: 'bg-purple-50 font-bold'
    },
    { field: 'email', headerName: 'Email', width: '200px', sortable: true, filterable: true, headerClassName: 'bg-purple-50' },
    { 
      field: 'address.city', // Nested field access
      headerName: 'City', 
      width: '120px', 
      sortable: true, 
      filterable: true,
      headerClassName: 'bg-purple-50'
    },
    { 
      field: 'address.country', // Nested field access
      headerName: 'Country', 
      width: '120px', 
      sortable: true, 
      filterable: true,
      headerClassName: 'bg-purple-50'
    },
    { 
      field: 'phoneNumbers.phone1', // Nested field access
      headerName: 'Phone', 
      width: '140px', 
      sortable: true,
      headerClassName: 'bg-purple-50'
    }
  ];

  // API orders child columns
  const apiOrderChildColumns: ZGridColumn[] = [
    { field: 'orderNumber', headerName: 'Order Number', width: '140px', sortable: true },
    { 
      field: 'total', 
      headerName: 'Total', 
      width: '100px', 
      sortable: true,
      cellRenderer: (value) => `$${value}`
    },
    { field: 'date', headerName: 'Date', width: '120px', sortable: true },
    { 
      field: 'status', 
      headerName: 'Status', 
      width: '120px', 
      cellRenderer: statusBadge
    }
  ];

  // Bulk actions
  const userBulkActions = (
    <div className="flex items-center space-x-2">
      <AppButton size="sm" variant="outline">
        <Edit className="w-4 h-4 mr-1" />
        Edit Selected
      </AppButton>
      <AppButton size="sm" variant="outline">
        <Trash2 className="w-4 h-4 mr-1" />
        Delete Selected
      </AppButton>
    </div>
  );

  const ticketBulkActions = (
    <div className="flex items-center space-x-2">
      <AppButton size="sm" variant="outline">
        <Copy className="w-4 h-4 mr-1" />
        Assign
      </AppButton>
      <AppButton size="sm" variant="outline">
        <Edit className="w-4 h-4 mr-1" />
        Update Status
      </AppButton>
    </div>
  );

  return (
    <div className="space-y-8">
      <SectionHeader
        title="ZGrid Examples"
        description="Professional data grid component built like tickets table with advanced features"
      />

      {/* Advanced Users Table with Custom Styling */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Advanced Users Table (Custom Header Styling)</AppCardTitle>
        </AppCardHeader>
        <AppCardContent>
          <ZGrid
            columns={userColumns}
            data={usersData}
            pagination={true}
            pageSize={5}
            height="500px"
            selectable={true}
            selectedItems={selectedUsers}
            onSelectionChange={setSelectedUsers}
            onRowClick={(row) => console.log('Row clicked:', row)}
            onRowDoubleClick={(row) => console.log('Row double clicked:', row)}
            bulkActions={userBulkActions}
            // Custom styling
            headerClassName="bg-gradient-to-r from-blue-50 to-indigo-50 border-b-2 border-blue-200"
            headerStyle={{ background: 'linear-gradient(90deg, #f0f9ff 0%, #e0e7ff 100%)' }}
            rowClassName={(row, index) => 
              row.status === 'Inactive' 
                ? 'bg-red-50 text-red-900' 
                : index % 2 === 0 
                  ? 'bg-gray-50' 
                  : 'bg-white'
            }
            searchClassName="bg-gradient-to-r from-gray-50 to-blue-50"
            paginationClassName="bg-gradient-to-r from-blue-50 to-gray-50"
            // Filter options
            filterPosition="header"
            sortIcons="arrows"
            emptyState={{
              title: "No users found",
              description: "There are no users matching your current filters.",
              action: {
                label: "Add New User",
                onClick: () => console.log('Add user'),
                icon: <Plus className="w-4 h-4 mr-1" />
              }
            }}
          />
        </AppCardContent>
      </AppCard>

      {/* Support Tickets with Below Filters */}
      <AppCard>
        <AppCardHeader>
          <div className="flex items-center justify-between">
            <div>
              <AppCardTitle>Support Tickets (Filters Below Headers)</AppCardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                {ticketsData.length} tickets total • {selectedTickets.length} selected
              </p>
            </div>
          </div>
        </AppCardHeader>
        <AppCardContent>
          <ZGrid
            columns={ticketColumns}
            data={ticketsData}
            pagination={true}
            pageSize={10}
            selectable={true}
            selectedItems={selectedTickets}
            onSelectionChange={setSelectedTickets}
            onRowClick={(row) => console.log('Ticket clicked:', row)}
            onRowDoubleClick={(row) => console.log('Open ticket:', row)}
            bulkActions={ticketBulkActions}
            // Filter position below headers (default)
            filterPosition="below"
            sortIcons="chevrons"
            // Custom row styling based on priority
            rowStyle={(row) => ({
              borderLeft: row.priority === 'High' 
                ? '4px solid #ef4444' 
                : row.priority === 'Medium'
                  ? '4px solid #f59e0b'
                  : '4px solid #10b981'
            })}
            emptyState={{
              title: "No tickets found",
              description: "There are no tickets matching your current search.",
              action: {
                label: "Create New Ticket",
                onClick: () => console.log('Create ticket'),
                icon: <Plus className="w-4 h-4 mr-1" />
              }
            }}
          />
        </AppCardContent>
      </AppCard>

      {/* Custom Row Styling Example */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Custom Row Styling (Zebra Stripes + Status Colors)</AppCardTitle>
        </AppCardHeader>
        <AppCardContent>
          <ZGrid
            columns={userColumns.slice(0, -1)} // Remove actions column
            data={usersData}
            pagination={true}
            pageSize={8}
            selectable={false}
            showFilters={true}
            filterPosition="header"
            sortIcons="arrows"
            // Zebra stripes with status-based colors
            rowClassName={(row, index) => {
              const baseClass = index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
              if (row.status === 'Active') return `${baseClass} border-l-4 border-green-400`
              if (row.status === 'Inactive') return `${baseClass} border-l-4 border-red-400`
              if (row.status === 'Pending') return `${baseClass} border-l-4 border-yellow-400`
              return baseClass
            }}
            headerClassName="bg-gray-800 text-white"
            onRowDoubleClick={(row) => console.log('Custom row double clicked:', row)}
          />
        </AppCardContent>
      </AppCard>

      {/* Nested/Hierarchical Data Example 1 - Simple Nesting */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Nested Data Example - Customers & Orders</AppCardTitle>
        </AppCardHeader>
        <AppCardContent>
          <ZGrid
            columns={nestedCustomerColumns}
            data={nestedCustomersData}
            pagination={true}
            pageSize={10}
            selectable={true}
            selectedItems={selectedUsers}
            onSelectionChange={setSelectedUsers}
            // Nested configuration
            nested={{
              childrenField: 'children', // Field that contains child data
              childColumns: orderChildColumns, // Custom columns for child rows
              maxDepth: 5,
              defaultExpanded: false,
              childRowClassName: 'bg-muted/30 border-l-2 border-muted-foreground/20',
              indentSize: 25
            }}
            filterPosition="below"
            sortIcons="arrows"
            onRowDoubleClick={(row) => console.log('Nested row double clicked:', row)}
          />
        </AppCardContent>
      </AppCard>

      {/* API-like Nested Data Example */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Complex API Data - Nested Objects & Arrays</AppCardTitle>
        </AppCardHeader>
        <AppCardContent>
          <ZGrid
            columns={apiCustomerColumns}
            data={apiCustomersData}
            pagination={true}
            pageSize={5}
            selectable={true}
            selectedItems={selectedTickets}
            onSelectionChange={setSelectedTickets}
            // Advanced nested configuration
            nested={{
              childrenField: 'orders', // API orders array
              childColumns: apiOrderChildColumns,
              maxDepth: 3,
              defaultExpanded: false,
              childRowClassName: 'bg-muted/20 border-l-2 border-muted-foreground/10',
              indentSize: 30
            }}
            filterPosition="header"
            sortIcons="chevrons"
            bulkActions={ticketBulkActions}
            onRowClick={(row) => console.log('API nested row clicked:', row)}
          />
        </AppCardContent>
      </AppCard>

      {/* Auto-generated Child Columns Example */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Auto-Generated Child Columns (No childColumns provided)</AppCardTitle>
        </AppCardHeader>
        <AppCardContent>
          <ZGrid
            columns={nestedCustomerColumns}
            data={nestedCustomersData}
            pagination={true}
            pageSize={8}
            // No childColumns provided - will auto-generate from data
            nested={{
              childrenField: 'children',
              // childColumns: undefined, // Auto-generate from child data
              maxDepth: 2,
              childRowClassName: 'bg-muted/20',
              indentSize: 20
            }}
            showFilters={false}
          />
        </AppCardContent>
      </AppCard>

      {/* Simple Deep Nesting Example */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Deep Nesting Example - Multiple Levels</AppCardTitle>
        </AppCardHeader>
        <AppCardContent>
          <div className="space-y-4">
            <div className="text-sm text-muted-foreground">
              This example shows customers → orders → products (3 levels deep)
            </div>
            <ZGrid
              columns={[
                { 
                  field: 'customer', 
                  headerName: 'Entity', 
                  width: '250px', 
                  isExpanderColumn: true
                },
                { field: 'total', headerName: 'Value', width: '120px', cellRenderer: (value) => value ? `$${value}` : '-' },
                { field: 'status', headerName: 'Status', width: '120px', cellRenderer: statusBadge },
                { field: 'date', headerName: 'Date', width: '120px' }
              ]}
              data={[
                {
                  id: "deep-1",
                  customer: "Enterprise Corp",
                  total: 15000,
                  status: "Active", 
                  date: "2024-08-30",
                  orders: [
                    {
                      id: "deep-1-1",
                      customer: "Order #ORD-100",
                      total: 5000,
                      status: "Resolved",
                      date: "2024-08-25",
                      orders: [ // Using 'orders' field for products too (consistent field name)
                        { id: "deep-1-1-1", customer: "Laptop Pro", total: 2000, status: "Resolved", date: "2024-08-25" },
                        { id: "deep-1-1-2", customer: "Monitor 4K", total: 800, status: "Resolved", date: "2024-08-26" }
                      ]
                    },
                    {
                      id: "deep-1-2", 
                      customer: "Order #ORD-101",
                      total: 3200,
                      status: "In Progress",
                      date: "2024-08-28",
                      orders: [ // Products as 'orders' for consistency
                        { id: "deep-1-2-1", customer: "Keyboard Mech", total: 150, status: "Open", date: "2024-08-28" },
                        { id: "deep-1-2-2", customer: "Mouse Gaming", total: 80, status: "In Progress", date: "2024-08-29" }
                      ]
                    }
                  ]
                },
                {
                  id: "deep-2",
                  customer: "Tech Solutions Ltd",
                  total: 8500,
                  status: "Active", 
                  date: "2024-08-29",
                  orders: [
                    {
                      id: "deep-2-1",
                      customer: "Order #ORD-200",
                      total: 4500,
                      status: "In Progress",
                      date: "2024-08-29",
                      orders: [
                        { id: "deep-2-1-1", customer: "Server Rack", total: 3000, status: "In Progress", date: "2024-08-29" },
                        { id: "deep-2-1-2", customer: "Network Switch", total: 1500, status: "Open", date: "2024-08-29" }
                      ]
                    }
                  ]
                }
              ]}
              nested={{
                childrenField: 'orders', // Level 1: orders
                maxDepth: 10, // Allow deep nesting
                childRowClassName: 'bg-muted/20',
                indentSize: 25
              }}
              pageSize={20}
              showSearch={false}
            />
          </div>
        </AppCardContent>
      </AppCard>

      {/* Loading State Example */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Loading State</AppCardTitle>
        </AppCardHeader>
        <AppCardContent>
          <div className="space-y-4">
            <div className="flex space-x-2">
              <AppButton onClick={() => setLoading(!loading)}>
                {loading ? 'Stop Loading' : 'Show Loading'}
              </AppButton>
            </div>
            <ZGrid
              columns={userColumns}
              data={usersData}
              loading={loading}
              height="300px"
            />
          </div>
        </AppCardContent>
      </AppCard>

      {/* Empty State Example */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Empty State Example</AppCardTitle>
        </AppCardHeader>
        <AppCardContent>
          <ZGrid
            columns={ticketColumns}
            data={[]}
            height="300px"
            emptyState={{
              title: "No tickets created yet",
              description: "Get started by creating your first support ticket.",
              action: {
                label: "Create First Ticket",
                onClick: () => console.log('Create first ticket'),
                icon: <Plus className="w-4 h-4 mr-1" />
              }
            }}
          />
        </AppCardContent>
      </AppCard>
    </div>
  );
}

// Responsive Example Component
const ResponsiveExample = () => (
  <AppCard>
    <AppCardHeader>
      <AppCardTitle>📱 Responsive Design Features</AppCardTitle>
    </AppCardHeader>
    <AppCardContent>
      <div className="space-y-4">
        <div className="text-sm text-muted-foreground">
          ZGrid automatically adapts to different screen sizes:
        </div>
        <ul className="text-sm space-y-2 pl-4">
          <li className="list-disc">• <strong>Mobile:</strong> Compact padding, smaller text, abbreviated pagination</li>
          <li className="list-disc">• <strong>Tablet:</strong> Medium padding, readable text, full pagination</li>
          <li className="list-disc">• <strong>Desktop:</strong> Full padding, optimal text size, complete features</li>
          <li className="list-disc">• <strong>Columns:</strong> Minimum widths prevent overcrowding</li>
          <li className="list-disc">• <strong>Scrolling:</strong> Horizontal scroll for wide tables</li>
          <li className="list-disc">• <strong>Sticky Header:</strong> Headers remain visible when scrolling</li>
        </ul>
        <div className="bg-muted p-4 rounded-lg">
          <h4 className="font-medium mb-2">Try These Actions:</h4>
          <p className="text-sm text-muted-foreground">
            1. Resize your browser window to see responsive changes<br/>
            2. Test on mobile/tablet devices<br/>
            3. Scroll horizontally with many columns<br/>
            4. Check pagination on different screen sizes
          </p>
        </div>
      </div>
    </AppCardContent>
  </AppCard>
);
