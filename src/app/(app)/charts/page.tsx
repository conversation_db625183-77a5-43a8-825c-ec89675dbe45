"use client";

import * as React from "react";
import { Download, RefreshCw } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardT<PERSON>le,
  <PERSON>pp<PERSON>utton,
  SectionHeader,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>bs<PERSON>ist,
  TabsTrigger,
} from "@/components/ui-toolkit";
import { AppChart } from "@/components/ui-toolkit/app-chart";
import { dashboardService } from "@/lib/demo/api";

export default function ChartsPage() {
  const [chartData, setChartData] = React.useState<{
    ticketVolume: { name: string; value: number }[];
    slaCompliance: { name: string; value: number }[];
    agentWorkload: { name: string; value: number }[];
  }>({
    ticketVolume: [],
    slaCompliance: [],
    agentWorkload: [],
  });
  const [loading, setLoading] = React.useState(true);

  const refreshData = async () => {
    setLoading(true);
    try {
      const data = await dashboardService.getChartData();
      setChartData(data);
    } catch (error) {
      console.error("Failed to load chart data:", error);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    refreshData();
  }, []);

  // Additional sample data for different chart types
  const sampleData = {
    ticketTrend: [
      { name: "Week 1", tickets: 23, resolved: 20 },
      { name: "Week 2", tickets: 31, resolved: 28 },
      { name: "Week 3", tickets: 18, resolved: 22 },
      { name: "Week 4", tickets: 27, resolved: 25 },
    ],
    priorityDistribution: [
      { name: "Low", value: 45 },
      { name: "Medium", value: 28 },
      { name: "High", value: 15 },
      { name: "Urgent", value: 12 },
    ],
    departmentVolume: [
      { name: "IT Support", value: 42 },
      { name: "HR", value: 18 },
      { name: "Finance", value: 23 },
      { name: "Operations", value: 17 },
    ],
  };

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="grid gap-6 md:grid-cols-2">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-80 bg-muted rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <SectionHeader
        title="Charts & Analytics"
        description="Visual insights into your ticket system performance"
        actions={
          <div className="flex items-center space-x-2">
            <AppButton 
              variant="outline" 
              icon={<RefreshCw className="h-4 w-4" />}
              onClick={refreshData}
              loading={loading}
            >
              Refresh
            </AppButton>
            <AppButton 
              variant="outline" 
              icon={<Download className="h-4 w-4" />}
            >
              Export
            </AppButton>
          </div>
        }
      />

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <AppCard>
              <AppCardHeader>
                <AppCardTitle>Ticket Volume Over Time</AppCardTitle>
                <AppCardDescription>
                  Monthly ticket creation trends
                </AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                <AppChart
                  type="area"
                  data={chartData.ticketVolume}
                  height={300}
                  dataKey="value"
                  nameKey="name"
                />
              </AppCardContent>
            </AppCard>

            <AppCard>
              <AppCardHeader>
                <AppCardTitle>Priority Distribution</AppCardTitle>
                <AppCardDescription>
                  Breakdown of tickets by priority level
                </AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                <AppChart
                  type="pie"
                  data={sampleData.priorityDistribution}
                  height={300}
                  dataKey="value"
                  nameKey="name"
                />
              </AppCardContent>
            </AppCard>

            <AppCard>
              <AppCardHeader>
                <AppCardTitle>Agent Workload</AppCardTitle>
                <AppCardDescription>
                  Current ticket assignments by agent
                </AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                <AppChart
                  type="bar"
                  data={chartData.agentWorkload}
                  height={300}
                  dataKey="value"
                  nameKey="name"
                />
              </AppCardContent>
            </AppCard>

            <AppCard>
              <AppCardHeader>
                <AppCardTitle>Department Volume</AppCardTitle>
                <AppCardDescription>
                  Tickets by department this month
                </AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                <AppChart
                  type="bar"
                  data={sampleData.departmentVolume}
                  height={300}
                  dataKey="value"
                  nameKey="name"
                />
              </AppCardContent>
            </AppCard>
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
            <AppCard className="lg:col-span-2">
              <AppCardHeader>
                <AppCardTitle>Ticket Creation vs Resolution Trend</AppCardTitle>
                <AppCardDescription>
                  Weekly comparison of tickets created vs resolved
                </AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                <AppChart
                  type="line"
                  data={sampleData.ticketTrend}
                  height={400}
                  dataKey="tickets"
                  nameKey="name"
                />
              </AppCardContent>
            </AppCard>

            <AppCard>
              <AppCardHeader>
                <AppCardTitle>SLA Compliance Rate</AppCardTitle>
                <AppCardDescription>
                  Percentage of tickets resolved within SLA
                </AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                <AppChart
                  type="pie"
                  data={chartData.slaCompliance}
                  height={300}
                  dataKey="value"
                  nameKey="name"
                />
              </AppCardContent>
            </AppCard>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <AppCard>
              <AppCardHeader>
                <AppCardTitle>Resolution Time Trend</AppCardTitle>
                <AppCardDescription>
                  Average time to resolve tickets over time
                </AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                <AppChart
                  type="line"
                  data={[
                    { name: "Jan", value: 4.2 },
                    { name: "Feb", value: 3.8 },
                    { name: "Mar", value: 4.1 },
                    { name: "Apr", value: 3.5 },
                    { name: "May", value: 3.9 },
                    { name: "Jun", value: 3.2 },
                  ]}
                  height={300}
                  dataKey="value"
                  nameKey="name"
                />
              </AppCardContent>
            </AppCard>

            <AppCard>
              <AppCardHeader>
                <AppCardTitle>First Response Time</AppCardTitle>
                <AppCardDescription>
                  Average first response time by month
                </AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                <AppChart
                  type="bar"
                  data={[
                    { name: "Jan", value: 2.1 },
                    { name: "Feb", value: 1.8 },
                    { name: "Mar", value: 2.0 },
                    { name: "Apr", value: 1.5 },
                    { name: "May", value: 1.9 },
                    { name: "Jun", value: 1.4 },
                  ]}
                  height={300}
                  dataKey="value"
                  nameKey="name"
                />
              </AppCardContent>
            </AppCard>

            <AppCard className="md:col-span-2">
              <AppCardHeader>
                <AppCardTitle>Customer Satisfaction Trend</AppCardTitle>
                <AppCardDescription>
                  Monthly customer satisfaction ratings
                </AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                <AppChart
                  type="area"
                  data={[
                    { name: "Jan", value: 4.2 },
                    { name: "Feb", value: 4.1 },
                    { name: "Mar", value: 4.3 },
                    { name: "Apr", value: 4.5 },
                    { name: "May", value: 4.4 },
                    { name: "Jun", value: 4.6 },
                  ]}
                  height={300}
                  dataKey="value"
                  nameKey="name"
                />
              </AppCardContent>
            </AppCard>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
