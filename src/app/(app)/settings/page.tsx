"use client";

import * as React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
  <PERSON>bsContent,
} from "@/components/ui-toolkit";
import { toast } from "sonner";
import GeneralTab from "./tabs/general";
import BrandingsTab from "./tabs/brandings";
import NotificationsTab from "./tabs/notifications";
import SecurityTab from "./tabs/security";

export default function SettingsPage() {
  const [saving, setSaving] = React.useState(false);

  const handleSave = async () => {
    setSaving(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    toast.success("Settings saved successfully!");
    setSaving(false);
  };

  return (
    <div className="space-y-4 p-4">
      <SectionHeader
        title="Settings"
        description="Manage your organization settings and preferences"
      />

      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="brandings">Brandings</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <GeneralTab />
        </TabsContent>

        <TabsContent value="brandings">
          <BrandingsTab />
        </TabsContent>

        <TabsContent value="notifications">
          <NotificationsTab />
        </TabsContent>

        <TabsContent value="security">
          <SecurityTab />
        </TabsContent>
      </Tabs>

      {/* Save Button */}
      {/* <div className="flex justify-end">
        <AppButton
          onClick={handleSave}
          loading={saving}
          className="min-w-32"
        >
          Save Changes
        </AppButton>
      </div> */}
    </div>
  );
}
