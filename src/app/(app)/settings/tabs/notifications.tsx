"use client";

import {
  App<PERSON>ard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
} from "@/components/ui-toolkit";

export default function NotificationsTab() {
  return (
    <div className="space-y-6">
      {/* Notifications Settings */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Notifications</AppCardTitle>
          <AppCardDescription>
            Configure how and when you receive notifications
          </AppCardDescription>
        </AppCardHeader>
        <AppCardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>Email Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive email updates for ticket activities
                </p>
              </div>
              <Select defaultValue="important">
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All notifications</SelectItem>
                  <SelectItem value="important">Important only</SelectItem>
                  <SelectItem value="none">None</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <Label>Desktop Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Show browser notifications for real-time updates
                </p>
              </div>
              <Select defaultValue="enabled">
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="enabled">Enabled</SelectItem>
                  <SelectItem value="disabled">Disabled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <Label>SLA Breach Alerts</Label>
                <p className="text-sm text-muted-foreground">
                  Get notified before tickets breach their SLA
                </p>
              </div>
              <Select defaultValue="30min">
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1hour">1 hour before</SelectItem>
                  <SelectItem value="30min">30 minutes before</SelectItem>
                  <SelectItem value="15min">15 minutes before</SelectItem>
                  <SelectItem value="disabled">Disabled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </AppCardContent>
      </AppCard>
    </div>
  );
}
