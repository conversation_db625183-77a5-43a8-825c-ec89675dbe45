"use client";

import * as React from "react";
import {
  App<PERSON>ard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppInput,
  AppButton,
} from "@/components/ui-toolkit";
import { toast } from "sonner";
import axiosInstance from "@/lib/axios";

export default function SecurityTab() {
  const [mfaEnabled, setMfaEnabled] = React.useState(false);
  const [showSetup, setShowSetup] = React.useState(false);
  const [qrCode, setQrCode] = React.useState("");
  const [secret, setSecret] = React.useState("");
  const [verificationCode, setVerificationCode] = React.useState("");
  const [loading, setLoading] = React.useState(false);

  React.useEffect(() => {
    checkMFAStatus();
  }, []);

  const checkMFAStatus = async () => {
    try {
      const response = await axiosInstance.get("/auth/profile", {
        skipAuthRedirect: true,
      } as any);
      const data = response.data as any;
      if (data.success) {
        setMfaEnabled(data.data.user.mfa?.enabled || false);
      }
    } catch (error) {
      console.error("Failed to check MFA status:", error);
    }
  };

  const handleEnableMFA = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.post("/auth/mfa/setup");
      const data = response.data as any;
      if (data.success) {
        setQrCode(data.data.qrCodeUrl);
        setSecret(data.data.secret);
        setShowSetup(true);
      } else {
        toast.error(data.message || "Failed to setup MFA");
      }
    } catch (error) {
      toast.error("Failed to setup MFA");
    }
    setLoading(false);
  };

  const handleVerifyAndEnable = async () => {
    if (!verificationCode) {
      toast.error("Please enter verification code");
      return;
    }

    setLoading(true);
    try {
      const response = await axiosInstance.post("/auth/mfa/enable", {
        code: verificationCode,
      });
      const data = response.data as any;
      if (data.success) {
        setMfaEnabled(true);
        setShowSetup(false);
        setVerificationCode("");
        toast.success("2FA enabled successfully");
      } else {
        toast.error(data.message || "Failed to enable 2FA");
      }
    } catch (error) {
      toast.error("Failed to enable 2FA");
    }
    setLoading(false);
  };

  const handleDisableMFA = async () => {
    const password = prompt("Enter your password to disable 2FA:");
    if (!password) return;

    setLoading(true);
    try {
      const response = await axiosInstance.post("/auth/mfa/disable", {
        password,
      });
      const data = response.data as any;
      if (data.success) {
        setMfaEnabled(false);
        setShowSetup(false);
        toast.success("2FA disabled successfully");
      } else {
        toast.error(data.message || "Failed to disable 2FA");
      }
    } catch (error) {
      toast.error("Failed to disable 2FA");
    }
    setLoading(false);
  };

  return (
    <div className="space-y-6">
      {/* Two-Factor Authentication */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Two-Factor Authentication (2FA)</AppCardTitle>
          <AppCardDescription>
            Add an extra layer of security to your account by enabling 2FA
          </AppCardDescription>
        </AppCardHeader>
        <AppCardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Enable 2FA</p>
              <p className="text-sm text-muted-foreground">
                {mfaEnabled ? "2FA is currently enabled" : "2FA is currently disabled"}
              </p>
            </div>
            <div className="flex items-center gap-2">
              {mfaEnabled ? (
                <AppButton
                  variant="outline"
                  onClick={handleDisableMFA}
                  loading={loading}
                >
                  Disable 2FA
                </AppButton>
              ) : (
                <AppButton
                  onClick={handleEnableMFA}
                  loading={loading}
                >
                  Enable 2FA
                </AppButton>
              )}
            </div>
          </div>

          {showSetup && (
            <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
              <h4 className="font-medium">Setup 2FA</h4>
              <p className="text-sm text-muted-foreground">
                Scan the QR code below with your authenticator app (Google Authenticator, Authy, etc.)
              </p>
              {qrCode && (
                <div className="flex justify-center">
                  <img src={qrCode} alt="2FA QR Code" className="w-48 h-48" />
                </div>
              )}
              <div className="space-y-2">
                <p className="text-sm font-medium">Manual entry code:</p>
                <code className="text-xs bg-background p-2 rounded block">
                  {secret}
                </code>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Enter verification code:</label>
                <AppInput
                  type="text"
                  placeholder="000000"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  maxLength={6}
                />
              </div>
              <div className="flex gap-2">
                <AppButton onClick={handleVerifyAndEnable} loading={loading}>
                  Verify & Enable
                </AppButton>
                <AppButton
                  variant="outline"
                  onClick={() => setShowSetup(false)}
                >
                  Cancel
                </AppButton>
              </div>
            </div>
          )}
        </AppCardContent>
      </AppCard>
    </div>
  );
}