"use client";

import * as React from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Plus, Edit } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  AppInput,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
  AppTextarea,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui-toolkit";
import { useThemeAccent, type AccentTheme } from "@/components/theme-provider";
import LogoUploader from "@/components/ui/LogoUploader";
import { useGetBrandingListQuery, useCreateBrandingMutation, useUpdateBrandingMutation } from "@/redux/slices/branding";
import { toast } from "sonner";
import type { BrandingItem, CreateUpdateBrandingRequest } from "@/types/branding";

const accentThemes: { value: AccentTheme; label: string; color: string }[] = [
  { value: "blue", label: "Blue", color: "bg-blue-500" },
  { value: "lightBlue", label: "Light Blue", color: "bg-sky-500" },
  { value: "darkBlue", label: "Dark Blue", color: "bg-blue-800" },
  { value: "cyan", label: "Cyan", color: "bg-cyan-500" },
  { value: "teal", label: "Teal", color: "bg-teal-500" },
  { value: "turquoise", label: "Turquoise", color: "bg-cyan-400" },
  { value: "green", label: "Green", color: "bg-green-500" },
  { value: "emerald", label: "Emerald", color: "bg-emerald-500" },
  { value: "lime", label: "Lime", color: "bg-lime-500" },
  { value: "yellow", label: "Yellow", color: "bg-yellow-500" },
  { value: "amber", label: "Amber", color: "bg-amber-500" },
  { value: "orange", label: "Orange", color: "bg-orange-500" },
  { value: "deepOrange", label: "Deep Orange", color: "bg-orange-700" },
  { value: "red", label: "Red", color: "bg-red-500" },
  { value: "rose", label: "Rose", color: "bg-rose-500" },
  { value: "pink", label: "Pink", color: "bg-pink-500" },
  { value: "fuchsia", label: "Fuchsia", color: "bg-fuchsia-500" },
  { value: "purple", label: "Purple", color: "bg-purple-500" },
  { value: "deepPurple", label: "Deep Purple", color: "bg-purple-700" },
  { value: "indigo", label: "Indigo", color: "bg-indigo-500" },
  { value: "violet", label: "Violet", color: "bg-violet-500" },
  { value: "magenta", label: "Magenta", color: "bg-fuchsia-600" },
  { value: "brown", label: "Brown", color: "bg-amber-700" },
  { value: "gray", label: "Gray", color: "bg-gray-500" },
  { value: "coolGray", label: "Cool Gray", color: "bg-slate-500" },
  { value: "warmGray", label: "Warm Gray", color: "bg-stone-500" },
  { value: "slate", label: "Slate", color: "bg-slate-500" },
  { value: "stone", label: "Stone", color: "bg-stone-500" },
];

interface BrandingFormData {
  tenantId: string;
  customColors: boolean;
  colors: string;
  tagline: string;
  description: string;
  featureHighlights: string[];
  image?: File;
}

function BrandingForm({ branding, onClose }: { branding?: BrandingItem; onClose: () => void }) {
  const [formData, setFormData] = React.useState<BrandingFormData>(() => {
    // Get tenantId from user data in localStorage
    const getTenantId = () => {
      try {
        const userData = localStorage.getItem('user');
        if (userData) {
          const user = JSON.parse(userData);
          console.log('Extracted tenantId from user data:', user.tenantId);
          return user.tenantId || '';
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
      // Fallback to direct tenantId if user data not found
      const fallbackTenantId = localStorage.getItem('tenantId') || '';
      console.log('Using fallback tenantId:', fallbackTenantId);
      return fallbackTenantId;
    };

    const tenantId = getTenantId();
    
    // If we have branding from props (API), use that
    if (branding) {
      return {
        tenantId: branding.tenantId || tenantId,
        customColors: branding.customColors,
        colors: branding.colors,
        tagline: branding.tagline,
        description: branding.description,
        featureHighlights: branding.featureHighlights || [],
      };
    }
    
    // Otherwise, load from localStorage if no branding prop is provided
    const storedBranding = localStorage.getItem('tickflo-branding');
    if (storedBranding) {
      try {
        const parsed = JSON.parse(storedBranding);
        return {
          tenantId: tenantId,
          customColors: parsed.customColors || false,
          colors: typeof parsed.colors === 'object' ? JSON.stringify(parsed.colors) : parsed.colors || '',
          tagline: parsed.tagline || '',
          description: parsed.description || '',
          featureHighlights: [],
        };
      } catch (error) {
        console.error('Error parsing stored branding:', error);
      }
    }
    
    // Default empty form
    return {
      tenantId: tenantId,
      customColors: false,
      colors: '',
      tagline: '',
      description: '',
      featureHighlights: [],
    };
  });
  const [image, setImage] = React.useState<File | null>(null);
  const [featureHighlight, setFeatureHighlight] = React.useState('');

  const [createBranding, { isLoading: isCreating }] = useCreateBrandingMutation();
  const [updateBranding, { isLoading: isUpdating }] = useUpdateBrandingMutation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const data: CreateUpdateBrandingRequest = {
        ...formData,
        image: image || undefined,
      };

      console.log('Submitting branding data:', data); // Debug log

      if (branding) {
        console.log('Updating branding with ID:', branding._id); // Debug log
        await updateBranding({ id: branding._id, data }).unwrap();
        toast.success('Branding updated successfully!');
      } else {
        await createBranding(data).unwrap();
        toast.success('Branding created successfully!');
      }

      // Refresh localStorage data
      const updatedStored = localStorage.getItem('tickflo-branding');
      if (updatedStored) {
        // Trigger a re-render by updating the parent component
        window.dispatchEvent(new CustomEvent('brandingUpdated'));
      }

      onClose();
    } catch (error: any) {
      console.error('Branding submission error:', error); // Debug log
      toast.error(error?.data?.message || 'Failed to save branding');
    }
  };

  const addFeatureHighlight = () => {
    if (featureHighlight.trim()) {
      setFormData(prev => ({
        ...prev,
        featureHighlights: [...prev.featureHighlights, featureHighlight.trim()]
      }));
      setFeatureHighlight('');
    }
  };

  const removeFeatureHighlight = (index: number) => {
    setFormData(prev => ({
      ...prev,
      featureHighlights: prev.featureHighlights.filter((_, i) => i !== index)
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="tagline">Tagline</Label>
        <AppInput
          id="tagline"
          value={formData.tagline}
          onChange={(e) => setFormData(prev => ({ ...prev, tagline: e.target.value }))}
          placeholder="Enter tagline"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <AppTextarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Enter description"
          required
        />
      </div>

      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="customColors"
            checked={formData.customColors}
            onChange={(e) => setFormData(prev => ({ ...prev, customColors: e.target.checked }))}
            className="rounded border-gray-300"
          />
          <Label htmlFor="customColors">Use Custom Colors</Label>
        </div>
      </div>

      {formData.customColors && (
        <div className="space-y-2">
          <Label htmlFor="colors">Select Color Theme</Label>
          <Select
            value={formData.colors}
            onValueChange={(value) => setFormData(prev => ({ ...prev, colors: value }))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a color theme" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="blue">Blue</SelectItem>
              <SelectItem value="lightBlue">Light Blue</SelectItem>
              <SelectItem value="darkBlue">Dark Blue</SelectItem>
              <SelectItem value="cyan">Cyan</SelectItem>
              <SelectItem value="teal">Teal</SelectItem>
              <SelectItem value="turquoise">Turquoise</SelectItem>
              <SelectItem value="green">Green</SelectItem>
              <SelectItem value="emerald">Emerald</SelectItem>
              <SelectItem value="lime">Lime</SelectItem>
              <SelectItem value="yellow">Yellow</SelectItem>
              <SelectItem value="amber">Amber</SelectItem>
              <SelectItem value="orange">Orange</SelectItem>
              <SelectItem value="deepOrange">Deep Orange</SelectItem>
              <SelectItem value="red">Red</SelectItem>
              <SelectItem value="rose">Rose</SelectItem>
              <SelectItem value="pink">Pink</SelectItem>
              <SelectItem value="fuchsia">Fuchsia</SelectItem>
              <SelectItem value="purple">Purple</SelectItem>
              <SelectItem value="deepPurple">Deep Purple</SelectItem>
              <SelectItem value="indigo">Indigo</SelectItem>
              <SelectItem value="violet">Violet</SelectItem>
              <SelectItem value="magenta">Magenta</SelectItem>
              <SelectItem value="brown">Brown</SelectItem>
              <SelectItem value="gray">Gray</SelectItem>
              <SelectItem value="coolGray">Cool Gray</SelectItem>
              <SelectItem value="warmGray">Warm Gray</SelectItem>
              <SelectItem value="slate">Slate</SelectItem>
              <SelectItem value="stone">Stone</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}

      <div className="space-y-2">
        <Label>Feature Highlights</Label>
        <div className="flex gap-2">
          <AppInput
            value={featureHighlight}
            onChange={(e) => setFeatureHighlight(e.target.value)}
            placeholder="Add feature highlight"
          />
          <AppButton type="button" onClick={addFeatureHighlight} size="sm">
            Add
          </AppButton>
        </div>
        <div className="space-y-1">
          {formData.featureHighlights.map((highlight, index) => (
            <div key={index} className="flex items-center justify-between bg-muted p-2 rounded">
              <span className="text-sm">{highlight}</span>
              <AppButton
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeFeatureHighlight(index)}
              >
                ×
              </AppButton>
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="image">Image</Label>
        <input
          id="image"
          type="file"
          accept="image/*"
          onChange={(e) => setImage(e.target.files?.[0] || null)}
          className="block w-full text-sm text-muted-foreground file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/80"
        />
      </div>

      <div className="flex justify-end gap-2">
        <AppButton type="button" variant="outline" onClick={onClose}>
          Cancel
        </AppButton>
        <AppButton type="submit" loading={isCreating || isUpdating}>
          {branding ? 'Update' : 'Create'} Branding
        </AppButton>
      </div>
    </form>
  );
}

export default function BrandingsTab() {
  const { accent, setAccent, isDark, setIsDark } = useThemeAccent();

  // Get tenantId from user data in localStorage
  const getTenantId = () => {
    try {
      const userData = localStorage.getItem('user');
      if (userData) {
        const user = JSON.parse(userData);
        console.log('BrandingsTab - Extracted tenantId from user data:', user.tenantId);
        return user.tenantId || '';
      }
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
    }
    // Fallback to direct tenantId if user data not found
    const fallbackTenantId = localStorage.getItem('tenantId') || '';
    console.log('BrandingsTab - Using fallback tenantId:', fallbackTenantId);
    return fallbackTenantId;
  };

  const tenantId = getTenantId();

  const { data: brandingList, isLoading, error, refetch } = useGetBrandingListQuery(
    { tenantId },
    { skip: !tenantId }
  );

  // Get current branding from localStorage
  const [currentBranding, setCurrentBranding] = React.useState<any>(null);

  const loadCurrentBranding = React.useCallback(() => {
    const stored = localStorage.getItem('tickflo-branding');
    if (stored) {
      try {
        setCurrentBranding(JSON.parse(stored));
      } catch (error) {
        console.error('Error parsing current branding:', error);
      }
    } else {
      setCurrentBranding(null);
    }
  }, []);

  React.useEffect(() => {
    loadCurrentBranding();
    
    // Listen for branding updates
    const handleBrandingUpdate = () => {
      loadCurrentBranding();
    };
    
    window.addEventListener('brandingUpdated', handleBrandingUpdate);
    
    return () => {
      window.removeEventListener('brandingUpdated', handleBrandingUpdate);
    };
  }, [loadCurrentBranding]);

  // Check if branding already exists (from API or localStorage)
  const existingBranding = brandingList?.data?.branding?.[0];
  const hasBrandingFromAPI = !!existingBranding && brandingList?.data?.branding?.length > 0;
  const hasBrandingFromStorage = !!currentBranding;
  const hasBranding = hasBrandingFromAPI || hasBrandingFromStorage;

  return (
    <div className="space-y-6">
      {/* Current Active Branding */}
      {currentBranding && (
        <AppCard>
          <AppCardHeader>
            <AppCardTitle>Current Active Branding</AppCardTitle>
            <AppCardDescription>
              This is the branding currently active in your application
            </AppCardDescription>
          </AppCardHeader>
          <AppCardContent>
            <div className="border rounded-lg p-4 bg-green-50 dark:bg-green-950/20">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-green-800 dark:text-green-200">{currentBranding.tagline}</h3>
                  <p className="text-sm text-green-600 dark:text-green-300 mt-1">{currentBranding.description}</p>
                  {currentBranding.url && (
                    <img 
                      src={currentBranding.url} 
                      alt="Current branding" 
                      className="w-16 h-16 object-cover rounded mt-2"
                    />
                  )}
                  <div className="flex items-center gap-2 mt-2">
                    <span className="text-xs bg-green-100 dark:bg-green-900 px-2 py-1 rounded">
                      Custom Colors: {currentBranding.customColors ? 'Yes' : 'No'}
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <span className="text-xs text-muted-foreground">Active</span>
                </div>
              </div>
            </div>
          </AppCardContent>
        </AppCard>
      )}

      {/* Branding Management */}
      <AppCard>
        <AppCardHeader>
          <div className="flex items-center justify-between">
            <div>
              <AppCardTitle>Branding Management</AppCardTitle>
              <AppCardDescription>
                {hasBranding
                  ? "Update your organization's branding settings"
                  : "Create your organization's branding settings"
                }
              </AppCardDescription>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <AppButton>
                  {hasBranding ? (
                    <>
                      <Edit className="h-4 w-4 mr-2" />
                      Update Branding
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Branding
                    </>
                  )}
                </AppButton>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>
                    {hasBranding ? 'Update Branding' : 'Create New Branding'}
                  </DialogTitle>
                  <DialogDescription>
                    {hasBranding 
                      ? 'Update your branding configuration.' 
                      : 'Create your first branding configuration.'
                    }
                  </DialogDescription>
                </DialogHeader>
                <BrandingForm
                  branding={existingBranding || (hasBrandingFromStorage ? {
                    _id: currentBranding?.brandingId || '',
                    tenantId: tenantId,
                    customColors: currentBranding?.customColors || false,
                    colors: currentBranding?.colors || '',
                    tagline: currentBranding?.tagline || '',
                    description: currentBranding?.description || '',
                    url: currentBranding?.url || '',
                    featureHighlights: [],
                    isActive: true,
                    createdBy: '',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    __v: 0,
                    id: currentBranding?.brandingId || ''
                  } : undefined)}
                  onClose={() => {
                    refetch(); // Refetch data after form submission
                  }}
                />
              </DialogContent>
            </Dialog>
          </div>
        </AppCardHeader>
        <AppCardContent>
          {isLoading ? (
            <div className="text-center py-4">Loading branding...</div>
          ) : error ? (
            <div className="text-center py-4 text-red-500">Error loading branding</div>
          ) : hasBranding ? (
            <div className="space-y-4">
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">{existingBranding?.tagline || currentBranding?.tagline || 'No tagline'}</h3>
                    <p className="text-sm text-muted-foreground">{existingBranding?.description || currentBranding?.description || 'No description'}</p>
                    <div className="flex gap-2 mt-2">
                      {(existingBranding?.featureHighlights || []).map((highlight, index) => (
                        <span key={index} className="text-xs bg-muted px-2 py-1 rounded">
                          {highlight}
                        </span>
                      ))}
                    </div>
                    <div className="flex items-center gap-2 mt-2">
                      <span className="text-xs bg-blue-100 dark:bg-blue-900 px-2 py-1 rounded">
                        Status: {existingBranding?.isActive ? 'Active' : 'Inactive'}
                      </span>
                      {existingBranding?.createdAt && (
                        <span className="text-xs bg-gray-100 dark:bg-gray-900 px-2 py-1 rounded">
                          Created: {new Date(existingBranding.createdAt).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    {existingBranding?.updatedAt && (
                      <span className="text-xs text-muted-foreground">
                        Last updated: {new Date(existingBranding.updatedAt).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No branding configuration found. Create your first branding to get started.
            </div>
          )}
        </AppCardContent>
      </AppCard>
      {/* Appearance Settings */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle className="flex items-center space-x-2">
            <Palette className="h-5 w-5" />
            <span>Appearance</span>
          </AppCardTitle>
          <AppCardDescription>
            Customize the look and feel of your workspace
          </AppCardDescription>
        </AppCardHeader>
        <AppCardContent className="space-y-6">
          {/* Theme Mode */}
          <div className="space-y-3">
            <Label>Theme Mode</Label>
            <div className="grid grid-cols-3 gap-2">
              <AppButton
                variant={!isDark ? "solid" : "outline"}
                size="sm"
                onClick={() => setIsDark(false)}
                className="justify-start"
              >
                <Sun className="h-4 w-4 mr-2" />
                Light
              </AppButton>
              <AppButton
                variant={isDark ? "solid" : "outline"}
                size="sm"
                onClick={() => setIsDark(true)}
                className="justify-start"
              >
                <Moon className="h-4 w-4 mr-2" />
                Dark
              </AppButton>
              <AppButton
                variant="outline"
                size="sm"
                className="justify-start"
                onClick={() => {
                  const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
                  setIsDark(prefersDark);
                }}
              >
                <Monitor className="h-4 w-4 mr-2" />
                System
              </AppButton>
            </div>
          </div>

          <Separator />

          {/* Accent Color */}
          <div className="space-y-3">
            <Label>Accent Color</Label>
            <p className="text-sm text-muted-foreground">
              Choose your preferred accent color for buttons, links, and highlights
            </p>
            <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-7 lg:grid-cols-6 xl:grid-cols-6 gap-2">
              {accentThemes.map((theme) => (
                <AppButton
                  key={theme.value}
                  variant={accent === theme.value ? "solid" : "outline"}
                  size="sm"
                  onClick={() => setAccent(theme.value)}
                  className="flex flex-col items-center space-y-1 h-auto py-2 px-1"
                >
                  <div className={`w-3 h-3 rounded-full ${theme.color}`} />
                  <span className="text-[10px] leading-tight text-center">{theme.label}</span>
                </AppButton>
              ))}
            </div>
          </div>

          <Separator />

          {/* Preview */}
          {/* <div className="space-y-3">
            <Label>Preview</Label>
            <div className="p-4 rounded-lg border bg-card space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Sample UI Elements</span>
                <AppButton size="sm">Primary Button</AppButton>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-accent"></div>
                <span className="text-sm">Accent color indicator</span>
              </div>
              <div className="text-xs text-muted-foreground">
                This shows how your selected theme will appear in the interface
              </div>
            </div>
          </div> */}
        </AppCardContent>
      </AppCard>
    </div>
  );
}
