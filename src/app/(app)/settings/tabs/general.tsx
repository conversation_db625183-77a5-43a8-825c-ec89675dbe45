"use client";

import * as React from "react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppInput,
  Label,
} from "@/components/ui-toolkit";

export default function GeneralTab() {
  const [tenantName, setTenantName] = React.useState("");

  React.useEffect(() => {
    const storedTenantName = localStorage.getItem("tenantName");
    if (storedTenantName) {
      setTenantName(storedTenantName);
    }
  }, []);

  return (
    <div className="space-y-6">
      {/* General Settings */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>General</AppCardTitle>
          <AppCardDescription>
            Basic organization information and configuration
          </AppCardDescription>
        </AppCardHeader>
        <AppCardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="tenant-name">Organization Name</Label>
            <AppInput
              id="tenant-name"
              value={tenantName}
              onChange={(e) => setTenantName(e.target.value)}
              placeholder="Enter organization name"
            />
          </div>
        </AppCardContent>
      </AppCard>
    </div>
  );
}
