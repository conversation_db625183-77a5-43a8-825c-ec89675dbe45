"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { Plus, Building2, Edit, Trash2, Users, Settings } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  SectionHeader,
  FilterBar,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  AppBadge,
  KebabActions,
  AppEmpty,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  AppInput,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Label,
  AppStat,
} from "@/components/ui-toolkit";
import { 
  useGetDepartmentsQuery,
  useGetDepartmentByIdQuery,
  useCreateDepartmentMutation,
  useUpdateDepartmentMutation,
  useDeleteDepartmentMutation,
} from "@/redux/slices/departments";
import { type Department } from "@/redux/slices/departments/departmentSlice";
import { toast } from "sonner";

export default function DepartmentsPage() {
  const router = useRouter();
  const { data: departments = [], isLoading } = useGetDepartmentsQuery();
  React.useEffect(() => {
    console.log('Departments hook data:', departments);
  }, [departments]);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [statusFilter, setStatusFilter] = React.useState("all");
  const [createDialogOpen, setCreateDialogOpen] = React.useState(false);

  // Add edit state
  const [editingDepartmentId, setEditingDepartmentId] = React.useState<string | null>(null);
  const [editDialogOpen, setEditDialogOpen] = React.useState(false);

  // Fetch department details when editing
  const { data: editingDepartmentData, isLoading: loadingDepartmentDetails } = useGetDepartmentByIdQuery(
    editingDepartmentId!, 
    { skip: !editingDepartmentId }
  );

  // Edit form state
  const [editName, setEditName] = React.useState("");
  const [editDescription, setEditDescription] = React.useState("");
  const [editStatus, setEditStatus] = React.useState<"active" | "inactive">("active");
  const [editEmail, setEditEmail] = React.useState("");
  const [editPhone, setEditPhone] = React.useState("");
  const [editManager, setEditManager] = React.useState("");
  const [editLocation, setEditLocation] = React.useState("");
  const [editBudget, setEditBudget] = React.useState("");
  const [editNotes, setEditNotes] = React.useState("");

  const [createDepartment, { isLoading: creating }] = useCreateDepartmentMutation();
  const [deleteDepartment] = useDeleteDepartmentMutation();
  const [updateDepartment, { isLoading: updating }] = useUpdateDepartmentMutation();

  // Create form state
  const [name, setName] = React.useState("");
  const [description, setDescription] = React.useState("");
  const [status, setStatus] = React.useState<"active" | "inactive">("active");
  const [email, setEmail] = React.useState("");
  const [phone, setPhone] = React.useState("");
  const [manager, setManager] = React.useState("");
  const [location, setLocation] = React.useState("");
  const [budget, setBudget] = React.useState("");
  const [notes, setNotes] = React.useState("");

  // Update form when department data is loaded
  React.useEffect(() => {
    if (editingDepartmentData) {
      console.log('Editing department data:', editingDepartmentData);
      setEditName(editingDepartmentData.name);
      setEditDescription(editingDepartmentData.description || "");
      setEditStatus(editingDepartmentData.status);
      setEditEmail(editingDepartmentData.additionalInfo?.email || "");
      setEditPhone(editingDepartmentData.additionalInfo?.phone || "");
      setEditManager(editingDepartmentData.additionalInfo?.manager || "");
      setEditLocation(editingDepartmentData.additionalInfo?.location || "");
      setEditBudget(editingDepartmentData.additionalInfo?.budget?.toString() || "");
      setEditNotes(editingDepartmentData.additionalInfo?.notes || "");
    }
  }, [editingDepartmentData]);
  // const filteredDepartments = React.useMemo(() => {
  //   return departments.filter(dept => {
  //     const matchesSearch = dept.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
  //                         (dept.description || "").toLowerCase().includes(searchQuery.toLowerCase());
  //     const matchesStatus = statusFilter === "all" || dept.status === statusFilter;
  //     return matchesSearch && matchesStatus;
  //   });
  // }, [departments, searchQuery, statusFilter]);

  const handleCreateDepartment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) {
      toast.error("Please fill in the department name");
      return;
    }

    try {
      await createDepartment({
        name: name.trim(),
        description: description.trim(),
        status,
        additionalInfo: {
          email: email.trim() || undefined,
          phone: phone.trim() || undefined,
          manager: manager.trim() || undefined,
          location: location.trim() || undefined,
          budget: budget ? parseFloat(budget) : undefined,
          notes: notes.trim() || undefined,
        }
      }).unwrap();

      toast.success("Department created successfully");
      setCreateDialogOpen(false);

      // Reset form
      setName("");
      setDescription("");
      setStatus("active");
      setEmail("");
      setPhone("");
      setManager("");
      setLocation("");
      setBudget("");
      setNotes("");
    } catch {
      toast.error("Failed to create department");
    }
  };

  const handleDeleteDepartment = async (departmentId: string) => {
    try {
      await deleteDepartment(departmentId).unwrap();
      toast.success("Department deleted successfully");
    } catch (error) {
      console.error("Failed to delete department:", error);
      toast.error("Failed to delete department");
    }
  };

  const handleEditDepartment = (department: Department) => {
    setEditingDepartmentId(department.id || null);
    setEditDialogOpen(true);
  };

  const handleUpdateDepartment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingDepartmentId || !editName.trim()) {
      toast.error("Please fill in the department name");
      return;
    }

    try {
      await updateDepartment({
        id: editingDepartmentId,
        data: {
          name: editName.trim(),
          description: editDescription.trim(),
          status: editStatus,
          additionalInfo: {
            email: editEmail.trim() || undefined,
            phone: editPhone.trim() || undefined,
            manager: editManager.trim() || undefined,
            location: editLocation.trim() || undefined,
            budget: editBudget ? parseFloat(editBudget) : undefined,
            notes: editNotes.trim() || undefined,
          }
        }
      }).unwrap();
      
      toast.success("Department updated successfully");
      setEditDialogOpen(false);
      setEditingDepartmentId(null);
    } catch {
      toast.error("Failed to update department");
    }
  };

  const handleCloseEditDialog = () => {
    setEditDialogOpen(false);
    setEditingDepartmentId(null);
    setEditName("");
    setEditDescription("");
    setEditStatus("active");
    setEditEmail("");
    setEditPhone("");
    setEditManager("");
    setEditLocation("");
    setEditBudget("");
    setEditNotes("");
  };

  const filteredDepartments = React.useMemo(() => {
    return departments.filter(dept => {
      const matchesSearch = dept.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          (dept.description || "").toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = statusFilter === "all" || dept.status === statusFilter;
      return matchesSearch && matchesStatus;
    });
  }, [departments, searchQuery, statusFilter]);

  const getDepartmentActions = (department: Department) => [
    {
      label: "View Details",
      onClick: () => router.push(`/departments/${department.id}`),
      icon: <Settings className="h-4 w-4" />,
    },
    {
      label: "Manage Users",
      onClick: () => router.push(`/departments/${department.id}`),
      icon: <Users className="h-4 w-4" />,
    },
    {
      label: "Edit",
      onClick: (e?: React.MouseEvent) => {
        e?.stopPropagation();
        handleEditDepartment(department);
      },
      icon: <Edit className="h-4 w-4" />,
    },
    {
      label: "Delete",
      onClick: (e?: React.MouseEvent) => {
        e?.stopPropagation();
        if (department.id) {
          handleDeleteDepartment(department.id);
        }
      },
      icon: <Trash2 className="h-4 w-4" />,
      variant: "destructive" as const,
      separator: true,
    },
  ];

  const statusOptions = [
    { value: "all", label: "All Status" },
    { value: "active", label: "Active" },
    { value: "inactive", label: "Inactive" },
  ];

  if (isLoading) {
    return (
      <div className="space-y-6 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="grid gap-4 md:grid-cols-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <SectionHeader
        title="Departments"
        description="Organize your team with departments and manage access"
        actions={
          <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
            <DialogTrigger asChild>
              <AppButton icon={<Plus className="h-4 w-4" />}>
                Create Department
              </AppButton>
            </DialogTrigger>
            <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Department</DialogTitle>
                <DialogDescription>
                  Add a new department with complete details
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleCreateDepartment} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <AppInput
                    label="Department Name *"
                    placeholder="e.g., Customer Success"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    required
                  />
                  
                  <div className="space-y-2">
                    <Label>Status</Label>
                    <Select value={status} onValueChange={(value: string) => setStatus(value as "active" | "inactive")}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <AppInput
                  label="Description"
                  placeholder="Brief description of the department"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />

                <div className="grid grid-cols-2 gap-4">
                  <AppInput
                    label="Email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  />
                  
                  <AppInput
                    label="Phone"
                    type="tel"
                    placeholder="+****************"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <AppInput
                    label="Manager"
                    placeholder="Department manager name"
                    value={manager}
                    onChange={(e) => setManager(e.target.value)}
                  />
                  
                  <AppInput
                    label="Location"
                    placeholder="Office location"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                  />
                </div>

                <AppInput
                  label="Budget"
                  type="number"
                  placeholder="Annual budget"
                  value={budget}
                  onChange={(e) => setBudget(e.target.value)}
                />

                <div className="space-y-2">
                  <Label>Notes</Label>
                  <textarea
                    className="w-full min-h-[80px] px-3 py-2 border border-input bg-background rounded-md"
                    placeholder="Additional notes about the department"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    maxLength={1000}
                  />
                </div>
                
                <div className="flex justify-end space-x-2 pt-4">
                  <AppButton
                    type="button"
                    variant="outline"
                    onClick={() => setCreateDialogOpen(false)}
                  >
                    Cancel
                  </AppButton>
                  <AppButton type="submit" loading={creating}>
                    Create Department
                  </AppButton>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        }
      />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <AppStat
          title="Total Departments"
          value={departments.length}
          icon={<Building2 className="h-4 w-4" />}
          description="All departments"
        />
        <AppStat
          title="Active Departments"
          value={departments.filter(d => d.status === "active").length}
          icon={<Building2 className="h-4 w-4" />}
          description="Currently operational"
        />
        <AppStat
          title="Total Members"
          value={departments.reduce((sum, d) => sum + d.memberCount, 0)}
          icon={<Users className="h-4 w-4" />}
          description="Across all departments"
        />
      </div>

      <div className="flex items-center justify-between gap-4">
        <FilterBar
          searchValue={searchQuery}
          onSearchChange={setSearchQuery}
          searchPlaceholder="Search departments..."
        />
        
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            {statusOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <AppCard>
        <AppCardHeader>
          <AppCardTitle>All Departments</AppCardTitle>
          <AppCardDescription>
            {filteredDepartments.length} department{filteredDepartments.length !== 1 ? 's' : ''} total
          </AppCardDescription>
        </AppCardHeader>
        <AppCardContent>
          {filteredDepartments.length === 0 ? (
            <AppEmpty
              icon={<Building2 className="h-12 w-12" />}
              title="No departments found"
              description={
                searchQuery || statusFilter !== "all"
                  ? "No departments match your search criteria."
                  : "Create your first department to organize your team."
              }
              action={{
                label: "Create First Department",
                onClick: () => setCreateDialogOpen(true),
                icon: <Plus className="h-4 w-4" />,
              }}
            />
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Department</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Members</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDepartments.map((department) => (
                  <TableRow 
                    key={department.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => router.push(`/departments/${department.id}`)}
                  >
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded bg-accent text-accent-foreground">
                          <Building2 className="h-4 w-4" />
                        </div>
                        <div className="font-medium">{department.name}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-muted-foreground text-sm">
                        {department.description || "No description"}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span>{department.memberCount}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <AppBadge 
                        variant={department.status === "active" ? "default" : "secondary"}
                      >
                        {department.status}
                      </AppBadge>
                    </TableCell>
                    <TableCell onClick={(e) => e.stopPropagation()}>
                      <KebabActions items={getDepartmentActions(department)} />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </AppCardContent>
      </AppCard>

      {/* Add edit dialog after create dialog */}
      <Dialog open={editDialogOpen} onOpenChange={handleCloseEditDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Department</DialogTitle>
            <DialogDescription>
              Update department details
            </DialogDescription>
          </DialogHeader>
          
          {loadingDepartmentDetails ? (
            <div className="space-y-4">
              <div className="h-4 bg-muted rounded animate-pulse"></div>
              <div className="h-4 bg-muted rounded animate-pulse"></div>
              <div className="h-4 bg-muted rounded animate-pulse"></div>
            </div>
          ) : (
            <form onSubmit={handleUpdateDepartment} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <AppInput
                  label="Department Name *"
                  placeholder="e.g., Customer Success"
                  value={editName}
                  onChange={(e) => setEditName(e.target.value)}
                  required
                />
                
                <div className="space-y-2">
                  <Label>Status</Label>
                  <Select value={editStatus} onValueChange={(value: string) => setEditStatus(value as "active" | "inactive")}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <AppInput
                label="Description"
                placeholder="Brief description of the department"
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
              />

              <div className="grid grid-cols-2 gap-4">
                <AppInput
                  label="Email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={editEmail}
                  onChange={(e) => setEditEmail(e.target.value)}
                />
                
                <AppInput
                  label="Phone"
                  type="tel"
                  placeholder="+****************"
                  value={editPhone}
                  onChange={(e) => setEditPhone(e.target.value)}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <AppInput
                  label="Manager"
                  placeholder="Department manager name"
                  value={editManager}
                  onChange={(e) => setEditManager(e.target.value)}
                />
                
                <AppInput
                  label="Location"
                  placeholder="Office location"
                  value={editLocation}
                  onChange={(e) => setEditLocation(e.target.value)}
                />
              </div>

              <AppInput
                label="Budget"
                type="number"
                placeholder="Annual budget"
                value={editBudget}
                onChange={(e) => setEditBudget(e.target.value)}
              />

              <div className="space-y-2">
                <Label>Notes</Label>
                <textarea
                  className="w-full min-h-[80px] px-3 py-2 border border-input bg-background rounded-md"
                  placeholder="Additional notes about the department"
                  value={editNotes}
                  onChange={(e) => setEditNotes(e.target.value)}
                  maxLength={1000}
                />
              </div>
              
              <div className="flex justify-end space-x-2 pt-4">
                <AppButton
                  type="button"
                  variant="outline"
                  onClick={handleCloseEditDialog}
                >
                  Cancel
                </AppButton>
                <AppButton type="submit" loading={updating}>
                  Update Department
                </AppButton>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
