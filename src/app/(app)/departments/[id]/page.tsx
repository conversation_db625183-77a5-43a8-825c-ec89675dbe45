"use client";

import * as React from "react";
import { useRouter, useParams } from "next/navigation";
import { ArrowLeft, Edit, Trash2, Users, Building2, Settings } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  AppBadge,
  AppStat,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  AppInput,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Label,
  AppTextarea,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui-toolkit";
import { departmentService, userService } from "@/lib/demo/api";
import { type Department, type User } from "@/lib/demo/types";
import { toast } from "sonner";
import { useGetDepartmentByIdQuery } from "@/redux/slices/departments";

export default function DepartmentDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const departmentId = params.id as string;

  const [department, setDepartment] = React.useState<Department | null>(null);
  const [departmentUsers, setDepartmentUsers] = React.useState<User[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [editDialogOpen, setEditDialogOpen] = React.useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [updating, setUpdating] = React.useState(false);
  const [deleting, setDeleting] = React.useState(false);
  const [editEmail, setEditEmail] = React.useState("");
  const [editPhone, setEditPhone] = React.useState("");
  const [editManager, setEditManager] = React.useState("");
  const [editLocation, setEditLocation] = React.useState("");
  const [editBudget, setEditBudget] = React.useState("");
  const [editNotes, setEditNotes] = React.useState("");
  const [editAdditionalInfo, setEditAdditionalInfo] = React.useState("");

  
  // Edit form state
  const [editName, setEditName] = React.useState("");
  const [editDescription, setEditDescription] = React.useState("");
  const [editStatus, setEditStatus] = React.useState<Department["status"]>("active");
   
  // Fetch department details when editing
  const { data: editingDepartmentData, isLoading: loadingDepartmentDetails } = useGetDepartmentByIdQuery
  (
    departmentId,
    { skip: !departmentId }
  );
  React.useEffect(() => {
    if (editingDepartmentData) {
      console.log("Editing department data:", editingDepartmentData);

      setDepartment(editingDepartmentData as Department);
  
      // Main fields
      setEditName(editingDepartmentData.name || "");
      setEditDescription(editingDepartmentData.description || "");
      setEditStatus(editingDepartmentData.status || "active");
  
      // Additional Info fields
      setEditEmail(editingDepartmentData.additionalInfo?.email || "");
      setEditPhone(editingDepartmentData.additionalInfo?.phone || "");
      setEditManager(editingDepartmentData.additionalInfo?.manager || "");
      setEditLocation(editingDepartmentData.additionalInfo?.location || "");
      setEditBudget(editingDepartmentData.additionalInfo?.budget?.toString() || "");
      setEditNotes(editingDepartmentData.additionalInfo?.notes || "");
    }
  }, [editingDepartmentData, departmentId]);
  console.log(
    "setDepartment",department
  )
  // React.useEffect(() => {
  //   const loadData = async () => {
  //     try {
  //       const [departmentsData, usersData] = await Promise.all([
  //         departmentService.getAll(),
  //         userService.getAll(),
  //       ]);
        
  //       const deptData = departmentsData.find(d => d.id === departmentId);
  //       if (!deptData) {
  //         throw new Error("Department not found");
  //       }
        
  //       setDepartment(deptData);
        
  //       // Filter users for this department
  //       const deptUsers = usersData.filter(u => u.department.id === departmentId);
  //       setDepartmentUsers(deptUsers);
        
  //       // Initialize edit form
  //       setEditName(deptData.name);
  //       setEditDescription(deptData.description || "");
  //       setEditStatus(deptData.status);
  //     } catch (error) {
  //       console.error("Failed to load department:", error);
  //       toast.error("Failed to load department details");
  //       // router.push("/departments");
  //     } finally {
  //       setLoading(false);
  //     }
  //   };

  //   if (departmentId) {
  //     loadData();
  //   }
  // }, [departmentId, router]);

  const handleUpdateDepartment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!department) return;

    setUpdating(true);
    try {
      // Since we don't have update in the API, we'll simulate it
      const updatedDept = {
        ...department,
        name: editName.trim(),
        description: editDescription.trim(),
        status: editStatus,
      };
      
      setDepartment(updatedDept);
      toast.success("Department updated successfully");
      setEditDialogOpen(false);
    } catch (error) {
      console.error("Failed to update department:", error);
      toast.error("Failed to update department");
    } finally {
      setUpdating(false);
    }
  };

  const handleDeleteDepartment = async () => {
    if (!department) return;

    setDeleting(true);
    try {
      // Since we don't have delete in the API, we'll simulate it
      toast.success("Department deleted successfully");
      router.push("/departments");
    } catch (error) {
      console.error("Failed to delete department:", error);
      toast.error("Failed to delete department");
    } finally {
      setDeleting(false);
    }
  };

  if (loadingDepartmentDetails) {
    return (
      <div className="space-y-6 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (!department) {
    return (
      <div className="space-y-6 p-6">
        <div className="text-center">
          <p className="text-muted-foreground">Department not found</p>
          <AppButton onClick={() => router.push("/departments")} className="mt-4">
            Back to Departments
          </AppButton>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <AppButton
            variant="outline"
            size="sm"
            onClick={() => router.push("/departments")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Departments
          </AppButton>
          
          <div className="flex items-center space-x-4">
            <div className="h-12 w-12 bg-muted rounded-lg flex items-center justify-center">
              <Building2 className="h-6 w-6 text-muted-foreground" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">{department.name}</h1>
              <p className="text-muted-foreground">{department.description || "No description"}</p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
            <DialogTrigger asChild>
              <AppButton variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </AppButton>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Department</DialogTitle>
                <DialogDescription>
                  Update the department details below.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleUpdateDepartment} className="space-y-4">
                <div>
                  <Label>Name</Label>
                  <AppInput
                    value={editName}
                    onChange={(e) => setEditName(e.target.value)}
                    placeholder="Department name..."
                    required
                  />
                </div>
                
                <div>
                  <Label className="mb-2 block">Description</Label>
                  <AppTextarea
                    value={editDescription}
                    onChange={(e) => setEditDescription(e.target.value)}
                    placeholder="Department description..."
                    rows={3}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Email</Label>
                  <AppInput
                    value={editEmail}
                    onChange={(e) => setEditEmail(e.target.value)}
                    placeholder="Department email..."
                  />
                </div>
                <div className="space-y-2">
                  <Label>Phone</Label>
                  <AppInput
                    value={editPhone}
                    onChange={(e) => setEditPhone(e.target.value)}
                    placeholder="Department phone..."
                  />
                </div>
                <div className="space-y-2">
                  <Label>Manager</Label>
                  <AppInput
                    value={editManager}
                    onChange={(e) => setEditManager(e.target.value)}
                    placeholder="Department manager..."
                  />
                </div>
                <div className="space-y-2">
                  <Label>Location</Label>
                  <AppInput
                    value={editLocation}
                    onChange={(e) => setEditLocation(e.target.value)}
                    placeholder="Department location..."
                  />
                </div>
                <div className="space-y-2">
                  <Label>Budget</Label>
                  <AppInput
                    value={editBudget}
                    onChange={(e) => setEditBudget(e.target.value)}
                    placeholder="Department budget..."
                  />
                </div>
                <div className="space-y-2">
                  <Label>Notes</Label>
                  <AppTextarea
                    value={editNotes}
                    onChange={(e) => setEditNotes(e.target.value)}
                    placeholder="Department notes..."
                    rows={3}
                  />
                </div>
                
                
                <div>
                  <Label className="mb-2 block">Status</Label>
                  <Select value={editStatus} onValueChange={(value: Department["status"]) => setEditStatus(value)}>
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex justify-end space-x-2 pt-4">
                  <AppButton
                    type="button"
                    variant="outline"
                    onClick={() => setEditDialogOpen(false)}
                  >
                    Cancel
                  </AppButton>
                  <AppButton type="submit" loading={updating}>
                    Update Department
                  </AppButton>
                </div>
              </form>
            </DialogContent>
          </Dialog>

          <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
            <DialogTrigger asChild>
              <AppButton variant="destructive" size="sm">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </AppButton>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete Department</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete this department? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-end space-x-2 pt-4">
                <AppButton
                  variant="outline"
                  onClick={() => setDeleteDialogOpen(false)}
                >
                  Cancel
                </AppButton>
                <AppButton
                  variant="destructive"
                  onClick={handleDeleteDepartment}
                  loading={deleting}
                >
                  Delete Department
                </AppButton>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <AppStat
          title="Total Members"
          value={departmentUsers.length}
          icon={<Users className="h-4 w-4" />}
        />
        <AppStat
          title="Active Members"
          value={departmentUsers.filter(u => u.status === "active").length}
          icon={<Users className="h-4 w-4" />}
        />
        <AppStat
          title="Open Tickets"
          value="12"
          icon={<Building2 className="h-4 w-4" />}
        />
        <AppStat
          title="Avg Resolution"
          value="4.2 hrs"
          icon={<Settings className="h-4 w-4" />}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Department Information */}
          <AppCard>
            <AppCardHeader>
              <AppCardTitle>Department Information</AppCardTitle>
            </AppCardHeader>
            <AppCardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium text-muted-foreground">Department Name</span>
                  </div>
                  <p className="font-medium">{department.name}</p>
                </div>
                
                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-sm font-medium text-muted-foreground">Description</span>
                  </div>
                  <p className={department.description ? "font-medium" : "text-muted-foreground"}>
                    {department.description || "No description provided"}
                  </p>
                </div>
                
                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-sm font-medium text-muted-foreground">Status</span>
                  </div>
                  <AppBadge variant={department.status === "active" ? "default" : "secondary"}>
                    {department.status}
                  </AppBadge>
                </div>

                {department && (
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">Manager</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-6 w-6">
                        {/* <AvatarImage src={department.additionalInfo?.manager} /> */}
                        {/* <AvatarFallback>{department?.additionalInfo.manager.name.slice(0, 2)}</AvatarFallback> */}
                      </Avatar>
                      <div>
                        {/* <p className="font-medium">{department.manager.name}</p> */}
                        {/* <p className="text-xs text-muted-foreground">{department.manager.email}</p> */}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </AppCardContent>
          </AppCard>

          {/* Department Members */}
          <AppCard>
            <AppCardHeader>
              <AppCardTitle>Department Members ({departmentUsers.length})</AppCardTitle>
            </AppCardHeader>
            <AppCardContent>
              {departmentUsers.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  No members in this department yet.
                </p>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {departmentUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={user.avatar} />
                              <AvatarFallback>{user.name.slice(0, 2)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{user.name}</p>
                              <p className="text-sm text-muted-foreground">{user.email}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <AppBadge 
                            variant={user.role?.type === "system" ? "default" : "secondary"}
                            className="cursor-pointer hover:bg-accent"
                            onClick={() => user.role?.id && router.push(`/roles/${user.role.id}`)}
                          >
                            {user.role?.name}
                          </AppBadge>
                        </TableCell>
                        <TableCell>
                          <AppBadge variant={user.status === "active" ? "default" : "secondary"}>
                            {user.status}
                          </AppBadge>
                        </TableCell>
                        <TableCell>
                          <AppButton
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/users/${user.id}`)}
                          >
                            View
                          </AppButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </AppCardContent>
          </AppCard>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <AppCard>
            <AppCardHeader>
              <AppCardTitle className="text-sm">Quick Actions</AppCardTitle>
            </AppCardHeader>
            <AppCardContent className="space-y-3">
              <AppButton 
                variant="outline" 
                size="sm" 
                className="w-full justify-start"
                onClick={() => setEditDialogOpen(true)}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Department
              </AppButton>
              
              <AppButton 
                variant="outline" 
                size="sm" 
                className="w-full justify-start"
                onClick={() => router.push("/users")}
              >
                <Users className="h-4 w-4 mr-2" />
                View All Members
              </AppButton>
              
              <AppButton 
                variant="outline" 
                size="sm" 
                className="w-full justify-start text-destructive hover:bg-destructive hover:text-destructive-foreground"
                onClick={() => setDeleteDialogOpen(true)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Department
              </AppButton>
            </AppCardContent>
          </AppCard>

          {/* Recent Activity */}
          <AppCard>
            <AppCardHeader>
              <AppCardTitle className="text-sm">Recent Activity</AppCardTitle>
            </AppCardHeader>
            <AppCardContent className="space-y-4">
              <div className="text-sm">
                <p className="font-medium">User Joined</p>
                <p className="text-muted-foreground">John Doe joined the department</p>
                <p className="text-xs text-muted-foreground">2 hours ago</p>
              </div>
              
              <div className="text-sm">
                <p className="font-medium">Ticket Resolved</p>
                <p className="text-muted-foreground">5 tickets resolved today</p>
                <p className="text-xs text-muted-foreground">4 hours ago</p>
              </div>
              
              <div className="text-sm">
                <p className="font-medium">Department Updated</p>
                <p className="text-muted-foreground">Description was updated</p>
                <p className="text-xs text-muted-foreground">1 day ago</p>
              </div>
            </AppCardContent>
          </AppCard>
        </div>
      </div>
    </div>
  );
}
