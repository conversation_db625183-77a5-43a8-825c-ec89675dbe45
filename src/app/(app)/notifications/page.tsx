"use client";

import { useState } from "react";
import { <PERSON>, Check, Trash2, Eye } from "lucide-react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ge,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui-toolkit";

interface Notification {
  id: string;
  title: string;
  message: string;
  timestamp: string;
  type: 'info' | 'warning' | 'success' | 'error';
  read: boolean;
  actionUrl?: string;
}

const mockNotifications: Notification[] = [
  {
    id: "1",
    title: "New ticket assigned",
    message: "TKT-001: Cannot access email account",
    timestamp: "2m ago",
    type: "info",
    read: false,
    actionUrl: "/tickets/1"
  },
  {
    id: "2",
    title: "SLA breach warning",
    message: "TKT-003 will breach SLA in 30 minutes",
    timestamp: "5m ago",
    type: "warning",
    read: false,
    actionUrl: "/tickets/3"
  },
  {
    id: "3",
    title: "Ticket updated",
    message: "TKT-002: Customer replied to ticket",
    timestamp: "10m ago",
    type: "info",
    read: true,
    actionUrl: "/tickets/2"
  },
  {
    id: "4",
    title: "New department created",
    message: "IT Support department has been created",
    timestamp: "1h ago",
    type: "success",
    read: true
  },
  {
    id: "5",
    title: "User account suspended",
    message: "User <EMAIL> has been suspended",
    timestamp: "2h ago",
    type: "error",
    read: false
  },
  {
    id: "6",
    title: "Weekly report generated",
    message: "Your weekly performance report is ready",
    timestamp: "1 day ago",
    type: "success",
    read: true
  }
];

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState(mockNotifications);
  const [activeTab, setActiveTab] = useState("all");

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, read: true }))
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const filteredNotifications = notifications.filter(notif => {
    if (activeTab === "unread") return !notif.read;
    if (activeTab === "read") return notif.read;
    return true;
  });

  const unreadCount = notifications.filter(n => !n.read).length;

  const getNotificationVariant = (type: Notification['type']) => {
    switch (type) {
      case 'warning': return 'destructive';
      case 'error': return 'destructive';
      case 'success': return 'default';
      default: return 'secondary';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <SectionHeader 
          title="Notifications" 
          description={`You have ${unreadCount} unread notifications`}
        />
        <AppButton onClick={markAllAsRead} variant="outline" size="sm">
          <Check className="h-4 w-4 mr-2" />
          Mark all as read
        </AppButton>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">
            All ({notifications.length})
          </TabsTrigger>
          <TabsTrigger value="unread">
            Unread ({unreadCount})
          </TabsTrigger>
          <TabsTrigger value="read">
            Read ({notifications.length - unreadCount})
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4 mt-6">
          {filteredNotifications.length === 0 ? (
            <AppCard>
              <AppCardContent className="flex flex-col items-center justify-center py-12">
                <Bell className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No notifications</h3>
                <p className="text-muted-foreground text-center">
                  {activeTab === "unread" 
                    ? "You're all caught up! No unread notifications."
                    : "No notifications found."
                  }
                </p>
              </AppCardContent>
            </AppCard>
          ) : (
            <div className="space-y-3">
              {filteredNotifications.map((notification) => (
                <AppCard 
                  key={notification.id} 
                  className={`transition-all hover:shadow-md ${
                    !notification.read ? 'border-primary bg-primary/5' : ''
                  }`}
                >
                  <AppCardContent className="p-4">
                    <div className="flex items-start justify-between space-x-4">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center space-x-2">
                          <h4 className={`font-medium ${!notification.read ? 'font-semibold' : ''}`}>
                            {notification.title}
                          </h4>
                          <AppBadge 
                            variant={getNotificationVariant(notification.type)}
                            className="text-xs"
                          >
                            {notification.type}
                          </AppBadge>
                          {!notification.read && (
                            <div className="h-2 w-2 rounded-full bg-primary" />
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {notification.message}
                        </p>
                        <span className="text-xs text-muted-foreground">
                          {notification.timestamp}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {notification.actionUrl && (
                          <AppButton 
                            variant="ghost" 
                            size="sm"
                            onClick={() => window.location.href = notification.actionUrl!}
                          >
                            <Eye className="h-4 w-4" />
                          </AppButton>
                        )}
                        {!notification.read && (
                          <AppButton 
                            variant="ghost" 
                            size="sm"
                            onClick={() => markAsRead(notification.id)}
                          >
                            <Check className="h-4 w-4" />
                          </AppButton>
                        )}
                        <AppButton 
                          variant="ghost" 
                          size="sm"
                          onClick={() => deleteNotification(notification.id)}
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </AppButton>
                      </div>
                    </div>
                  </AppCardContent>
                </AppCard>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
