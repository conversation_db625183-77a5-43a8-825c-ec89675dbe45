"use client";

import * as React from "react";
import { Plus, Shield, Edit, Trash2, Users, <PERSON>, Eye } from "lucide-react";
import { useRouter } from "next/navigation";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  SectionHeader,
  FilterBar,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  AppBadge,
  KebabActions,
  AppEmpty,
  AppStat,
} from "@/components/ui-toolkit";
import { useGetRolesQuery, type Role } from "@/redux/slices/roles/roleSlice";
import { toast } from "sonner";

export default function RolesPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = React.useState("");
  const [typeFilter, setTypeFilter] = React.useState("all");

  // RTK Queries
  const { data: roles = [], isLoading: loading, error } = useGetRolesQuery();

  // Handle API errors
  React.useEffect(() => {
    if (error) {
      console.error("Failed to load roles:", error);
      toast.error("Failed to load roles");
    }
  }, [error]);

  const filteredRoles = React.useMemo(() => {
    return roles.filter(role => {
      const matchesSearch = role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          (role.description || "").toLowerCase().includes(searchQuery.toLowerCase());
      const matchesType = typeFilter === "all" || role.type === typeFilter;
      return matchesSearch && matchesType;
    });
  }, [roles, searchQuery, typeFilter]);

  const getRoleActions = (role: Role) => [
    {
      label: "View Permissions",
      onClick: () => console.log("View permissions", role.id),
      icon: <Eye className="h-4 w-4" />,
    },
    {
      label: "Edit Role",
      onClick: () => console.log("Edit", role.id),
      icon: <Edit className="h-4 w-4" />,
      disabled: role.type === "system",
    },
    {
      label: "Delete",
      onClick: () => console.log("Delete", role.id),
      icon: <Trash2 className="h-4 w-4" />,
      variant: "destructive" as const,
      separator: true,
      disabled: role.type === "system",
    },
  ];

  const typeOptions = [
    { value: "all", label: "All Types" },
    { value: "system", label: "System Roles" },
    { value: "custom", label: "Custom Roles" },
  ];

  const getPermissionCount = (role: Role) => {
    return role.permissions.length;
  };

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="grid gap-4 md:grid-cols-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <SectionHeader
        title="Roles & Permissions"
        description="Manage user roles and their associated permissions"
        actions={
          <AppButton icon={<Plus className="h-4 w-4" />} onClick={() => router.push("/roles/create")}>
            Create Role
          </AppButton>
        }
      />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <AppStat
          title="Total Roles"
          value={roles.length}
          icon={<Shield className="h-4 w-4" />}
          description="All roles"
        />
        <AppStat
          title="Custom Roles"
          value={roles.filter(r => r.type === "custom").length}
          icon={<Lock className="h-4 w-4" />}
          description="User-created roles"
        />
        <AppStat
          title="System Roles"
          value={roles.filter(r => r.type === "system").length}
          icon={<Shield className="h-4 w-4" />}
          description="Built-in roles"
        />
      </div>

      <div className="flex items-center justify-between gap-4">
        <FilterBar
          searchValue={searchQuery}
          onSearchChange={setSearchQuery}
          searchPlaceholder="Search roles..."
          style={{ flex: 0.4 }}
        />
        
   
      </div>

      <AppCard>
        <AppCardHeader>
          <AppCardTitle>All Roles</AppCardTitle>
          <AppCardDescription>
            {roles.length} role{roles.length !== 1 ? 's' : ''} total
          </AppCardDescription>
        </AppCardHeader>
        <AppCardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Role</TableHead>
                <TableHead>Key</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Permissions</TableHead>
                {/* <TableHead>Users</TableHead> */}
              </TableRow>
            </TableHeader>
            <TableBody>
              {roles.map((role) => (
                <TableRow key={role._id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded bg-accent text-accent-foreground">
                        <Shield className="h-4 w-4" />
                      </div>
                      <div className="font-medium">{role.name}</div>
                    </div>
                  </TableCell>
  
                  <TableCell>
                    <AppBadge variant="secondary">
                      {role.key}
                    </AppBadge>
                  </TableCell>
                  <TableCell>
                    <AppBadge 
                      variant={role.isActive ? "default" : "destructive"}
                    >
                      {role.isActive ? "Active" : "Inactive"}
                    </AppBadge>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {role.permissions.length}
                    </div>
                  </TableCell>
                  <TableCell>
                    {
                      role?.key=="company_owner" ||

                    <KebabActions items={getRoleActions(role)} />
                    }
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </AppCardContent>
      </AppCard>
    </div>
  );
}
