"use client";

import * as React from "react";
import { ArrowLeft, Shield } from "lucide-react";
import { useRouter } from "next/navigation";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  AppInput,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Label,
  Checkbox,
} from "@/components/ui-toolkit";
import { useCreateRoleMutation } from "@/redux/slices/roles/roleSlice";
import { toast } from "sonner";
import { useAuth } from "@/hooks/useAuth";

export default function CreateRolePage() {
  const router = useRouter();
  const { user } = useAuth();
  const [createRole, { isLoading: creating }] = useCreateRoleMutation();

  // Form state
  const [name, setName] = React.useState("");
  const [description, setDescription] = React.useState("");
  const [type, setType] = React.useState<"system" | "custom">("custom");
  const [userType, setUserType] = React.useState<"systemAdmin" | "member" | "agent">("agent");
  const [isActive, setIsActive] = React.useState(true);
  const [permissions, setPermissions] = React.useState<string[]>([]);

  const handleCreateRole = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) {
      toast.error("Please fill in the role name");
      return;
    }

    try {
      await createRole({
        name: name.trim(),
        description: description.trim(),
        type,
        userType,
        permissions,
        isActive,
      }).unwrap();

      toast.success("Role created successfully");
      router.push("/roles");
    } catch (error) {
      console.error("Failed to create role:", error);
      toast.error("Failed to create role");
    }
  };

  // Available permissions
  const availablePermissions = React.useMemo(() => {
    if (!user?.permissions) return [];

    const grouped: { [module: string]: string[] } = {};
    user.permissions.forEach(permission => {
      const parts = permission.split('.');
      const action = parts.pop()!;
      const module = parts.join('.');
      if (!grouped[module]) {
        grouped[module] = [];
      }
      if (!grouped[module].includes(action)) {
        grouped[module].push(action);
      }
    });

    return Object.entries(grouped).map(([module, actions]) => ({
      module,
      actions,
    }));
  }, [user?.permissions]);

  const handlePermissionChange = (permission: string, checked: boolean) => {
    setPermissions(prev => {
      if (checked) {
        return [...prev, permission];
      } else {
        return prev.filter(p => p !== permission);
      }
    });
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center space-x-4">
        <AppButton
          variant="ghost"
          size="sm"
          onClick={() => router.push("/roles")}
          icon={<ArrowLeft className="h-4 w-4" />}
        >
          
        </AppButton>
        <div>
          <h1 className="text-2xl font-bold">Create New Role</h1>
          <p className="text-muted-foreground">Define a new role and assign permissions</p>
        </div>
      </div>

      <AppCard>
        <AppCardHeader>
          <AppCardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Role Details</span>
          </AppCardTitle>
          <AppCardDescription>
            Configure the role settings and permissions
          </AppCardDescription>
        </AppCardHeader>
        <AppCardContent>
          <form onSubmit={handleCreateRole} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <AppInput
                label="Role Name"
                placeholder="e.g., Support Agent"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />

              <div className="space-y-2">
                <Label>Type</Label>
                <Select value={type} onValueChange={(value: string) => setType(value as "system" | "custom")}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="custom">Custom Role</SelectItem>
                    <SelectItem value="system">System Role</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Status</Label>
                <Select value={isActive ? "active" : "inactive"} onValueChange={(value: string) => setIsActive(value === "active")}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <AppInput
              label="Description"
              placeholder="Brief description of the role"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />

            <div className="space-y-4">
              <Label>Permissions</Label>

              {availablePermissions.map(({ module, actions }) => (
                <div key={module} className="border rounded-lg p-4 space-y-3">
                  <h4 className="font-medium capitalize">{module.replace('_', ' ')}</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {actions.map((action) => {
                      const permission = `${module}.${action}`;
                      return (
                        <div key={permission} className="flex items-center space-x-2">
                          <Checkbox
                            id={permission}
                            checked={permissions.includes(permission)}
                            onCheckedChange={(isChecked) =>
                              handlePermissionChange(permission, !!isChecked)
                            }
                          />
                          <Label htmlFor={permission} className="text-sm capitalize">
                            {action}
                          </Label>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <AppButton
                type="button"
                variant="outline"
                onClick={() => router.push("/roles")}
              >
                Cancel
              </AppButton>
              <AppButton type="submit" loading={creating}>
                Create Role
              </AppButton>
            </div>
          </form>
        </AppCardContent>
      </AppCard>
    </div>
  );
}