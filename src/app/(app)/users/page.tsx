"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { UserPlus, Edit, Trash2, Mail, Download, FileText } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  SectionHeader,
  FilterBar,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  AppBadge,
  KebabActions,
  AppEmpty,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  AppInput,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Label,
} from "@/components/ui-toolkit";
import { Checkbox } from "@/components/ui/checkbox";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import {
  useGetUsersQuery,
  useGetUserByIdQuery,
  useUpdateUserMutation,
  useInviteUserMutation,
  useDeleteUserMutation,
} from "@/redux/slices/users";
import { useGetRolesQuery } from "@/redux/slices/roles";
import { useGetDepartmentsQuery } from "@/redux/slices/departments";
import { type User, type Role, type Department } from "@/redux/slices/users/userSlice";
import { toast } from "sonner";

export default function UsersPage() {
  const router = useRouter();
  const [inviteDialogOpen, setInviteDialogOpen] = React.useState(false);

  const [searchQuery, setSearchQuery] = React.useState("");

  // Invite form state
  const [inviteEmail, setInviteEmail] = React.useState("");
  const [inviteName, setInviteName] = React.useState("");
  const [invitePhone, setInvitePhone] = React.useState("");
  const [inviteRole, setInviteRole] = React.useState<string>("");
  const [inviteDepartments, setInviteDepartments] = React.useState<string[]>([]);
  const [inviteUserType, setInviteUserType] = React.useState<string>("");



  // Add edit state
  const [editingUserId, setEditingUserId] = React.useState<string | null>(null);
  const [editDialogOpen, setEditDialogOpen] = React.useState(false);

  // Response modal state
  const [responseModalOpen, setResponseModalOpen] = React.useState(false);
  const [apiResponse, setApiResponse] = React.useState<any>(null);
  const { data: users = [], isLoading } = useGetUsersQuery();
  const { data: roles = [] } = useGetRolesQuery();
  const { data: departments = [] } = useGetDepartmentsQuery(undefined, { skip: !inviteDialogOpen && !editDialogOpen });
  // PDF generation function
  const generatePDF = () => {
    if (!apiResponse) return;

    const userDetails = `
USER DETAILS REPORT
==================

Basic Information:
- Name: ${apiResponse?.data?.user?.displayName || 'N/A'}
- Email: ${apiResponse?.user?.email || 'N/A'}
- Phone: ${apiResponse?.data?.phone || 'N/A'}
- Status: ${apiResponse?.data?.user?.status || 'N/A'}
- User Type: ${apiResponse?.data?.user?.userType || 'N/A'}

Role & Department:
- Role: ${apiResponse?.data?.user?.role || 'N/A'}
- Department: ${apiResponse?.data?.user?.departments?.map((d: any) => d.name).join(', ') || 'N/A'}

Account Details:
- Generated Password: ${apiResponse?.data?.user?.generatedPassword || 'N/A'}
- Created: ${apiResponse?.data?.user?.createdAt ? new Date(apiResponse?.data?.user?.createdAt).toLocaleString() : 'N/A'}
- Last Updated: ${apiResponse?.data?.user?.updatedAt ? new Date(apiResponse?.data?.user?.updatedAt).toLocaleString() : 'N/A'}

Additional Information:
- Avatar: ${apiResponse?.data?.avatar || 'N/A'}

Full API Response:
${JSON.stringify(apiResponse?.data, null, 2)}
    `;

    const blob = new Blob([userDetails], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `user-details-${apiResponse?.data?.user?.name || 'user'}-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  // Fetch user details when editing
  const { data: editingUserData, isLoading: loadingUserDetails } = useGetUserByIdQuery(
    { id: editingUserId!, tenantId: users.find(u => u.id === editingUserId)?.tenantId || 'test-tenant-id' },
    { skip: !editingUserId }
  );

  // Edit form state
  const [editName, setEditName] = React.useState("");
  const [editEmail, setEditEmail] = React.useState("");
  const [editPhone, setEditPhone] = React.useState("");
  const [editRole, setEditRole] = React.useState("");
  const [editDepartments, setEditDepartments] = React.useState<string[]>([]);
  const [edittentid, setEditTenantId] = React.useState("");

  const [editUserType, setEditUserType] = React.useState<string>("");

  // Update form when user data is loaded
  React.useEffect(() => {
    if (editingUserData) {
      console.log('Editing user data:', editingUserData);
      setEditName(editingUserData.name || "");
      setEditEmail(editingUserData.email);
      setEditRole(typeof editingUserData.role === 'object' ? editingUserData.role.name : editingUserData?.role || ""); // <-- sirf name rakho
      setEditDepartments(editingUserData.department ? [editingUserData.department._id || editingUserData.department.id || ''] : []);
      setEditPhone(editingUserData.phone || "");
      setEditUserType(editingUserData.userType || "");
      setEditTenantId(editingUserData.tenantId || "");

    }
  }, [editingUserData]);
  console.log(
    "editRole", editRole
  )
  const [updateUser, { isLoading: updating }] = useUpdateUserMutation();
  const [inviteUser, { isLoading: inviteLoading }] = useInviteUserMutation();
  const [deleteUser, { isLoading: deleting }] = useDeleteUserMutation();

  React.useEffect(() => {
    console.log('Users hook data:', users);
    console.log('Roles hook data:', roles);
    console.log('Departments hook data:', departments);
  }, [users, roles, departments]);
  const handleDeleteUser = async (userId: string) => {
    try {
      await deleteUser({
        id: userId,
        tenantId: users.find(u => u.id === userId)?.tenantId || 'test-tenant-id',
      }).unwrap();
      toast.success("User deleted successfully");
    } catch (error) {
      console.error("Failed to delete user:", error);
      toast.error("Failed to delete user");
    }
  };


  const handleEditUser = (user: User) => {
    setEditingUserId(user.id);
    setEditDialogOpen(true);
  };

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingUserId || !editName || !editEmail || !editRole || editDepartments.length === 0) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      await updateUser({
        id: editingUserId,
        tenantId: edittentid,
        data: {
          name: editName,
          email: editEmail,
          phone: editPhone,
          role: editRole,
          departments: editDepartments,
          userType: editUserType,

        }
      }).unwrap();

      toast.success("User updated successfully");
      setEditDialogOpen(false);
      setEditingUserId(null);
    } catch {
      toast.error("Failed to update user");
    }
  };

  const handleCloseEditDialog = () => {
    setEditDialogOpen(false);
    setEditingUserId(null);
    setEditName("");
    setEditEmail("");
    setEditPhone("");
    setEditRole("");
    setEditDepartments([]);
  };

  const getUserActions = (user: User) => [
    {
      label: "View Profile",
      onClick: () => router.push(`/profile/${user.id}`),
      icon: <Edit className="h-4 w-4" />,
    },
    // {
    //   label: "Send Email",
    //   onClick: () => window.open(`mailto:${user.email}`),
    //   icon: <Mail className="h-4 w-4" />,
    // },
    // {
    //   label: "Edit User",
    //   onClick: (e?: React.MouseEvent) => {
    //     e?.stopPropagation();
    //     handleEditUser(user);
    //   },
    //   icon: <Edit className="h-4 w-4" />,
    // },
    {
      label: "Delete",
      onClick: (e?: React.MouseEvent) => {
        e?.stopPropagation();
        handleDeleteUser(user.id);
      },
      icon: <Trash2 className="h-4 w-4" />,
      variant: "destructive" as const,
      separator: true,
    },
  ];

  const filteredUsers = React.useMemo(() => {
    return users.filter(user => {
      const roleName = typeof user.role === 'object' ? user.role.name : user.role || '';
      return user.name?.toLowerCase().includes(searchQuery?.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchQuery?.toLowerCase()) ||
        roleName.toLowerCase().includes(searchQuery?.toLowerCase()) ||
        user.department?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.status.toLowerCase().includes(searchQuery?.toLowerCase()) ||
        user.userType.toLowerCase().includes(searchQuery?.toLowerCase());
    });
  }, [users, searchQuery]);

  const handleInviteUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inviteEmail || !inviteName || !inviteRole || inviteDepartments.length === 0) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      const response = await inviteUser({
        email: inviteEmail,
        name: inviteName,
        phone: invitePhone,
        role: inviteRole,
        departments: inviteDepartments,
        userType: inviteUserType,
      }).unwrap();
      // Show response in modal
      setApiResponse(response);
      setResponseModalOpen(true);
      //

      toast.success(`Invitation sent to ${inviteEmail}`);
      setInviteDialogOpen(false);

      // Reset form
      setInviteEmail("");
      setInviteName("");
      setInvitePhone("");
      setInviteRole("");
      setInviteDepartments([]);
      setInviteUserType("");
    } catch (error: any) {
      console.error("API Error:", error);
      setApiResponse(error);
      setResponseModalOpen(true);
      toast.error("Failed to send invitation");
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4 p-4">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 p-4">
      <SectionHeader
        title="Users & Team Management"
        description="Manage users, roles, and team assignments"
        actions={
          <Dialog open={inviteDialogOpen} onOpenChange={setInviteDialogOpen}>
            <DialogTrigger asChild>
              <AppButton icon={<UserPlus className="h-4 w-4" />}>
                Invite User
              </AppButton>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Invite New User</DialogTitle>
                <DialogDescription>
                  Send an invitation to join your organization
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleInviteUser} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <AppInput
                    id="name"
                    placeholder="John Doe"
                    value={inviteName}
                    onChange={(e) => setInviteName(e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <AppInput
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <AppInput
                    id="phone"
                    type="tel"
                    placeholder="+****************"
                    value={invitePhone}
                    onChange={(e) => setInvitePhone(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="userType">User Type</Label>
                  <Select value={inviteUserType} onValueChange={setInviteUserType} required>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select user type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Self Service</SelectItem>
                      <SelectItem value="1">Operator</SelectItem>
                      <SelectItem value="2">Both</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <Select value={inviteRole} onValueChange={setInviteRole} required>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      {roles?.map((role) => (
                        <SelectItem key={role.id} value={role.name}>
                          {role.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="departments">Departments</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        {inviteDepartments.length > 0
                          ? departments
                              .filter(dept => inviteDepartments.includes(dept.id!))
                              .map(dept => dept.name)
                              .join(', ')
                          : "Select departments"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80 p-0">
                      <ScrollArea className="h-48">
                        <div className="p-2">
                          {departments?.filter(dept => dept.id).map((department) => (
                            <div key={department.id} className="flex items-center space-x-2 p-2 hover:bg-accent rounded">
                              <Checkbox
                                id={`invite-dept-${department.id}`}
                                checked={inviteDepartments.includes(department.id!)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setInviteDepartments([...inviteDepartments, department.id!]);
                                  } else {
                                    setInviteDepartments(inviteDepartments.filter(id => id !== department.id));
                                  }
                                }}
                              />
                              <Label htmlFor={`invite-dept-${department.id}`} className="text-sm flex-1 cursor-pointer">
                                {department.name}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </PopoverContent>
                  </Popover>
                </div>



                <div className="flex justify-end space-x-2">
                  <AppButton
                    type="button"
                    variant="outline"
                    onClick={() => setInviteDialogOpen(false)}
                  >
                    Cancel
                  </AppButton>
                  <AppButton type="submit" loading={inviteLoading}>
                    Send Invitation
                  </AppButton>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        }
      />

      <FilterBar
        searchValue={searchQuery}
        onSearchChange={setSearchQuery}
        searchPlaceholder="Search users..."
      />

      <AppCard>
        <AppCardHeader>
          <div className="flex items-center justify-between">
            <div>
              <AppCardTitle>Team Members</AppCardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                {filteredUsers.length} users total
              </p>
            </div>
          </div>
        </AppCardHeader>
        <AppCardContent>
          {filteredUsers.length === 0 ? (
            <AppEmpty
              title="No users found"
              description="There are no users matching your current filters."
              action={{
                label: "Invite First User",
                onClick: () => setInviteDialogOpen(true),
                icon: <UserPlus className="h-4 w-4" />,
              }}
            />
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>User Type</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers?.map((user) => {
                  const userRole = roles.find(r => r._id === user.role);
                  const roleName = typeof user.role === 'object' ? user.role.name : userRole?.name || user.role;
                  const roleId = typeof user.role === 'object' ? user.role._id : userRole?._id;
                  return (
                  <TableRow
                    key={user.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => router.push(`/profile/${user.id}`)}
                  >
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={user.avatar} />
                          <AvatarFallback>
                            {user?.name?.split(" ").map(n => n[0]).join("").toUpperCase() || " N/A"}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{user.name || "No Name"}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {user.email}
                    </TableCell>
                    <TableCell>
                      <AppBadge 
                        variant="secondary"
                        className="cursor-pointer hover:bg-accent"
                        onClick={() => roleId && router.push(`/roles/${roleId}`)}
                      >
                        {roleName}
                      </AppBadge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {user.department ? (
                          <AppBadge key={user.department._id} variant="outline" className="text-xs">
                            {user.department.name}
                          </AppBadge>
                        ) : 'N/A'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <AppBadge
                      >
                        {user.status}
                      </AppBadge>


                    </TableCell>
                    <TableCell>
                      <AppBadge variant="secondary">
                        {user.userType === '0' ? 'Self Service' : user.userType === '1' ? 'Operator' : user.userType === '2' ? 'Both' : 'N/A'}
                      </AppBadge>
                    </TableCell>
                    {
                      roleName == "company_owner" ? null :
                        <TableCell onClick={(e) => e.stopPropagation()}>

                          <KebabActions items={getUserActions(user)} />
                        </TableCell>
                    }

                  </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </AppCardContent>
      </AppCard>

      {/* Add edit dialog */}
      <Dialog open={editDialogOpen} onOpenChange={handleCloseEditDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user details
            </DialogDescription>
          </DialogHeader>

          {loadingUserDetails ? (
            <div className="space-y-4">
              <div className="h-4 bg-muted rounded animate-pulse"></div>
              <div className="h-4 bg-muted rounded animate-pulse"></div>
              <div className="h-4 bg-muted rounded animate-pulse"></div>
            </div>
          ) : (
            <form onSubmit={handleUpdateUser} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="editName">Full Name</Label>
                <AppInput
                  id="editName"
                  placeholder="John Doe"
                  value={editName}
                  onChange={(e) => setEditName(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="editEmail">Email Address</Label>
                <AppInput
                  id="editEmail"
                  type="email"
                  value={editEmail}
                  onChange={(e) => setEditEmail(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="editPhone">Phone Number</Label>
                <AppInput
                  id="editPhone"
                  type="tel"
                  placeholder="+****************"
                  value={editPhone}
                  onChange={(e) => setEditPhone(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="editRole">Role</Label>
                <Select value={editRole} onValueChange={setEditRole} required>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    {roles.map((role) => (
                      <SelectItem key={role.id} value={role.name}>
                        {role.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

              </div>

              <div className="space-y-2">
                <Label htmlFor="editDepartments">Departments</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      {editDepartments.length > 0
                        ? departments
                            .filter(dept => editDepartments.includes(dept.id!))
                            .map(dept => dept.name)
                            .join(', ')
                        : "Select departments"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-80 p-0">
                    <ScrollArea className="h-48">
                      <div className="p-2">
                        {departments.map((department) => (
                          <div key={department.id} className="flex items-center space-x-2 p-2 hover:bg-accent rounded">
                            <Checkbox
                              id={`edit-dept-${department.id}`}
                              checked={editDepartments.includes(department.id!)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setEditDepartments([...editDepartments, department.id!]);
                                } else {
                                  setEditDepartments(editDepartments.filter(id => id !== department.id));
                                }
                              }}
                            />
                            <Label htmlFor={`edit-dept-${department.id}`} className="text-sm flex-1 cursor-pointer">
                              {department.name}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="editUserType">User Type</Label>
                <Select value={editUserType} onValueChange={setEditUserType} required>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select user type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Self Service</SelectItem>
                    <SelectItem value="1">Operator</SelectItem>
                    <SelectItem value="2">Both</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-end space-x-2">
                <AppButton
                  type="button"
                  variant="outline"
                  onClick={handleCloseEditDialog}
                >
                  Cancel
                </AppButton>
                <AppButton type="submit" loading={updating}>
                  Update User
                </AppButton>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>

      {/* User Details Modal */}
      <Dialog open={responseModalOpen} onOpenChange={setResponseModalOpen}>
        <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              User Details
            </DialogTitle>
            <DialogDescription>
              Complete user information
            </DialogDescription>
          </DialogHeader>

          {apiResponse && (
            <div className="space-y-6">
              {/* User Profile Section */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border">
                <div className="flex items-center gap-4">
                  <div className="h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-2xl font-bold text-blue-600">
                      {apiResponse?.data?.user?.displayName?.charAt(0)?.toUpperCase() || 'U'}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">{apiResponse?.data?.user?.displayName || 'No Name'}</h3>
                    <p className="text-muted-foreground">{apiResponse?.data?.user?.email || 'No Email'}</p>
                    <div className="flex gap-2 mt-2">
                      <AppBadge variant={apiResponse?.data?.user?.status === 'active' ? 'default' : 'secondary'}>
                        {apiResponse?.data?.user?.status || 'Unknown'}
                      </AppBadge>
                      <AppBadge variant="outline">
                        {apiResponse?.data?.user?.userType || 'N/A'}
                      </AppBadge>
                    </div>
                  </div>
                </div>
              </div>

              {/* Generated Password Alert */}
              {apiResponse?.data?.generatedPassword && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                    <h4 className="font-semibold text-green-800">Generated Password</h4>
                  </div>
                  <p className="text-green-700 mt-2">
                    Please save this password securely. It won't be shown again.
                  </p>
                  <div className="mt-3 p-3 bg-white border border-green-300 rounded font-mono text-lg font-bold text-center">
                    {apiResponse?.data?.generatedPassword}
                  </div>
                </div>
              )}

              {/* User Information Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold text-lg border-b pb-2">Basic Information</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="font-medium text-muted-foreground">Full Name:</span>
                      <span>{apiResponse?.data?.user?.displayName || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium text-muted-foreground">Email:</span>
                      <span className="text-sm">{apiResponse?.data?.user?.email || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium text-muted-foreground">Phone:</span>
                      <span>{apiResponse?.data?.user?.phone || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium text-muted-foreground">Status:</span>
                      <AppBadge variant={apiResponse?.data?.user?.status === 'active' ? 'default' : 'secondary'}>
                        {apiResponse?.data?.user?.status || 'N/A'}
                      </AppBadge>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium text-muted-foreground">User Type:</span>
                      <span>{apiResponse?.data?.user?.userType || 'N/A'}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold text-lg border-b pb-2">Role & Department</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="font-medium text-muted-foreground">Role:</span>
                      <AppBadge variant="secondary">{apiResponse?.data?.user?.role || 'N/A'}</AppBadge>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium text-muted-foreground">Department:</span>
                      <span>{apiResponse?.data?.user?.departments?.map((d: any) => d.name).join(', ') || 'N/A'}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Timestamps */}
              {/* <div className="space-y-4">
                <h4 className="font-semibold text-lg border-b pb-2">Account Timeline</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex justify-between">
                    <span className="font-medium text-muted-foreground">Created:</span>
                    <span className="text-sm">
                      {apiResponse.createdAt ? new Date(apiResponse.createdAt).toLocaleString() : 'N/A'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-muted-foreground">Last Updated:</span>
                    <span className="text-sm">
                      {apiResponse.updatedAt ? new Date(apiResponse.updatedAt).toLocaleString() : 'N/A'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-muted-foreground">Last Login:</span>
                    <span className="text-sm">
                      {apiResponse.lastLogin ? new Date(apiResponse.lastLogin).toLocaleString() : 'Never'}
                    </span>
                  </div>
                </div>
              </div> */}

              {/* Raw API Response (Collapsible) */}
              {/* <details className="space-y-2">
                <summary className="font-semibold text-lg cursor-pointer hover:text-blue-600">
                  Raw API Response (Click to expand)
                </summary>
                <div className="bg-muted p-4 rounded-lg">
                  <pre className="text-xs overflow-auto max-h-64 whitespace-pre-wrap">
                    {JSON.stringify(apiResponse, null, 2)}
                  </pre>
                </div>
              </details> */}
            </div>
          )}

          <div className="flex justify-between pt-4 border-t">
            <AppButton
              variant="outline"
              onClick={generatePDF}
              icon={<Download className="h-4 w-4" />}
            >
              Download as PDF
            </AppButton>
            <AppButton onClick={() => setResponseModalOpen(false)}>
              Close
            </AppButton>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
