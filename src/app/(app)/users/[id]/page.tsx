"use client";

import * as React from "react";
import { useRouter, useParams } from "next/navigation";
import { ArrowLeft, Edit, Trash2, Mail, Building2, Shield, User as UserIcon, Ticket as TicketIcon, Clock, Star, Key } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  AppBadge,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  AppInput,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Label,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui-toolkit";
import {
  useGetUserByIdQuery,
  useUpdateUserMutation,
  useDeleteUserMutation
} from "@/redux/slices/users";
import { useGetRolesQuery } from "@/redux/slices/roles";
import { useGetDepartmentsQuery } from "@/redux/slices/departments";
import { useGetTicketsByUserQuery } from "@/services/api/tickets";
import { type User, type Role, type Department, useResetPasswordMutation } from "@/redux/slices/users/userSlice";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";

export default function UserDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;
  const { tenant } = useAuth();
  console.log('User ID:', userId);

  // Use RTK Query hooks instead of demo API
  const { data: user, isLoading: loading, error } = useGetUserByIdQuery({
    id: userId,
    tenantId: tenant?.tenantId,
  });
  const { data: roles = [] } = useGetRolesQuery();
  const { data: departments = [] } = useGetDepartmentsQuery();
  const { data: userTicketsData } = useGetTicketsByUserQuery({ userId });

  const [updateUser, { isLoading: updating }] = useUpdateUserMutation();
  const [deleteUser, { isLoading: deleting }] = useDeleteUserMutation();
  const [resetPassword, { isLoading: resetting }] = useResetPasswordMutation();

  const [editDialogOpen, setEditDialogOpen] = React.useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [resetPasswordDialogOpen, setResetPasswordDialogOpen] = React.useState(false);
  const [resetPasswordResult, setResetPasswordResult] = React.useState<{ newPassword: string; message: string } | null>(null);

  // Edit form state
  const [editName, setEditName] = React.useState("");
  const [editEmail, setEditEmail] = React.useState("");
  const [editRoleId, setEditRoleId] = React.useState("");
  const [editDepartmentId, setEditDepartmentId] = React.useState("");
  const [editStatus, setEditStatus] = React.useState<"active" | "inactive" | "suspended" | "pending_activation">("active");
  const [editUserType, setEditUserType] = React.useState<string>("");
  const [editTenantId, setEditTenantId] = React.useState<string>("");

  // Update form when user data is loaded
  React.useEffect(() => {
    if (user) {
      setEditName(user.name || "");
      setEditEmail(user.email);
      // Set role id directly from the role object
      setEditRoleId(typeof user.role === 'object' ? user.role._id || "" : user.role || "");
      // Find department by id and set its id
      if (departments.length > 0) {
        const currentDepartment = departments.find(dept => dept.id === user.department?._id);
        setEditDepartmentId(currentDepartment?.id || "");
      }
      setEditStatus(user.status);

      setEditUserType(user.userType || "");
      setEditTenantId(user.tenantId || "");
    }
  }, [user, departments]);

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    try {
      await updateUser({
        id: user.id,
        tenantId: editTenantId,
        data: {
          name: editName.trim(),
          email: editEmail.trim(),
          role: editRoleId,
          departments: editDepartmentId ? [editDepartmentId] : [],
          status: editStatus,
          userType: editUserType,
        }
      }).unwrap();

      toast.success("User updated successfully");
      setEditDialogOpen(false);
    } catch (error) {
      console.error("Failed to update user:", error);
      toast.error("Failed to update user");
    }
  };

  const handleDeleteUser = async () => {
    if (!user) return;

    try {
      await deleteUser({
        id: user.id,
        tenantId: user.tenantId ?? '',
      }).unwrap();
      toast.success("User deleted successfully");
      router.push("/users");
    } catch (error) {
      console.error("Failed to delete user:", error);
      toast.error("Failed to delete user");
    }
  };

  const handleResetPassword = async () => {
    if (!user) return;

    try {
      const result = await resetPassword({
        id: user.id,
        tenantId: user.tenantId ?? '',
      }).unwrap();
      
      setResetPasswordResult(result.data);
      toast.success("Password reset successfully");
    } catch (error) {
      console.error("Failed to reset password:", error);
      toast.error("Failed to reset password");
    }
  };

  if (loading) {
    return (
      <div className="space-y-4 p-4">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className="p-4">
        <div className="text-center">
          <h2 className="text-lg font-semibold">User not found</h2>
          <p className="text-muted-foreground">The user you're looking for doesn't exist.</p>
          <AppButton onClick={() => router.push("/users")} className="mt-4">
            Back to Users
          </AppButton>
        </div>
      </div>
    );
  }

  const userTickets = userTicketsData?.data || [];

  // Find the user's role object
  const userRole = roles.find(r => r._id === user.role);

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <AppButton
            variant="outline"
            size="sm"
            onClick={() => router.push("/users")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </AppButton>

          <div className="flex items-center space-x-4">
            <Avatar className="h-12 w-12">
              <AvatarImage src={user.avatar} />
              <AvatarFallback className="text-lg">{user.name.slice(0, 2)}</AvatarFallback>
            </Avatar>
            <div>
              <h1 className="text-2xl font-bold">{user.name}</h1>
              <p className="text-muted-foreground">{user.email}</p>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <AppButton
            variant="outline"
            size="sm"
            onClick={() => window.open(`mailto:${user.email}`)}
          >
            <Mail className="h-4 w-4 mr-2" />
            Email
          </AppButton>

          <Dialog open={resetPasswordDialogOpen} onOpenChange={(open) => {
            setResetPasswordDialogOpen(open);
            if (!open) {
              setResetPasswordResult(null);
            }
          }}>
            <DialogTrigger asChild>
              <AppButton variant="outline" size="sm">
                <Key className="h-4 w-4 mr-2" />
                Reset Password
              </AppButton>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Reset Password</DialogTitle>
                {!resetPasswordResult ? (
                  <DialogDescription>
                    Are you sure you want to reset the password for {user.name}? A new temporary password will be generated and the user will be required to change it on next login.
                  </DialogDescription>
                ) : (
                  <DialogDescription>
                    Password has been reset successfully. Please share the new credentials with the user.
                  </DialogDescription>
                )}
              </DialogHeader>
              
              {resetPasswordResult ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Username</Label>
                      <AppInput
                        value={user.name}
                        readOnly
                        className="bg-muted"
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Email</Label>
                      <AppInput
                        value={user.email}
                        readOnly
                        className="bg-muted"
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium">New Password</Label>
                      <AppInput
                        value={resetPasswordResult.newPassword}
                        readOnly
                        className="bg-muted font-mono"
                      />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2 pt-4">
                    <AppButton
                      variant="outline"
                      onClick={() => {
                        setResetPasswordDialogOpen(false);
                        setResetPasswordResult(null);
                      }}
                    >
                      Close
                    </AppButton>
                  </div>
                </div>
              ) : (
                <div className="flex justify-end space-x-2 pt-4">
                  <AppButton
                    variant="outline"
                    onClick={() => setResetPasswordDialogOpen(false)}
                  >
                    Cancel
                  </AppButton>
                  <AppButton
                    onClick={handleResetPassword}
                    loading={resetting}
                  >
                    Reset Password
                  </AppButton>
                </div>
              )}
            </DialogContent>
          </Dialog>

          <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
            <DialogTrigger asChild>
              <AppButton variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </AppButton>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit User</DialogTitle>
                <DialogDescription>
                  Update the user details below.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleUpdateUser} className="space-y-4">
                <div>
                  <Label>Name</Label>
                  <AppInput
                    value={editName}
                    onChange={(e) => setEditName(e.target.value)}
                    placeholder="Full name..."
                    required
                  />
                </div>

                <div>
                  <Label>Email</Label>
                  <AppInput
                    type="email"
                    value={editEmail}
                    onChange={(e) => setEditEmail(e.target.value)}
                    placeholder="Email address..."
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Role</Label>
                    <Select value={editRoleId} onValueChange={setEditRoleId}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {roles.filter(role => role._id && role.key !== "company_owner").map((role) => (
                          <SelectItem key={role._id!} value={role._id!}>
                            {role.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Department</Label>
                    <Select value={editDepartmentId} onValueChange={setEditDepartmentId}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {departments.filter(dept => dept.id).map((department) => (
                          <SelectItem key={department.id!} value={department.id!}>
                            {department.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label className="mb-2 block">Status</Label>
                  <Select value={editStatus} onValueChange={(value: User["status"]) => setEditStatus(value)}>
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending_activation">Pending Activation</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      {/* <SelectItem value="inactive">Inactive</SelectItem> */}
                      <SelectItem value="suspended">Suspended</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="mb-2 block">User Type</Label>
                  <Select value={editUserType} onValueChange={setEditUserType}>
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Self Service</SelectItem>
                      <SelectItem value="1">Operator</SelectItem>
                      <SelectItem value="2">Both</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <AppButton
                    type="button"
                    variant="outline"
                    onClick={() => setEditDialogOpen(false)}
                  >
                    Cancel
                  </AppButton>
                  <AppButton type="submit" loading={updating}>
                    Update User
                  </AppButton>
                </div>
              </form>
            </DialogContent>
          </Dialog>

          <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
            <DialogTrigger asChild>
              <AppButton variant="destructive" size="sm">
                <Trash2 className="h-4 w-4 mr-2" />
                Deactivate
              </AppButton>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Deactivate User</DialogTitle>
                <DialogDescription>
                  Are you sure you want to deactivate this user? This action can be reversed later.
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-end space-x-2 pt-4">
                <AppButton
                  variant="outline"
                  onClick={() => setDeleteDialogOpen(false)}
                >
                  Cancel
                </AppButton>
                <AppButton
                  variant="destructive"
                  onClick={handleDeleteUser}
                  loading={deleting}
                >
                  Deactivate User
                </AppButton>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* User Information */}
          <AppCard>
            <AppCardHeader>
              <AppCardTitle>User Information</AppCardTitle>
            </AppCardHeader>
            <AppCardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <UserIcon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">Full Name</span>
                    </div>
                    <p className="font-medium">{user.name}</p>
                  </div>

                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">Email Address</span>
                    </div>
                    <p className="font-medium">{user.email}</p>
                  </div>

                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <Shield className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">Role</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <AppBadge 
                        variant={userRole?.type === "system" ? "default" : "secondary"}
                        className="cursor-pointer hover:bg-accent"
                        onClick={() => userRole?._id && router.push(`/roles/${userRole._id}`)}
                      >
                        {userRole?.name || (typeof user.role === 'object' ? user.role.name : user.role)}
                      </AppBadge>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">Department</span>
                    </div>
                    <p className="font-medium">{user.department.name}</p>
                    {user.department.description && (
                      <p className="text-sm text-muted-foreground">{user.department.description}</p>
                    )}
                  </div>

                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm font-medium text-muted-foreground">Status</span>
                    </div>
                    <AppBadge variant={user.status === "active" ? "default" : "secondary"}>
                      {user.status}
                    </AppBadge>
                  </div>
                </div>
              </div>
            </AppCardContent>
          </AppCard>

        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <AppCard>
            <AppCardHeader>
              <AppCardTitle className="text-sm">Quick Actions</AppCardTitle>
            </AppCardHeader>
            <AppCardContent className="space-y-3">
              <AppButton
                variant="outline"
                size="sm"
                className="w-full justify-start"
                onClick={() => window.open(`mailto:${user.email}`)}
              >
                <Mail className="h-4 w-4 mr-2" />
                Send Email
              </AppButton>

              <AppButton
                variant="outline"
                size="sm"
                className="w-full justify-start"
                onClick={() => setEditDialogOpen(true)}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Profile
              </AppButton>

              <AppButton
                variant="outline"
                size="sm"
                className="w-full justify-start text-destructive hover:bg-destructive hover:text-destructive-foreground"
                onClick={() => setDeleteDialogOpen(true)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Deactivate User
              </AppButton>
            </AppCardContent>
          </AppCard>

          {/* Activity Summary */}
          <AppCard>
            <AppCardHeader>
              <AppCardTitle className="text-sm">Activity Summary</AppCardTitle>
            </AppCardHeader>
            <AppCardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Total Tickets</span>
                <span className="font-medium">{userTickets.length}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Open Tickets</span>
                <span className="font-medium">
                  {userTickets.filter(t => ['new', 'triage', 'in_progress', 'waiting'].includes(t.status)).length}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Resolved Tickets</span>
                <span className="font-medium">
                  {userTickets.filter(t => ['resolved', 'closed'].includes(t.status)).length}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Avg Response Time</span>
                <span className="font-medium">2.4 hrs</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Last Active</span>
                <span className="font-medium">2 hours ago</span>
              </div>
            </AppCardContent>
          </AppCard>
        </div>
      </div>

      {/* User Tickets Section */}
      {userTickets.length > 0 && (
        <AppCard>
          <AppCardHeader>
            <AppCardTitle>User Tickets ({userTickets.length})</AppCardTitle>
          </AppCardHeader>
          <AppCardContent>
            <Tabs defaultValue="all" className="w-full">
              <TabsList>
                <TabsTrigger value="all">All Tickets</TabsTrigger>
                <TabsTrigger value="open">Open ({userTickets.filter(t => ['new', 'triage', 'in_progress', 'waiting'].includes(t.status)).length})</TabsTrigger>
                <TabsTrigger value="resolved">Resolved ({userTickets.filter(t => ['resolved', 'closed'].includes(t.status)).length})</TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="space-y-4 mt-4">
                {userTickets?.map((ticket) => (
                  <div key={ticket._id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{ticket.ticketKey}</span>
                        {/* <AppBadge variant={ticket.status === 'new' ? 'destructive' : 'secondary'}>
                          {ticket.status}
                        </AppBadge>
                        <AppBadge variant={ticket.priority === 'urgent' || ticket.priority === 'high' ? 'destructive' : 'secondary'}>
                          {ticket.priority}
                        </AppBadge> */}
                      </div>
                      <h4 className="font-medium">{ticket.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        {/* Created {ticket.createdAt.toLocaleDateString()} • Department: {ticket.department.name} */}
                      </p>
                    </div>
                    <AppButton
                      variant="ghost"
                      size="sm"
                      onClick={() => router.push(`/tickets/${ticket._id}`)}
                    >
                      View
                    </AppButton>
                  </div>
                ))}
              </TabsContent>

              <TabsContent value="open" className="space-y-4 mt-4">
                {userTickets.filter(t => ['new', 'triage', 'in_progress', 'waiting'].includes(t.status)).map((ticket) => (
                  <div key={ticket._id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{ticket.ticketKey}</span>
                        <AppBadge variant="destructive">
                          {ticket.status}
                        </AppBadge>
                        {/* <AppBadge variant={ticket.priority === 'urgent' || ticket.priority === 'high' ? 'destructive' : 'secondary'}>
                          {ticket.priority}
                        </AppBadge> */}
                      </div>
                      <h4 className="font-medium">{ticket.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        {/* Created {ticket.createdAt.toLocaleDateString()} • Department: {ticket.department.name} */}
                      </p>
                    </div>
                    <AppButton
                      variant="ghost"
                      size="sm"
                      onClick={() => router.push(`/tickets/${ticket._id}`)}
                    >
                      View
                    </AppButton>
                  </div>
                ))}
              </TabsContent>

              <TabsContent value="resolved" className="space-y-4 mt-4">
                {userTickets.filter(t => ['resolved', 'closed'].includes(t.status)).map((ticket) => (
                  <div key={ticket._id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{ticket.ticketKey}</span>
                        <AppBadge variant="secondary">
                          {ticket.status}
                        </AppBadge>
                        {/* <AppBadge variant={ticket.priority === 'urgent' || ticket.priority === 'high' ? 'destructive' : 'secondary'}>
                          {ticket.priority}
                        </AppBadge> */}
                      </div>
                      <h4 className="font-medium">{ticket.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        {/* Created {ticket.createdAt.toLocaleDateString()} • Department: {ticket.department.name} */}
                      </p>
                    </div>
                    <AppButton
                      variant="ghost"
                      size="sm"
                      onClick={() => router.push(`/tickets/${ticket._id}`)}
                    >
                      View
                    </AppButton>
                  </div>
                ))}
              </TabsContent>
            </Tabs>
          </AppCardContent>
        </AppCard>
      )}
    </div>
  );
}
