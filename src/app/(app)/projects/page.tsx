'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  ChevronDown,
  ChevronRight,
  FolderKanban,
  KanbanSquare,
  Target,
  Ticket,
  CheckSquare,
  Plus,
  MoreHorizontal
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { CreateSubTicketModal } from '@/components/CreateSubTicketModal'

// Dummy data structure
interface SubTicket {
  id: string
  title: string
  completed: boolean
  assignee?: string
}

interface Ticket {
  id: string
  title: string
  status: 'To Do' | 'In Progress' | 'Done'
  priority: 'Low' | 'Medium' | 'High'
  assignee?: string
  subTickets: SubTicket[]
}

interface Milestone {
  id: string
  title: string
  progress: number
  dueDate: string
  tickets: Ticket[]
}

interface Board {
  id: string
  title: string
  type: 'Kanban' | 'Scrum'
  milestones: Milestone[]
}

interface Project {
  id: string
  name: string
  description: string
  status: 'Active' | 'On Hold' | 'Completed'
  progress: number
  boards: Board[]
}

const dummyProjects: Project[] = [
  {
    id: '1',
    name: 'E-Commerce Platform',
    description: 'Building a modern e-commerce platform with React and Node.js',
    status: 'Active',
    progress: 65,
    boards: [
      {
        id: '1',
        title: 'Development Board',
        type: 'Kanban',
        milestones: [
          {
            id: '1',
            title: 'Frontend Development',
            progress: 80,
            dueDate: '2025-10-15',
            tickets: [
              {
                id: '1',
                title: 'Implement user authentication',
                status: 'Done',
                priority: 'High',
                assignee: 'John Doe',
                subTickets: [
                  { id: '1', title: 'Create login form', completed: true },
                  { id: '2', title: 'Add JWT token handling', completed: true },
                  { id: '3', title: 'Implement logout functionality', completed: false }
                ]
              },
              {
                id: '2',
                title: 'Design product catalog',
                status: 'In Progress',
                priority: 'Medium',
                assignee: 'Jane Smith',
                subTickets: [
                  { id: '4', title: 'Create product card component', completed: true },
                  { id: '5', title: 'Add filtering options', completed: false },
                  { id: '6', title: 'Implement search functionality', completed: false }
                ]
              }
            ]
          },
          {
            id: '2',
            title: 'Backend API',
            progress: 45,
            dueDate: '2025-11-01',
            tickets: [
              {
                id: '3',
                title: 'Set up database schema',
                status: 'Done',
                priority: 'High',
                assignee: 'Mike Johnson',
                subTickets: [
                  { id: '7', title: 'Design user table', completed: true },
                  { id: '8', title: 'Create product table', completed: true }
                ]
              }
            ]
          }
        ]
      },
      {
        id: '2',
        title: 'Bug Fixes',
        type: 'Kanban',
        milestones: [
          {
            id: '3',
            title: 'Critical Bug Fixes',
            progress: 90,
            dueDate: '2025-09-30',
            tickets: [
              {
                id: '4',
                title: 'Fix checkout process bug',
                status: 'Done',
                priority: 'High',
                assignee: 'Sarah Wilson',
                subTickets: [
                  { id: '9', title: 'Identify root cause', completed: true },
                  { id: '10', title: 'Implement fix', completed: true },
                  { id: '11', title: 'Test fix', completed: true }
                ]
              }
            ]
          }
        ]
      }
    ]
  },
  {
    id: '2',
    name: 'Mobile App Development',
    description: 'Cross-platform mobile app using React Native',
    status: 'Active',
    progress: 30,
    boards: [
      {
        id: '3',
        title: 'Mobile Development',
        type: 'Scrum',
        milestones: [
          {
            id: '4',
            title: 'Sprint 1 - Setup',
            progress: 100,
            dueDate: '2025-09-20',
            tickets: [
              {
                id: '5',
                title: 'Initialize React Native project',
                status: 'Done',
                priority: 'High',
                assignee: 'Alex Brown',
                subTickets: [
                  { id: '12', title: 'Set up development environment', completed: true },
                  { id: '13', title: 'Configure navigation', completed: true }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
]

export default function ProjectsPage() {
  const [expandedProjects, setExpandedProjects] = useState<Set<string>>(new Set(['1']))
  const [expandedBoards, setExpandedBoards] = useState<Set<string>>(new Set(['1']))
  const [expandedMilestones, setExpandedMilestones] = useState<Set<string>>(new Set(['1']))
  const [expandedTickets, setExpandedTickets] = useState<Set<string>>(new Set())

  const toggleProject = (projectId: string) => {
    const newExpanded = new Set(expandedProjects)
    if (newExpanded.has(projectId)) {
      newExpanded.delete(projectId)
    } else {
      newExpanded.add(projectId)
    }
    setExpandedProjects(newExpanded)
  }

  const toggleBoard = (boardId: string) => {
    const newExpanded = new Set(expandedBoards)
    if (newExpanded.has(boardId)) {
      newExpanded.delete(boardId)
    } else {
      newExpanded.add(boardId)
    }
    setExpandedBoards(newExpanded)
  }

  const toggleMilestone = (milestoneId: string) => {
    const newExpanded = new Set(expandedMilestones)
    if (newExpanded.has(milestoneId)) {
      newExpanded.delete(milestoneId)
    } else {
      newExpanded.add(milestoneId)
    }
    setExpandedMilestones(newExpanded)
  }

  const toggleTicket = (ticketId: string) => {
    const newExpanded = new Set(expandedTickets)
    if (newExpanded.has(ticketId)) {
      newExpanded.delete(ticketId)
    } else {
      newExpanded.add(ticketId)
    }
    setExpandedTickets(newExpanded)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Done': return 'bg-green-100 text-green-800'
      case 'In Progress': return 'bg-blue-100 text-blue-800'
      case 'To Do': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High': return 'bg-red-100 text-red-800'
      case 'Medium': return 'bg-yellow-100 text-yellow-800'
      case 'Low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Projects</h1>
          <p className="text-muted-foreground">Manage your projects, boards, and tasks</p>
        </div>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          New Project
        </Button>
      </div>

      <div className="space-y-4">
        {dummyProjects.map((project) => (
          <Card key={project.id} className="w-full">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleProject(project.id)}
                    className="p-1 h-6 w-6"
                  >
                    {expandedProjects.has(project.id) ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                  </Button>
                  <FolderKanban className="w-5 h-5 text-blue-600" />
                  <div>
                    <CardTitle className="text-lg">{project.name}</CardTitle>
                    <p className="text-sm text-muted-foreground">{project.description}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={project.status === 'Active' ? 'default' : 'secondary'}>
                    {project.status}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    {project.progress}% complete
                  </span>
                </div>
              </div>
            </CardHeader>

            {expandedProjects.has(project.id) && (
              <CardContent className="pt-0">
                <div className="space-y-3">
                  {project.boards.map((board) => (
                    <div key={board.id} className="border rounded-lg p-3">
                      <div className="flex items-center space-x-3 mb-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleBoard(board.id)}
                          className="p-1 h-6 w-6"
                        >
                          {expandedBoards.has(board.id) ? (
                            <ChevronDown className="w-4 h-4" />
                          ) : (
                            <ChevronRight className="w-4 h-4" />
                          )}
                        </Button>
                        <KanbanSquare className="w-4 h-4 text-purple-600" />
                        <span className="font-medium cursor-pointer hover:text-purple-600" onClick={() => {
                          // Navigate to board detail page
                          window.location.href = `/projects/${project.id}/boards/${board.id}`
                        }}>
                          {board.title}
                        </span>
                        <Badge variant="outline">{board.type}</Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="ml-auto h-6 w-6 p-0"
                          onClick={() => {
                            // TODO: Open create ticket modal for this board
                            console.log("Create new ticket for board:", board.id);
                          }}
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                      </div>

                      {expandedBoards.has(board.id) && (
                        <div className="ml-8 space-y-2">
                          {board.milestones.map((milestone) => (
                            <div key={milestone.id} className="border-l-2 border-gray-200 pl-4">
                              <div className="flex items-center space-x-3 mb-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleMilestone(milestone.id)}
                                  className="p-1 h-6 w-6"
                                >
                                  {expandedMilestones.has(milestone.id) ? (
                                    <ChevronDown className="w-4 h-4" />
                                  ) : (
                                    <ChevronRight className="w-4 h-4" />
                                  )}
                                </Button>
                                <Target className="w-4 h-4 text-orange-600" />
                                <span className="font-medium">{milestone.title}</span>
                                <Badge variant="outline">{milestone.progress}%</Badge>
                                <span className="text-sm text-muted-foreground">
                                  Due: {milestone.dueDate}
                                </span>
                              </div>

                              {expandedMilestones.has(milestone.id) && (
                                <div className="ml-8 space-y-2">
                                  {milestone.tickets.map((ticket) => (
                                    <div key={ticket.id} className="border rounded p-3 bg-gray-50">
                                      <div className="flex items-center space-x-3 mb-2">
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => toggleTicket(ticket.id)}
                                          className="p-1 h-6 w-6"
                                        >
                                          {expandedTickets.has(ticket.id) ? (
                                            <ChevronDown className="w-4 h-4" />
                                          ) : (
                                            <ChevronRight className="w-4 h-4" />
                                          )}
                                        </Button>
                                        <Ticket className="w-4 h-4 text-green-600" />
                                        <span className="font-medium">{ticket.title}</span>
                                        <Badge className={getStatusColor(ticket.status)}>
                                          {ticket.status}
                                        </Badge>
                                        <Badge className={getPriorityColor(ticket.priority)}>
                                          {ticket.priority}
                                        </Badge>
                                        {ticket.assignee && (
                                          <span className="text-sm text-muted-foreground">
                                            {ticket.assignee}
                                          </span>
                                        )}
                                      </div>

                                      {expandedTickets.has(ticket.id) && (
                                        <div className="ml-8 space-y-1">
                                          {ticket.subTickets.map((subTicket) => (
                                            <div key={subTicket.id} className="flex items-center space-x-2">
                                              <Checkbox
                                                checked={subTicket.completed}
                                                className="w-4 h-4"
                                              />
                                              <span className={cn(
                                                "text-sm",
                                                subTicket.completed && "line-through text-muted-foreground"
                                              )}>
                                                {subTicket.title}
                                              </span>
                                              {subTicket.assignee && (
                                                <span className="text-xs text-muted-foreground">
                                                  ({subTicket.assignee})
                                                </span>
                                              )}
                                            </div>
                                          ))}
                                          <div className="pt-2">
                                            <CreateSubTicketModal
                                              trigger={
                                                <Button variant="outline" size="sm" className="text-xs">
                                                  <Plus className="w-3 h-3 mr-1" />
                                                  Add Subtask
                                                </Button>
                                              }
                                              onCreateSubTicket={(subTicketData) => {
                                                // TODO: Add subtask to ticket
                                                console.log('Create subtask for ticket:', ticket.id, subTicketData)
                                              }}
                                            />
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>
    </div>
  )
}