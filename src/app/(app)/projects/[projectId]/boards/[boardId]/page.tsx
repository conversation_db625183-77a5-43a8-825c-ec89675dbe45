'use client'

import React, { useState } from 'react'
import { use<PERSON>ara<PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { KanbanBoard } from '@/components/KanbanBoard'
import { TicketList } from '@/components/TicketList'
import { CreateTicketModal } from '@/components/CreateTicketModal'
import { CreateSubTicketModal } from '@/components/CreateSubTicketModal'
import { ArrowLeft, KanbanSquare, List, Plus } from 'lucide-react'
import Link from 'next/link'

// Dummy data - in real app this would come from API
interface SubTicket {
  id: string
  title: string
  completed: boolean
  assignee?: string
}

interface Ticket {
  id: string
  title: string
  status: 'To Do' | 'In Progress' | 'Done'
  priority: 'Low' | 'Medium' | 'High'
  assignee?: string
  subTickets: SubTicket[]
}

interface Board {
  id: string
  title: string
  type: 'Kanban' | 'Scrum'
  description?: string
}

const dummyBoard: Board = {
  id: '1',
  title: 'Development Board',
  type: 'Kanban',
  description: 'Main development board for tracking tasks'
}

const dummyTickets: Ticket[] = [
  {
    id: '1',
    title: 'Implement user authentication',
    status: 'Done',
    priority: 'High',
    assignee: 'John Doe',
    subTickets: [
      { id: '1', title: 'Create login form', completed: true },
      { id: '2', title: 'Add JWT token handling', completed: true },
      { id: '3', title: 'Implement logout functionality', completed: false }
    ]
  },
  {
    id: '2',
    title: 'Design product catalog',
    status: 'In Progress',
    priority: 'Medium',
    assignee: 'Jane Smith',
    subTickets: [
      { id: '4', title: 'Create product card component', completed: true },
      { id: '5', title: 'Add filtering options', completed: false },
      { id: '6', title: 'Implement search functionality', completed: false }
    ]
  },
  {
    id: '3',
    title: 'Set up database schema',
    status: 'To Do',
    priority: 'High',
    assignee: 'Mike Johnson',
    subTickets: [
      { id: '7', title: 'Design user table', completed: true },
      { id: '8', title: 'Create product table', completed: true }
    ]
  },
  {
    id: '4',
    title: 'Fix checkout process bug',
    status: 'Done',
    priority: 'High',
    assignee: 'Sarah Wilson',
    subTickets: [
      { id: '9', title: 'Identify root cause', completed: true },
      { id: '10', title: 'Implement fix', completed: true },
      { id: '11', title: 'Test fix', completed: true }
    ]
  }
]

export default function BoardDetailPage() {
  const params = useParams()
  const projectId = params.projectId as string
  const boardId = params.boardId as string

  const [tickets, setTickets] = useState<Ticket[]>(dummyTickets)
  const [viewMode, setViewMode] = useState<'kanban' | 'list'>('kanban')

  const kanbanColumns = [
    {
      id: 'todo',
      title: 'To Do',
      tickets: tickets.filter(t => t.status === 'To Do')
    },
    {
      id: 'in-progress',
      title: 'In Progress',
      tickets: tickets.filter(t => t.status === 'In Progress')
    },
    {
      id: 'done',
      title: 'Done',
      tickets: tickets.filter(t => t.status === 'Done')
    }
  ]

  const handleCreateTicket = (ticketData: {
    title: string
    description: string
    priority: 'Low' | 'Medium' | 'High'
    assignee?: string
  }) => {
    const newTicket: Ticket = {
      id: Date.now().toString(),
      title: ticketData.title,
      status: 'To Do',
      priority: ticketData.priority,
      assignee: ticketData.assignee,
      subTickets: []
    }
    setTickets(prev => [...prev, newTicket])
  }

  const handleTicketClick = (ticket: Ticket) => {
    // TODO: Open ticket detail modal or navigate to ticket detail page
    console.log('Ticket clicked:', ticket)
  }

  const handleCreateSubTicket = (ticketId: string, subTicketData: {
    title: string
    assignee?: string
  }) => {
    setTickets(prev => prev.map(ticket =>
      ticket.id === ticketId
        ? {
            ...ticket,
            subTickets: [
              ...ticket.subTickets,
              {
                id: Date.now().toString(),
                title: subTicketData.title,
                completed: false,
                assignee: subTicketData.assignee
              }
            ]
          }
        : ticket
    ))
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href={`/projects/${projectId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Project
            </Button>
          </Link>
          <div>
            <div className="flex items-center space-x-2">
              <KanbanSquare className="w-5 h-5 text-purple-600" />
              <h1 className="text-2xl font-bold">{dummyBoard.title}</h1>
              <Badge variant="outline">{dummyBoard.type}</Badge>
            </div>
            {dummyBoard.description && (
              <p className="text-muted-foreground mt-1">{dummyBoard.description}</p>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <CreateTicketModal onCreateTicket={handleCreateTicket} />
        </div>
      </div>

      {/* View Toggle */}
      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'kanban' | 'list')}>
        <TabsList>
          <TabsTrigger value="kanban" className="flex items-center space-x-2">
            <KanbanSquare className="w-4 h-4" />
            <span>Kanban</span>
          </TabsTrigger>
          <TabsTrigger value="list" className="flex items-center space-x-2">
            <List className="w-4 h-4" />
            <span>List</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="kanban" className="mt-6">
          <KanbanBoard
            columns={kanbanColumns}
            onCreateTicket={(columnId) => {
              // Map column ID to status
              const statusMap = {
                'todo': 'To Do',
                'in-progress': 'In Progress',
                'done': 'Done'
              } as const
              // TODO: Open create ticket modal with pre-selected status
              console.log('Create ticket in column:', columnId)
            }}
            onTicketClick={handleTicketClick}
          />
        </TabsContent>

        <TabsContent value="list" className="mt-6">
          <TicketList
            tickets={tickets}
            onCreateTicket={() => {
              // TODO: Open create ticket modal
              console.log('Create ticket from list view')
            }}
            onTicketClick={handleTicketClick}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}