"use client";

import * as React from "react";
import { Plus, Mail, Play, Pause, Edit, Trash2, Eye, Ticket, TrendingUp, Clock } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  SectionHeader,
  FilterBar,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  AppBadge,
  KebabActions,
  AppEmpty,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  AppInput,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Label,
  Checkbox,
  AppStat,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui-toolkit";

interface EmailRule {
  id: string;
  name: string;
  email: string;
  department: string;
  priority: "low" | "medium" | "high" | "urgent";
  assignee?: string;
  tags: string[];
  status: "active" | "inactive";
  createdAt: Date;
  processedCount: number;
}

export default function EmailToTicketPage() {
  const [emailRules, setEmailRules] = React.useState<EmailRule[]>([
    {
      id: "1",
      name: "Support Inbox",
      email: "<EMAIL>",
      department: "IT Support",
      priority: "medium",
      assignee: "Auto-assign",
      tags: ["email", "support"],
      status: "active",
      createdAt: new Date("2024-01-15"),
      processedCount: 234,
    },
    {
      id: "2", 
      name: "Sales Inquiries",
      email: "<EMAIL>",
      department: "Sales",
      priority: "high",
      tags: ["sales", "inquiry"],
      status: "active",
      createdAt: new Date("2024-01-10"),
      processedCount: 156,
    },
    {
      id: "3",
      name: "Bug Reports",
      email: "<EMAIL>",
      department: "Development",
      priority: "urgent",
      tags: ["bug", "development"],
      status: "inactive",
      createdAt: new Date("2024-02-01"),
      processedCount: 89,
    },
  ]);

  const [searchQuery, setSearchQuery] = React.useState("");
  const [statusFilter, setStatusFilter] = React.useState("all");
  const [createDialogOpen, setCreateDialogOpen] = React.useState(false);
  const [creating, setCreating] = React.useState(false);

  // Form state
  const [name, setName] = React.useState("");
  const [email, setEmail] = React.useState("");
  const [department, setDepartment] = React.useState("");
  const [priority, setPriority] = React.useState<"low" | "medium" | "high" | "urgent">("medium");
  const [autoTag, setAutoTag] = React.useState("");

  const filteredRules = React.useMemo(() => {
    return emailRules.filter(rule => {
      const matchesSearch = rule.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          rule.email.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = statusFilter === "all" || rule.status === statusFilter;
      return matchesSearch && matchesStatus;
    });
  }, [emailRules, searchQuery, statusFilter]);

  const handleCreateRule = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim() || !email.trim()) {
      return;
    }

    setCreating(true);
    // Simulate API call
    setTimeout(() => {
      const newRule: EmailRule = {
        id: Math.random().toString(36).substr(2, 9),
        name: name.trim(),
        email: email.trim(),
        department: department || "IT Support",
        priority,
        tags: autoTag ? [autoTag, "email"] : ["email"],
        status: "active",
        createdAt: new Date(),
        processedCount: 0,
      };
      
      setEmailRules(prev => [...prev, newRule]);
      setCreateDialogOpen(false);
      
      // Reset form
      setName("");
      setEmail("");
      setDepartment("");
      setPriority("medium");
      setAutoTag("");
      setCreating(false);
    }, 1000);
  };

  const getRuleActions = (rule: EmailRule) => [
    {
      label: "View Details",
      onClick: () => console.log("View details", rule.id),
      icon: <Eye className="h-4 w-4" />,
    },
    {
      label: rule.status === "active" ? "Pause" : "Activate",
      onClick: () => console.log("Toggle status", rule.id),
      icon: rule.status === "active" ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />,
    },
    {
      label: "Edit Rule",
      onClick: () => console.log("Edit", rule.id),
      icon: <Edit className="h-4 w-4" />,
    },
    {
      label: "Delete",
      onClick: () => console.log("Delete", rule.id),
      icon: <Trash2 className="h-4 w-4" />,
      variant: "destructive" as const,
      separator: true,
    },
  ];

  const statusOptions = [
    { value: "all", label: "All Status" },
    { value: "active", label: "Active" },
    { value: "inactive", label: "Inactive" },
  ];

  return (
    <div className="space-y-6 p-6">
      <SectionHeader
        title="Email-to-Ticket"
        description="Configure automatic ticket creation from email addresses"
        actions={
          <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
            <DialogTrigger asChild>
              <AppButton icon={<Plus className="h-4 w-4" />}>
                Add Email Rule
              </AppButton>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Create Email Rule</DialogTitle>
                <DialogDescription>
                  Set up automatic ticket creation from an email address
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleCreateRule} className="space-y-4">
                <AppInput
                  label="Rule Name"
                  placeholder="e.g., Support Inbox"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                />
                
                <AppInput
                  label="Email Address"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
                
                <AppInput
                  label="Default Department"
                  placeholder="IT Support"
                  value={department}
                  onChange={(e) => setDepartment(e.target.value)}
                />
                
                <div className="space-y-2">
                  <Label className="mb-2 block">Default Priority</Label>
                  <Select value={priority} onValueChange={(value: string) => setPriority(value as "low" | "medium" | "high" | "urgent")}>
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <AppInput
                  label="Auto Tag"
                  placeholder="e.g., support"
                  value={autoTag}
                  onChange={(e) => setAutoTag(e.target.value)}
                  helperText="Automatically add this tag to created tickets"
                />
                
                <div className="flex justify-end space-x-2 pt-4">
                  <AppButton
                    type="button"
                    variant="outline"
                    onClick={() => setCreateDialogOpen(false)}
                  >
                    Cancel
                  </AppButton>
                  <AppButton type="submit" loading={creating}>
                    Create Rule
                  </AppButton>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        }
      />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <AppStat
          title="Active Rules"
          value={emailRules.filter(r => r.status === "active").length}
          icon={<Mail className="h-4 w-4" />}
          description="Email addresses monitored"
        />
        <AppStat
          title="Total Processed"
          value={emailRules.reduce((sum, r) => sum + r.processedCount, 0)}
          icon={<Ticket className="h-4 w-4" />}
          description="Tickets created from emails"
        />
        <AppStat
          title="This Month"
          value="47"
          icon={<TrendingUp className="h-4 w-4" />}
          description="Tickets created this month"
        />
        <AppStat
          title="Avg Response"
          value="2.4h"
          icon={<Clock className="h-4 w-4" />}
          description="Average processing time"
        />
      </div>

      <Tabs defaultValue="rules" className="space-y-4">
        <TabsList>
          <TabsTrigger value="rules">Email Rules</TabsTrigger>
          <TabsTrigger value="settings">IMAP Settings</TabsTrigger>
        </TabsList>
        
        <TabsContent value="rules" className="space-y-4">
          <div className="flex items-center justify-between gap-4">
            <FilterBar
              searchValue={searchQuery}
              onSearchChange={setSearchQuery}
              searchPlaceholder="Search email rules..."
            />
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <AppCard>
            <AppCardHeader>
              <AppCardTitle>Email Rules</AppCardTitle>
              <AppCardDescription>
                {filteredRules.length} rule{filteredRules.length !== 1 ? 's' : ''} configured
              </AppCardDescription>
            </AppCardHeader>
            <AppCardContent>
              {filteredRules.length === 0 ? (
                <AppEmpty
                  icon={<Mail className="h-12 w-12" />}
                  title="No email rules found"
                  description={
                    searchQuery || statusFilter !== "all"
                      ? "No email rules match your search criteria."
                      : "Create your first email rule to start processing emails automatically."
                  }
                  action={{
                    label: "Create First Rule",
                    onClick: () => setCreateDialogOpen(true),
                    icon: <Plus className="h-4 w-4" />,
                  }}
                />
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Rule Name</TableHead>
                      <TableHead>Email Address</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Processed</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="w-12"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRules.map((rule) => (
                      <TableRow key={rule.id}>
                        <TableCell>
                          <div className="font-medium">{rule.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {rule.tags.map(tag => `#${tag}`).join(' ')}
                          </div>
                        </TableCell>
                        <TableCell>
                          <code className="rounded bg-muted px-1 py-0.5 text-sm">
                            {rule.email}
                          </code>
                        </TableCell>
                        <TableCell>{rule.department}</TableCell>
                        <TableCell>
                          <AppBadge 
                            variant={
                              rule.priority === "urgent" ? "destructive" :
                              rule.priority === "high" ? "default" : "outline"
                            }
                          >
                            {rule.priority}
                          </AppBadge>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{rule.processedCount}</div>
                          <div className="text-sm text-muted-foreground">tickets</div>
                        </TableCell>
                        <TableCell>
                          <AppBadge 
                            variant={rule.status === "active" ? "default" : "secondary"}
                          >
                            {rule.status}
                          </AppBadge>
                        </TableCell>
                        <TableCell>
                          <KebabActions items={getRuleActions(rule)} />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </AppCardContent>
          </AppCard>
        </TabsContent>
        
        <TabsContent value="settings">
          <AppCard>
            <AppCardHeader>
              <AppCardTitle>IMAP Configuration</AppCardTitle>
              <AppCardDescription>
                Configure IMAP settings for email processing
              </AppCardDescription>
            </AppCardHeader>
            <AppCardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <AppInput
                  label="IMAP Server"
                  placeholder="imap.gmail.com"
                  defaultValue="imap.company.com"
                />
                <AppInput
                  label="Port"
                  placeholder="993"
                  defaultValue="993"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <AppInput
                  label="Username"
                  placeholder="<EMAIL>"
                />
                <AppInput
                  label="Password"
                  type="password"
                  placeholder="••••••••"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox id="ssl" defaultChecked />
                <Label htmlFor="ssl">Use SSL/TLS</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox id="delete" />
                <Label htmlFor="delete">Delete processed emails</Label>
              </div>
              
              <div className="flex justify-end space-x-2 pt-4">
                <AppButton variant="outline">Test Connection</AppButton>
                <AppButton>Save Settings</AppButton>
              </div>
            </AppCardContent>
          </AppCard>
        </TabsContent>
      </Tabs>
    </div>
  );
}
