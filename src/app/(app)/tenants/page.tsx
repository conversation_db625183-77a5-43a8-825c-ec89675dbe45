"use client";

import * as React from "react";
import { Plus, Building, Edit, Trash2, Globe, Users, Settings } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  SectionHeader,
  FilterBar,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  AppBadge,
  KebabActions,
  AppEmpty,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  AppInput,
  Label,
  Checkbox,
  AppStat,
} from "@/components/ui-toolkit";
import { tenantService } from "@/lib/demo/api";
import { type Tenant } from "@/lib/demo/types";
import { toast } from "sonner";

export default function TenantsPage() {
  const [tenants, setTenants] = React.useState<Tenant[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [createDialogOpen, setCreateDialogOpen] = React.useState(false);
  const [creating, setCreating] = React.useState(false);

  // Form state
  const [name, setName] = React.useState("");
  const [slug, setSlug] = React.useState("");
  const [entitlements, setEntitlements] = React.useState({
    tickets: true,
    knowledge: false,
    reports: false,
  });

  React.useEffect(() => {
    const loadTenants = async () => {
      try {
        const data = await tenantService.getAll();
        setTenants(data);
      } catch (error) {
        console.error("Failed to load tenants:", error);
      } finally {
        setLoading(false);
      }
    };

    loadTenants();
  }, []);

  const filteredTenants = React.useMemo(() => {
    return tenants.filter(tenant => 
      tenant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tenant.slug.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [tenants, searchQuery]);

  const handleNameChange = (value: string) => {
    setName(value);
    // Auto-generate slug from name
    const autoSlug = value
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
    setSlug(autoSlug);
  };

  const handleCreateTenant = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim() || !slug.trim()) {
      toast.error("Please fill in name and slug");
      return;
    }

    setCreating(true);
    try {
      const newTenant = await tenantService.create({
        name: name.trim(),
        slug: slug.trim(),
        entitlements,
      });
      
      setTenants(prev => [...prev, newTenant]);
      toast.success("Tenant created successfully");
      setCreateDialogOpen(false);
      
      // Reset form
      setName("");
      setSlug("");
      setEntitlements({ tickets: true, knowledge: false, reports: false });
    } catch {
      toast.error("Failed to create tenant");
    } finally {
      setCreating(false);
    }
  };

  const getTenantActions = (tenant: Tenant) => [
    {
      label: "Configure",
      onClick: () => console.log("Configure", tenant.id),
      icon: <Settings className="h-4 w-4" />,
    },
    {
      label: "Manage Users",
      onClick: () => console.log("Manage users", tenant.id),
      icon: <Users className="h-4 w-4" />,
    },
    {
      label: "Edit Tenant",
      onClick: () => console.log("Edit", tenant.id),
      icon: <Edit className="h-4 w-4" />,
    },
    {
      label: "Delete",
      onClick: () => console.log("Delete", tenant.id),
      icon: <Trash2 className="h-4 w-4" />,
      variant: "destructive" as const,
      separator: true,
    },
  ];

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="grid gap-4 md:grid-cols-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <SectionHeader
        title="Tenant Management"
        description="Manage multi-tenant organizations and their settings"
        actions={
          <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
            <DialogTrigger asChild>
              <AppButton icon={<Plus className="h-4 w-4" />}>
                Create Tenant
              </AppButton>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Create New Tenant</DialogTitle>
                <DialogDescription>
                  Add a new organization to your system
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleCreateTenant} className="space-y-4">
                <AppInput
                  label="Organization Name"
                  placeholder="e.g., Acme Corporation"
                  value={name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  required
                />
                
                <div className="space-y-2">
                  <Label htmlFor="slug">Slug (URL identifier)</Label>
                  <AppInput
                    id="slug"
                    placeholder="e.g., acme-corp"
                    value={slug}
                    onChange={(e) => setSlug(e.target.value)}
                    helperText="This will be used in URLs and cannot be changed after creation"
                    required
                  />
                </div>
                
                <div className="space-y-3">
                  <Label>Features & Entitlements</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="tickets"
                        checked={entitlements.tickets}
                        onCheckedChange={(checked) => 
                          setEntitlements(prev => ({ ...prev, tickets: !!checked }))
                        }
                      />
                      <Label htmlFor="tickets" className="text-sm">
                        Ticket Management
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="knowledge"
                        checked={entitlements.knowledge}
                        onCheckedChange={(checked) => 
                          setEntitlements(prev => ({ ...prev, knowledge: !!checked }))
                        }
                      />
                      <Label htmlFor="knowledge" className="text-sm">
                        Knowledge Base
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="reports"
                        checked={entitlements.reports}
                        onCheckedChange={(checked) => 
                          setEntitlements(prev => ({ ...prev, reports: !!checked }))
                        }
                      />
                      <Label htmlFor="reports" className="text-sm">
                        Advanced Reports
                      </Label>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-2 pt-4">
                  <AppButton
                    type="button"
                    variant="outline"
                    onClick={() => setCreateDialogOpen(false)}
                  >
                    Cancel
                  </AppButton>
                  <AppButton type="submit" loading={creating}>
                    Create Tenant
                  </AppButton>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        }
      />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <AppStat
          title="Total Tenants"
          value={tenants.length}
          icon={<Building className="h-4 w-4" />}
          description="Active organizations"
        />
        <AppStat
          title="Active Tenants"
          value={tenants.filter(t => t.status === "active").length}
          icon={<Globe className="h-4 w-4" />}
          description="Currently operational"
        />
        <AppStat
          title="Full Access"
          value={tenants.filter(t => t.entitlements.tickets && t.entitlements.knowledge && t.entitlements.reports).length}
          icon={<Settings className="h-4 w-4" />}
          description="All features enabled"
        />
      </div>

      <FilterBar
        searchValue={searchQuery}
        onSearchChange={setSearchQuery}
        searchPlaceholder="Search tenants..."
      />

      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Organizations</AppCardTitle>
          <AppCardDescription>
            {filteredTenants.length} tenant{filteredTenants.length !== 1 ? 's' : ''} total
          </AppCardDescription>
        </AppCardHeader>
        <AppCardContent>
          {filteredTenants.length === 0 ? (
            <AppEmpty
              icon={<Building className="h-12 w-12" />}
              title="No tenants found"
              description={
                searchQuery
                  ? "No tenants match your search criteria."
                  : "Create your first tenant to get started with multi-tenancy."
              }
              action={{
                label: "Create First Tenant",
                onClick: () => setCreateDialogOpen(true),
                icon: <Plus className="h-4 w-4" />,
              }}
            />
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Organization</TableHead>
                  <TableHead>Slug</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Features</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTenants.map((tenant) => (
                  <TableRow key={tenant.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded bg-accent text-accent-foreground">
                          {tenant.name[0].toUpperCase()}
                        </div>
                        <div className="font-medium">{tenant.name}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <code className="rounded bg-muted px-1 py-0.5 text-sm">
                        {tenant.slug}
                      </code>
                    </TableCell>
                    <TableCell>
                      <AppBadge 
                        variant={tenant.status === "active" ? "default" : "secondary"}
                      >
                        {tenant.status}
                      </AppBadge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {tenant.entitlements.tickets && (
                          <AppBadge variant="outline" className="text-xs">
                            Tickets
                          </AppBadge>
                        )}
                        {tenant.entitlements.knowledge && (
                          <AppBadge variant="outline" className="text-xs">
                            Knowledge
                          </AppBadge>
                        )}
                        {tenant.entitlements.reports && (
                          <AppBadge variant="outline" className="text-xs">
                            Reports
                          </AppBadge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <KebabActions items={getTenantActions(tenant)} />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </AppCardContent>
      </AppCard>
    </div>
  );
}
