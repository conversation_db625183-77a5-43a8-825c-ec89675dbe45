"use client";

import * as React from "react";
import { Activity, Download } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  SectionHeader,
  FilterBar,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  AppBadge,
  AppEmpty,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  AppStat,
} from "@/components/ui-toolkit";
import { auditLogService } from "@/lib/demo/api";
import { type AuditLog } from "@/lib/demo/types";
import { formatDistance } from "date-fns";

export default function AuditPage() {
  const [auditLogs, setAuditLogs] = React.useState<AuditLog[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [actionFilter, setActionFilter] = React.useState("all");
  const [entityFilter, setEntityFilter] = React.useState("all");

  React.useEffect(() => {
    const loadAuditLogs = async () => {
      try {
        const data = await auditLogService.getAll();
        setAuditLogs(data);
      } catch (error) {
        console.error("Failed to load audit logs:", error);
      } finally {
        setLoading(false);
      }
    };

    loadAuditLogs();
  }, []);

  const filteredLogs = React.useMemo(() => {
    return auditLogs.filter(log => {
      const matchesSearch = log.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          log.actor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          log.entity.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesAction = actionFilter === "all" || log.action.toLowerCase().includes(actionFilter);
      const matchesEntity = entityFilter === "all" || log.entity === entityFilter;
      return matchesSearch && matchesAction && matchesEntity;
    });
  }, [auditLogs, searchQuery, actionFilter, entityFilter]);

  const actionOptions = [
    { value: "all", label: "All Actions" },
    { value: "create", label: "Created" },
    { value: "update", label: "Updated" },
    { value: "delete", label: "Deleted" },
    { value: "login", label: "Login" },
  ];

  const entityOptions = [
    { value: "all", label: "All Entities" },
    { value: "ticket", label: "Tickets" },
    { value: "user", label: "Users" },
    { value: "role", label: "Roles" },
    { value: "department", label: "Departments" },
  ];

  const getActionBadgeVariant = (action: string) => {
    if (action.includes("create")) return "default";
    if (action.includes("update")) return "outline";
    if (action.includes("delete")) return "destructive";
    return "secondary";
  };

  const handleExport = () => {
    console.log("Export audit logs");
  };

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="grid gap-4 md:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <SectionHeader
        title="Audit Log"
        description="Track all system activities and changes"
        actions={
          <AppButton 
            variant="outline" 
            icon={<Download className="h-4 w-4" />}
            onClick={handleExport}
          >
            Export Logs
          </AppButton>
        }
      />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <AppStat
          title="Total Events"
          value={auditLogs.length}
          icon={<Activity className="h-4 w-4" />}
          description="All recorded activities"
        />
        <AppStat
          title="Today's Events"
          value={auditLogs.filter(log => 
            new Date(log.createdAt).toDateString() === new Date().toDateString()
          ).length}
          icon={<Activity className="h-4 w-4" />}
          description="Activities today"
        />
        <AppStat
          title="User Actions"
          value={auditLogs.filter(log => log.entity === "user").length}
          icon={<Activity className="h-4 w-4" />}
          description="User-related events"
        />
        <AppStat
          title="Ticket Actions"
          value={auditLogs.filter(log => log.entity === "ticket").length}
          icon={<Activity className="h-4 w-4" />}
          description="Ticket-related events"
        />
      </div>

      {/* Filters */}
      <div className="flex items-center justify-between gap-4">
        <FilterBar
          searchValue={searchQuery}
          onSearchChange={setSearchQuery}
          searchPlaceholder="Search audit logs..."
        />
        
        <div className="flex items-center gap-2">
          <Select value={actionFilter} onValueChange={setActionFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Action" />
            </SelectTrigger>
            <SelectContent>
              {actionOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={entityFilter} onValueChange={setEntityFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Entity" />
            </SelectTrigger>
            <SelectContent>
              {entityOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Activity Log</AppCardTitle>
          <AppCardDescription>
            {filteredLogs.length} event{filteredLogs.length !== 1 ? 's' : ''} found
          </AppCardDescription>
        </AppCardHeader>
        <AppCardContent>
          {filteredLogs.length === 0 ? (
            <AppEmpty
              icon={<Activity className="h-12 w-12" />}
              title="No audit logs found"
              description={
                searchQuery || actionFilter !== "all" || entityFilter !== "all"
                  ? "No audit logs match your search criteria."
                  : "No audit events have been recorded yet."
              }
            />
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Entity</TableHead>
                  <TableHead>Details</TableHead>
                  <TableHead>Time</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={log.actor.avatar} />
                          <AvatarFallback>
                            {log.actor.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{log.actor.name}</div>
                          <div className="text-sm text-muted-foreground">{log.actor.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <AppBadge variant={getActionBadgeVariant(log.action)}>
                        {log.action}
                      </AppBadge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <span className="capitalize">{log.entity}</span>
                        <code className="text-xs text-muted-foreground">
                          {log.entityId.substring(0, 8)}
                        </code>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs">
                        {log.details && typeof log.details === 'object' ? (
                          <div className="text-sm text-muted-foreground">
                            {Object.entries(log.details).slice(0, 2).map(([key, value]) => (
                              <div key={key}>
                                <span className="font-medium">{key}:</span> {String(value)}
                              </div>
                            ))}
                          </div>
                        ) : (
                          <span className="text-sm text-muted-foreground">
                            No additional details
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {formatDistance(new Date(log.createdAt), new Date(), { addSuffix: true })}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </AppCardContent>
      </AppCard>
    </div>
  );
}
