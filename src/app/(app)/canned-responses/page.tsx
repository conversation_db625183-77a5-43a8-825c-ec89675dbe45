"use client";

import * as React from "react";
import { Plus, Copy, Edit, Trash2, Eye } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  SectionHeader,
  FilterBar,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  AppBadge,
  KebabActions,
  AppEmpty,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  AppInput,
  AppTextarea,
  Label,
} from "@/components/ui-toolkit";
import { 
  useGetCannedResponsesQuery,
  useCreateCannedResponseMutation,
  useUpdateCannedResponseMutation,
  useDeleteCannedResponseMutation,
} from "@/redux/slices/cannedResponses";
import type { CannedResponse } from "@/types/canned-responses";
import { toast } from "sonner";

export default function CannedResponsesPage() {
  // RTK Query hooks
  const { 
    data: responses, 
    isLoading, 
    error,
    refetch 
  } = useGetCannedResponsesQuery();
  
  const [createResponse, { isLoading: isCreating }] = useCreateCannedResponseMutation();
  const [updateResponse, { isLoading: isUpdating }] = useUpdateCannedResponseMutation();
  const [deleteResponse, { isLoading: isDeleting }] = useDeleteCannedResponseMutation();

  // Local state
  const [searchQuery, setSearchQuery] = React.useState("");
  const [createDialogOpen, setCreateDialogOpen] = React.useState(false);
  const [editDialogOpen, setEditDialogOpen] = React.useState(false);
  const [previewDialogOpen, setPreviewDialogOpen] = React.useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [selectedResponse, setSelectedResponse] = React.useState<CannedResponse | null>(null);

  // Form state
  const [title, setTitle] = React.useState("");
  const [content, setContent] = React.useState("");
  const [category, setCategory] = React.useState("");
  const [tags, setTags] = React.useState("");

  // Ensure responses is always an array
  const validResponses = React.useMemo(() => {
    return Array.isArray(responses) ? responses : [];
  }, [responses]);

  // Filter responses based on search query
  const filteredResponses = React.useMemo(() => {
    if (!searchQuery.trim()) return validResponses;
    
    const query = searchQuery.toLowerCase();
    return validResponses.filter(response => 
      response.title.toLowerCase().includes(query) ||
      response.content.toLowerCase().includes(query) ||
      response.tags.some(tag => tag.toLowerCase().includes(query)) ||
      (response.category && response.category.toLowerCase().includes(query))
    );
  }, [validResponses, searchQuery]);

  // Reset form fields
  const resetForm = () => {
    setTitle("");
    setContent("");
    setCategory("");
    setTags("");
    setSelectedResponse(null);
  };

  // Handle create response
  const handleCreateResponse = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !content.trim()) {
      toast.error("Please fill in title and content");
      return;
    }

    try {
      await createResponse({
        title: title.trim(),
        content: content.trim(),
        category: category.trim() || undefined,
        tags: tags.split(",").map(tag => tag.trim()).filter(Boolean),
        isActive: true,
      }).unwrap();
      
      toast.success("Canned response created successfully");
      setCreateDialogOpen(false);
      resetForm();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to create canned response");
    }
  };

  // Handle update response
  const handleUpdateResponse = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedResponse?.id) {
      toast.error("No response selected for update");
      return;
    }
    
    if (!title.trim() || !content.trim()) {
      toast.error("Please fill in title and content");
      return;
    }

    try {
      await updateResponse({
        id: selectedResponse.id,
        data: {
          title: title.trim(),
          content: content.trim(),
          category: category.trim() || undefined,
          tags: tags.split(",").map(tag => tag.trim()).filter(Boolean),
        }
      }).unwrap();
      
      toast.success("Canned response updated successfully");
      setEditDialogOpen(false);
      resetForm();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to update canned response");
    }
  };

  // Handle delete response
  const handleDeleteResponse = async () => {    
    if (!selectedResponse?.id) {
      toast.error("No response selected for deletion");
      return;
    }

    try {
      await deleteResponse(selectedResponse.id).unwrap();
      toast.success("Canned response deleted successfully");
      setDeleteDialogOpen(false);
      setSelectedResponse(null);
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to delete canned response");
    }
  };

  // Open edit dialog with pre-filled data
  const openEditDialog = (response: CannedResponse) => {
    if (!response || !response.id) {
      toast.error("Invalid response data - missing ID");
      return;
    }
    
    setSelectedResponse(response);
    setTitle(response.title);
    setContent(response.content);
    setCategory(response.category || "");
    setTags(Array.isArray(response.tags) ? response.tags.join(", ") : "");
    setEditDialogOpen(true);
  };

  // Open delete confirmation dialog
  const openDeleteDialog = (response: CannedResponse) => {
    
    if (!response || !response.id) {
      toast.error("Invalid response data - missing ID");
      return;
    }
    
    setSelectedResponse(response);
    setDeleteDialogOpen(true);
  };

  // Get actions for each response
  const getResponseActions = (response: CannedResponse) => [
    {
      label: "Preview",
      onClick: () => {
        setSelectedResponse(response);
        setPreviewDialogOpen(true);
      },
      icon: <Eye className="h-4 w-4" />,
    },
    {
      label: "Copy Content",
      onClick: () => {
        navigator.clipboard.writeText(response.content);
        toast.success("Content copied to clipboard");
      },
      icon: <Copy className="h-4 w-4" />,
    },
    {
      label: "Edit",
      onClick: () => openEditDialog(response),
      icon: <Edit className="h-4 w-4" />,
    },
    {
      label: "Delete",
      onClick: () => openDeleteDialog(response),
      icon: <Trash2 className="h-4 w-4" />,
      variant: "destructive" as const,
      separator: true,
    },
  ];

  // Show error state
  if (error) {
    return (
      <div className="space-y-6 p-6">
        <SectionHeader
          title="Canned Responses"
          description="Pre-written responses for common support scenarios"
        />
        <AppCard>
          <AppCardContent className="text-center py-6">
            <p className="text-destructive mb-4">
              Failed to load canned responses: {
                (error as any)?.data?.message || 
                (error as any)?.message || 
                'Unknown error'
              }
            </p>
            <AppButton onClick={() => refetch()}>Retry</AppButton>
          </AppCardContent>
        </AppCard>
      </div>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <SectionHeader
        title="Canned Responses"
        description="Pre-written responses for common support scenarios"
        actions={
          <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
            <DialogTrigger asChild>
              <AppButton icon={<Plus className="h-4 w-4" />}>
                New Response
              </AppButton>
            </DialogTrigger>
            <DialogContent className="sm:max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create Canned Response</DialogTitle>
                <DialogDescription>
                  Create a reusable response template for common support scenarios
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleCreateResponse} className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <AppInput
                    label="Title"
                    placeholder="e.g., Password Reset Instructions"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    required
                  />
                  <AppInput
                    label="Category (Optional)"
                    placeholder="e.g., Account Support"
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                  />
                </div>
                
                <AppTextarea
                  label="Response Content"
                  placeholder="Write your response template here. You can use variables like {{name}} and {{ticket_number}}..."
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  className="min-h-32"
                  required
                />
                
                <AppInput
                  label="Tags (Optional)"
                  placeholder="password, reset, account (comma separated)"
                  value={tags}
                  onChange={(e) => setTags(e.target.value)}
                  helperText="Add tags to help organize and find responses"
                />
                
                <div className="flex justify-end space-x-2 pt-4">
                  <AppButton
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setCreateDialogOpen(false);
                      resetForm();
                    }}
                  >
                    Cancel
                  </AppButton>
                  <AppButton type="submit" loading={isCreating}>
                    Create Response
                  </AppButton>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        }
      />

      <FilterBar
        searchValue={searchQuery}
        onSearchChange={setSearchQuery}
        searchPlaceholder="Search responses..."
      />

      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Response Library</AppCardTitle>
          <AppCardDescription>
            {filteredResponses.length} response{filteredResponses.length !== 1 ? 's' : ''} available
          </AppCardDescription>
        </AppCardHeader>
        <AppCardContent>
          {filteredResponses.length === 0 ? (
            <AppEmpty
              title="No canned responses found"
              description={
                searchQuery
                  ? "No responses match your search criteria."
                  : "Create your first canned response to get started."
              }
              action={{
                label: "Create First Response",
                onClick: () => setCreateDialogOpen(true),
                icon: <Plus className="h-4 w-4" />,
              }}
            />
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Tags</TableHead>
                  <TableHead>Preview</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredResponses.map((response, responseIndex) => (
                  <TableRow key={response.id || `response-${responseIndex}`}>
                    <TableCell className="font-medium">
                      {response.title}
                    </TableCell>
                    <TableCell>
                      {response.category && (
                        <AppBadge variant="secondary">
                          {response.category}
                        </AppBadge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {response.tags.slice(0, 2).map((tag, index) => (
                          <AppBadge key={`${response.id || responseIndex}-tag-${index}`} variant="outline" className="text-xs">
                            {tag}
                          </AppBadge>
                        ))}
                        {response.tags.length > 2 && (
                          <AppBadge key={`${response.id || responseIndex}-more-tags`} variant="outline" className="text-xs">
                            +{response.tags.length - 2}
                          </AppBadge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="max-w-xs">
                      <p className="text-sm text-muted-foreground truncate">
                        {response.content.substring(0, 80)}...
                      </p>
                    </TableCell>
                    <TableCell>
                      <AppBadge variant={response.isActive ? "default" : "secondary"}>
                        {response.isActive ? "Active" : "Inactive"}
                      </AppBadge>
                    </TableCell>
                    <TableCell>
                      <KebabActions items={getResponseActions(response)} />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </AppCardContent>
      </AppCard>

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Canned Response</DialogTitle>
            <DialogDescription>
              Update your response template
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateResponse} className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <AppInput
                label="Title"
                placeholder="e.g., Password Reset Instructions"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
              <AppInput
                label="Category (Optional)"
                placeholder="e.g., Account Support"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
              />
            </div>
            
            <AppTextarea
              label="Response Content"
              placeholder="Write your response template here. You can use variables like {{name}} and {{ticket_number}}..."
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="min-h-32"
              required
            />
            
            <AppInput
              label="Tags (Optional)"
              placeholder="password, reset, account (comma separated)"
              value={tags}
              onChange={(e) => setTags(e.target.value)}
              helperText="Add tags to help organize and find responses"
            />
            
            <div className="flex justify-end space-x-2 pt-4">
              <AppButton
                type="button"
                variant="outline"
                onClick={() => {
                  setEditDialogOpen(false);
                  resetForm();
                }}
              >
                Cancel
              </AppButton>
              <AppButton type="submit" loading={isUpdating}>
                Update Response
              </AppButton>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Preview Dialog */}
      <Dialog open={previewDialogOpen} onOpenChange={setPreviewDialogOpen}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>{selectedResponse?.title}</DialogTitle>
            {selectedResponse?.category && (
              <div className="flex items-center space-x-2">
                <AppBadge variant="secondary">
                  {selectedResponse.category}
                </AppBadge>
                <AppBadge variant={selectedResponse.isActive ? "default" : "secondary"}>
                  {selectedResponse.isActive ? "Active" : "Inactive"}
                </AppBadge>
              </div>
            )}
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">
                Response Content
              </Label>
              <div className="mt-2 p-4 bg-muted rounded-lg">
                <pre className="whitespace-pre-wrap text-sm">
                  {selectedResponse?.content}
                </pre>
              </div>
            </div>
            
            {selectedResponse?.tags && selectedResponse.tags.length > 0 && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Tags
                </Label>
                <div className="mt-2 flex flex-wrap gap-1">
                  {selectedResponse.tags.map((tag, index) => (
                    <AppBadge key={`preview-tag-${index}`} variant="outline">
                      {tag}
                    </AppBadge>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          <div className="flex justify-end space-x-2 pt-4">
            <AppButton
              variant="outline"
              onClick={() => {
                if (selectedResponse) {
                  navigator.clipboard.writeText(selectedResponse.content);
                  toast.success("Content copied to clipboard");
                }
              }}
              icon={<Copy className="h-4 w-4" />}
            >
              Copy Content
            </AppButton>
            <AppButton onClick={() => setPreviewDialogOpen(false)}>
              Close
            </AppButton>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Canned Response</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedResponse?.title}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2 pt-4">
            <AppButton
              variant="outline"
              onClick={() => {
                setDeleteDialogOpen(false);
                setSelectedResponse(null);
              }}
            >
              Cancel
            </AppButton>
            <AppButton
              variant="destructive"
              onClick={handleDeleteResponse}
              loading={isDeleting}
            >
              Delete
            </AppButton>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
