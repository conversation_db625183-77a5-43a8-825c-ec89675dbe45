"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { Plus, Filter, Eye, Edit, Trash2, <PERSON><PERSON><PERSON>, List } from "lucide-react";
import {
  App<PERSON>ard,
  App<PERSON>ardContent,
  App<PERSON>ard<PERSON><PERSON><PERSON>,
  <PERSON>ppCard<PERSON><PERSON>le,
  AppButton,
  SectionHeader,
  FilterBar,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  AppBadge,
  KebabActions,
  AppEmpty,
  Tabs,
  TabsList,
  TabsTrigger,
  Checkbox,
} from "@/components/ui-toolkit";
import { ticketService } from "@/lib/demo/api";
import { type Ticket } from "@/lib/demo/types";
import { type FilterChip } from "@/components/ui-toolkit";

export default function TicketsPage() {
  const router = useRouter();
  const [tickets, setTickets] = React.useState<Ticket[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [selectedTickets, setSelectedTickets] = React.useState<string[]>([]);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [filters, setFilters] = React.useState<FilterChip[]>([]);
  const [view, setView] = React.useState<"table" | "kanban">("table");

  React.useEffect(() => {
    const loadTickets = async () => {
      try {
        const data = await ticketService.getAll();
        setTickets(data);
      } catch (error) {
        console.error("Failed to load tickets:", error);
      } finally {
        setLoading(false);
      }
    };

    loadTickets();
  }, []);

  const handleSelectTicket = (ticketId: string) => {
    setSelectedTickets(prev => 
      prev.includes(ticketId) 
        ? prev.filter(id => id !== ticketId)
        : [...prev, ticketId]
    );
  };

  const handleSelectAll = () => {
    setSelectedTickets(
      selectedTickets.length === tickets.length ? [] : tickets.map(t => t.id)
    );
  };

  const handleRemoveFilter = (filterId: string) => {
    setFilters(prev => prev.filter(f => f.id !== filterId));
  };

  const handleDeleteTicket = async (ticketId: string) => {
    try {
      await ticketService.delete(ticketId);
      setTickets(prev => prev.filter(t => t.id !== ticketId));
      setSelectedTickets(prev => prev.filter(id => id !== ticketId));
    } catch (error) {
      console.error("Failed to delete ticket:", error);
    }
  };

  const getTicketActions = (ticket: Ticket) => [
    {
      label: "View Details",
      onClick: () => router.push(`/tickets/${ticket.id}`),
      icon: <Eye className="h-4 w-4" />,
    },
    {
      label: "Edit Ticket",
      onClick: () => router.push(`/tickets/${ticket.id}`),
      icon: <Edit className="h-4 w-4" />,
    },
    {
      label: "Delete",
      onClick: () => handleDeleteTicket(ticket.id),
      icon: <Trash2 className="h-4 w-4" />,
      variant: "destructive" as const,
      separator: true,
    },
  ];

  const filteredTickets = React.useMemo(() => {
    return tickets.filter(ticket => 
      ticket.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.requester.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [tickets, searchQuery]);

  const ticketsByStatus = React.useMemo(() => {
    const statuses = ["new", "triage", "in_progress", "waiting", "resolved", "closed"] as const;
    return statuses.reduce((acc, status) => {
      acc[status] = filteredTickets.filter(ticket => ticket.status === status);
      return acc;
    }, {} as Record<string, Ticket[]>);
  }, [filteredTickets]);

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  const renderTableView = () => (
    <AppCard>
      <AppCardHeader>
        <div className="flex items-center justify-between">
          <div>
            <AppCardTitle>All Tickets</AppCardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              {filteredTickets.length} tickets total
            </p>
          </div>
          {selectedTickets.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                {selectedTickets.length} selected
              </span>
              <AppButton variant="outline" size="sm">
                Bulk Actions
              </AppButton>
            </div>
          )}
        </div>
      </AppCardHeader>
      <AppCardContent>
        {filteredTickets.length === 0 ? (
          <AppEmpty
            title="No tickets found"
            description="There are no tickets matching your current filters."
            action={{
              label: "Create New",
              onClick: () => router.push("/create"),
              icon: <Plus className="h-4 w-4" />,
            }}
          />
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedTickets.length === tickets.length}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>Ticket</TableHead>
                <TableHead>Subject</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Requester</TableHead>
                <TableHead>Assignee</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTickets.map((ticket) => (
                <TableRow key={ticket.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedTickets.includes(ticket.id)}
                      onCheckedChange={() => handleSelectTicket(ticket.id)}
                    />
                  </TableCell>
                  <TableCell className="font-medium">
                    <button
                      className="text-left hover:underline"
                      onClick={() => router.push(`/tickets/${ticket.id}`)}
                    >
                      {ticket.number}
                    </button>
                  </TableCell>
                  <TableCell className="max-w-xs truncate">
                    {ticket.subject}
                  </TableCell>
                  <TableCell>
                    <AppBadge status={ticket.status}>
                      {ticket.status.replace("_", " ")}
                    </AppBadge>
                  </TableCell>
                  <TableCell>
                    <AppBadge priority={ticket.priority}>
                      {ticket.priority}
                    </AppBadge>
                  </TableCell>
                  <TableCell>{ticket.requester.name}</TableCell>
                  <TableCell>
                    {ticket.assignee ? ticket.assignee.name : "Unassigned"}
                  </TableCell>
                  <TableCell>
                    {ticket.createdAt.toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <KebabActions items={getTicketActions(ticket)} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </AppCardContent>
    </AppCard>
  );

  const renderKanbanView = () => (
    <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
      {Object.entries(ticketsByStatus).map(([status, statusTickets]) => (
        <AppCard key={status} className="min-h-96">
          <AppCardHeader className="pb-3">
            <AppCardTitle className="flex items-center justify-between text-sm">
              <span className="capitalize">{status.replace("_", " ")}</span>
              <AppBadge variant="secondary">{statusTickets.length}</AppBadge>
            </AppCardTitle>
          </AppCardHeader>
          <AppCardContent className="space-y-3">
            {statusTickets.map((ticket) => (
              <div
                key={ticket.id}
                className="p-3 rounded-lg border bg-card hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-muted-foreground">
                      {ticket.number}
                    </span>
                    <AppBadge priority={ticket.priority} className="text-xs">
                      {ticket.priority}
                    </AppBadge>
                  </div>
                  <p className="text-sm font-medium leading-tight">
                    {ticket.subject}
                  </p>
                  <div className="text-xs text-muted-foreground">
                    {ticket.assignee ? ticket.assignee.name : "Unassigned"}
                  </div>
                </div>
              </div>
            ))}
            {statusTickets.length === 0 && (
              <div className="text-center text-sm text-muted-foreground py-8">
                No tickets
              </div>
            )}
          </AppCardContent>
        </AppCard>
      ))}
    </div>
  );

  return (
    <div className="space-y-6 p-6">
      <SectionHeader
        title="Tickets"
        description="Manage and track all support tickets"
        actions={
          <div className="flex items-center space-x-2">
            <Tabs value={view} onValueChange={(v) => setView(v as "table" | "kanban")}>
              <TabsList>
                <TabsTrigger value="table">
                  <List className="h-4 w-4 mr-1" />
                  Table
                </TabsTrigger>
                <TabsTrigger value="kanban">
                  <Kanban className="h-4 w-4 mr-1" />
                  Kanban
                </TabsTrigger>
              </TabsList>
            </Tabs>
            <AppButton icon={<Plus className="h-4 w-4" />} onClick={() => router.push("/create")}>
              Create New
            </AppButton>
          </div>
        }
      />

      <FilterBar
        searchValue={searchQuery}
        onSearchChange={setSearchQuery}
        searchPlaceholder="Search tickets..."
        chips={filters}
        onRemoveChip={handleRemoveFilter}
        actions={
          <AppButton variant="outline" icon={<Filter className="h-4 w-4" />}>
            Filters
          </AppButton>
        }
      />

      {view === "table" ? renderTableView() : renderKanbanView()}
    </div>
  );
}
