"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft, Plus, X } from "lucide-react";
import {
  App<PERSON>ard,
  AppCardContent,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  AppInput,
  AppTextarea,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Label,
  AppBadge,
} from "@/components/ui-toolkit";
import { ticketService, userService, departmentService } from "@/lib/demo/api";
import { type User, type Department, type Ticket } from "@/lib/demo/types";
import { toast } from "sonner";

const TICKET_CATEGORIES = [
  "Hardware Issues",
  "Software Issues", 
  "Network Problems",
  "Account Access",
  "General Inquiry",
  "Bug Report",
  "Feature Request",
  "Other"
];

const PREDEFINED_TAGS = [
  "urgent",
  "hardware",
  "software",
  "network",
  "email",
  "printer",
  "laptop",
  "desktop",
  "mobile",
  "security",
  "password",
  "vpn",
  "wifi",
  "server",
  "database"
];

export default function NewTicketPage() {
  const router = useRouter();
  const [users, setUsers] = React.useState<User[]>([]);
  const [departments, setDepartments] = React.useState<Department[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [creating, setCreating] = React.useState(false);

  // Form state
  const [subject, setSubject] = React.useState("");
  const [description, setDescription] = React.useState("");
  const [priority, setPriority] = React.useState<Ticket["priority"]>("medium");
  const [requesterId, setRequesterId] = React.useState("");
  const [departmentId, setDepartmentId] = React.useState("");
  const [category, setCategory] = React.useState("");
  const [tags, setTags] = React.useState<string[]>([]);
  const [newTag, setNewTag] = React.useState("");

  React.useEffect(() => {
    const loadData = async () => {
      try {
        const [usersData, departmentsData] = await Promise.all([
          userService.getAll(),
          departmentService.getAll(),
        ]);
        setUsers(usersData);
        setDepartments(departmentsData);
      } catch (error) {
        console.error("Failed to load data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const addTag = (tag: string) => {
    const trimmedTag = tag.trim().toLowerCase();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      setTags(prev => [...prev, trimmedTag]);
    }
    setNewTag("");
  };

  const removeTag = (tagToRemove: string) => {
    setTags(prev => prev.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && newTag.trim()) {
      e.preventDefault();
      addTag(newTag);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!subject.trim() || !requesterId || !departmentId || !category) {
      toast.error("Please fill in all required fields");
      return;
    }

    setCreating(true);
    try {
      const requester = users.find(u => u.id === requesterId)!;
      const department = departments.find(d => d.id === departmentId)!;
      
      const newTicket = await ticketService.create({
        subject: subject.trim(),
        description: description.trim(),
        priority,
        requester,
        department,
        category,
        tags,
      });
      
      toast.success("Ticket created successfully");
      router.push(`/tickets/${newTicket.id}`);
    } catch (error) {
      console.error("Failed to create ticket:", error);
      toast.error("Failed to create ticket");
    } finally {
      setCreating(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <AppButton
          variant="outline"
          size="sm"
          onClick={() => router.push("/tickets")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Tickets
        </AppButton>
        <div>
          <h1 className="text-2xl font-bold">Create New Ticket</h1>
          <p className="text-muted-foreground">Fill in the details to create a new support ticket</p>
        </div>
      </div>

      {/* Form */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Ticket Details</AppCardTitle>
        </AppCardHeader>
        <AppCardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label className="mb-2 block">Subject *</Label>
              <AppInput
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="Brief description of the issue..."
                required
              />
            </div>

            <div>
              <Label className="mb-2 block">Description</Label>
              <AppTextarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Detailed description of the issue..."
                rows={6}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <Label className="mb-2 block">Priority *</Label>
                <Select value={priority} onValueChange={(value: Ticket["priority"]) => setPriority(value)}>
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="mb-2 block">Category *</Label>
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select category..." />
                  </SelectTrigger>
                  <SelectContent>
                    {TICKET_CATEGORIES.map((cat) => (
                      <SelectItem key={cat} value={cat}>
                        {cat}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="mb-2 block">Requester *</Label>
                <Select value={requesterId} onValueChange={setRequesterId}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select requester..." />
                  </SelectTrigger>
                  <SelectContent>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.name} ({user.email})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="mb-2 block">Department *</Label>
                <Select value={departmentId} onValueChange={setDepartmentId}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select department..." />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((department) => (
                      <SelectItem key={department.id} value={department.id}>
                        {department.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label className="mb-2 block">Tags</Label>
              <div className="space-y-3">
                <div className="flex gap-2">
                  <AppInput
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Type a tag and press Enter..."
                    className="flex-1"
                  />
                  <AppButton
                    type="button"
                    variant="outline"
                    onClick={() => addTag(newTag)}
                    disabled={!newTag.trim()}
                  >
                    Add Tag
                  </AppButton>
                </div>
                
                {/* Predefined Tags */}
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Or select from common tags:</p>
                  <div className="flex flex-wrap gap-2">
                    {PREDEFINED_TAGS.map((tag) => (
                      <AppButton
                        key={tag}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addTag(tag)}
                        disabled={tags.includes(tag)}
                        className="text-xs"
                      >
                        {tag}
                      </AppButton>
                    ))}
                  </div>
                </div>

                {/* Selected Tags */}
                {tags.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Selected tags:</p>
                    <div className="flex flex-wrap gap-2">
                      {tags.map((tag) => (
                        <AppBadge
                          key={tag}
                          variant="secondary"
                          className="flex items-center gap-1 px-2 py-1"
                        >
                          {tag}
                          <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="ml-1 hover:bg-destructive hover:text-destructive-foreground rounded-full p-0.5 transition-colors"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </AppBadge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <AppButton
                type="button"
                variant="outline"
                onClick={() => router.push("/tickets")}
              >
                Cancel
              </AppButton>
              <AppButton 
                type="submit" 
                loading={creating}
                icon={<Plus className="h-4 w-4" />}
              >
                Create Ticket
              </AppButton>
            </div>
          </form>
        </AppCardContent>
      </AppCard>
    </div>
  );
}
