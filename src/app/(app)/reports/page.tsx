"use client";

import * as React from "react";
import { Download, TrendingUp, Users, Ticket, Clock, RefreshCw } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  SectionHeader,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  AppBadge,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  AppStat,
} from "@/components/ui-toolkit";
import { AppChart } from "@/components/ui-toolkit/app-chart";
import { dashboardService } from "@/lib/demo/api";

export default function ReportsPage() {
  const [loading, setLoading] = React.useState(true);
  const [dateRange, setDateRange] = React.useState("7d");
  const [reportData, setReportData] = React.useState<Record<string, unknown> | null>(null);

  React.useEffect(() => {
    const loadReportData = async () => {
      try {
        const data = await dashboardService.getChartData();
        setReportData(data);
      } catch (error) {
        console.error("Failed to load report data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadReportData();
  }, [dateRange]);

  const dateRangeOptions = [
    { value: "24h", label: "Last 24 hours" },
    { value: "7d", label: "Last 7 days" },
    { value: "30d", label: "Last 30 days" },
    { value: "90d", label: "Last 90 days" },
    { value: "1y", label: "Last year" },
  ];

  const handleExport = (format: string) => {
    console.log(`Exporting report as ${format}`);
  };

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="grid gap-4 md:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <SectionHeader
        title="Reports & Analytics"
        description="Comprehensive insights and performance metrics"
        actions={
          <div className="flex items-center space-x-2">
            <Select value={dateRange} onValueChange={setDateRange}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {dateRangeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <AppButton
              variant="outline"
              icon={<RefreshCw className="h-4 w-4" />}
              onClick={handleRefresh}
            >
              Refresh
            </AppButton>
            
            <AppButton
              icon={<Download className="h-4 w-4" />}
              onClick={() => handleExport('pdf')}
            >
              Export
            </AppButton>
          </div>
        }
      />

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-4">
        <AppStat
          title="Total Tickets"
          value="1,247"
          delta={{ value: 12, type: "positive", label: "+12%" }}
          icon={<Ticket className="h-4 w-4" />}
          description="vs last period"
        />
        <AppStat
          title="Resolved Tickets"
          value="1,089"
          delta={{ value: 8, type: "positive", label: "+8%" }}
          icon={<TrendingUp className="h-4 w-4" />}
          description="87.3% resolution rate"
        />
        <AppStat
          title="Avg Resolution Time"
          value="2.4h"
          delta={{ value: -15, type: "negative", label: "-15%" }}
          icon={<Clock className="h-4 w-4" />}
          description="Faster than last period"
        />
        <AppStat
          title="Customer Satisfaction"
          value="4.2/5"
          delta={{ value: 0.3, type: "positive", label: "+0.3" }}
          icon={<Users className="h-4 w-4" />}
          description="Based on 347 ratings"
        />
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="agents">Agent Stats</TabsTrigger>
          <TabsTrigger value="satisfaction">Satisfaction</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <AppCard>
              <AppCardHeader>
                <AppCardTitle>Ticket Volume Trend</AppCardTitle>
                <AppCardDescription>Daily ticket creation over time</AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                {reportData && (
                  <AppChart
                    type="line"
                    data={reportData.ticketVolume as Record<string, unknown>[]}
                    height={300}
                  />
                )}
              </AppCardContent>
            </AppCard>

            <AppCard>
              <AppCardHeader>
                <AppCardTitle>Tickets by Priority</AppCardTitle>
                <AppCardDescription>Distribution of ticket priorities</AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                {reportData && (
                  <AppChart
                    type="pie"
                    data={[
                      { name: "Low", value: 245, color: "#10b981" },
                      { name: "Medium", value: 467, color: "#f59e0b" },
                      { name: "High", value: 389, color: "#ef4444" },
                      { name: "Urgent", value: 146, color: "#dc2626" },
                    ]}
                    height={300}
                  />
                )}
              </AppCardContent>
            </AppCard>
          </div>

          <AppCard>
            <AppCardHeader>
              <AppCardTitle>SLA Compliance</AppCardTitle>
              <AppCardDescription>Response and resolution time compliance</AppCardDescription>
            </AppCardHeader>
            <AppCardContent>
              {reportData && (
                <AppChart
                  type="bar"
                  data={reportData.slaCompliance as Record<string, unknown>[]}
                  height={400}
                />
              )}
            </AppCardContent>
          </AppCard>
        </TabsContent>
        
        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <AppCard>
              <AppCardHeader>
                <AppCardTitle>Resolution Time Trends</AppCardTitle>
                <AppCardDescription>Average resolution times over time</AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                {reportData && (
                  <AppChart
                    type="area"
                    data={[
                      { name: "Week 1", value: 3.2 },
                      { name: "Week 2", value: 2.8 },
                      { name: "Week 3", value: 2.4 },
                      { name: "Week 4", value: 2.1 },
                    ]}
                    height={300}
                  />
                )}
              </AppCardContent>
            </AppCard>

            <AppCard>
              <AppCardHeader>
                <AppCardTitle>First Contact Resolution</AppCardTitle>
                <AppCardDescription>Percentage resolved on first contact</AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                <div className="text-center">
                  <div className="text-4xl font-bold text-accent">73.2%</div>
                  <div className="text-muted-foreground">First Contact Resolution</div>
                  <AppBadge variant="outline" className="mt-2">+5.3% vs last period</AppBadge>
                </div>
              </AppCardContent>
            </AppCard>
          </div>

          <AppCard>
            <AppCardHeader>
              <AppCardTitle>Performance by Department</AppCardTitle>
              <AppCardDescription>Resolution metrics by department</AppCardDescription>
            </AppCardHeader>
            <AppCardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Department</TableHead>
                    <TableHead>Total Tickets</TableHead>
                    <TableHead>Resolved</TableHead>
                    <TableHead>Avg Resolution Time</TableHead>
                    <TableHead>SLA Compliance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">IT Support</TableCell>
                    <TableCell>487</TableCell>
                    <TableCell>421</TableCell>
                    <TableCell>2.1h</TableCell>
                    <TableCell>
                      <AppBadge variant="default">94.2%</AppBadge>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Helpdesk</TableCell>
                    <TableCell>623</TableCell>
                    <TableCell>578</TableCell>
                    <TableCell>1.8h</TableCell>
                    <TableCell>
                      <AppBadge variant="default">96.1%</AppBadge>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Infrastructure</TableCell>
                    <TableCell>137</TableCell>
                    <TableCell>90</TableCell>
                    <TableCell>4.2h</TableCell>
                    <TableCell>
                      <AppBadge variant="outline">78.3%</AppBadge>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </AppCardContent>
          </AppCard>
        </TabsContent>
        
        <TabsContent value="agents" className="space-y-4">
          <AppCard>
            <AppCardHeader>
              <AppCardTitle>Agent Performance</AppCardTitle>
              <AppCardDescription>Individual agent metrics and performance</AppCardDescription>
            </AppCardHeader>
            <AppCardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Agent</TableHead>
                    <TableHead>Tickets Handled</TableHead>
                    <TableHead>Resolved</TableHead>
                    <TableHead>Avg Resolution Time</TableHead>
                    <TableHead>Customer Rating</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">Sarah Johnson</TableCell>
                    <TableCell>89</TableCell>
                    <TableCell>84</TableCell>
                    <TableCell>1.9h</TableCell>
                    <TableCell>4.8/5</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Mike Chen</TableCell>
                    <TableCell>76</TableCell>
                    <TableCell>71</TableCell>
                    <TableCell>2.3h</TableCell>
                    <TableCell>4.6/5</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Alex Rivera</TableCell>
                    <TableCell>92</TableCell>
                    <TableCell>88</TableCell>
                    <TableCell>2.0h</TableCell>
                    <TableCell>4.7/5</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </AppCardContent>
          </AppCard>
        </TabsContent>
        
        <TabsContent value="satisfaction" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <AppCard>
              <AppCardHeader>
                <AppCardTitle>Satisfaction Trend</AppCardTitle>
                <AppCardDescription>Customer satisfaction over time</AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                <AppChart
                  type="line"
                  data={[
                    { name: "Jan", value: 4.1 },
                    { name: "Feb", value: 4.2 },
                    { name: "Mar", value: 4.0 },
                    { name: "Apr", value: 4.3 },
                    { name: "May", value: 4.2 },
                    { name: "Jun", value: 4.4 },
                  ]}
                  height={300}
                />
              </AppCardContent>
            </AppCard>

            <AppCard>
              <AppCardHeader>
                <AppCardTitle>Feedback Distribution</AppCardTitle>
                <AppCardDescription>Rating distribution breakdown</AppCardDescription>
              </AppCardHeader>
              <AppCardContent>
                <AppChart
                  type="bar"
                  data={[
                    { name: "5 Stars", value: 156 },
                    { name: "4 Stars", value: 98 },
                    { name: "3 Stars", value: 45 },
                    { name: "2 Stars", value: 23 },
                    { name: "1 Star", value: 11 },
                  ]}
                  height={300}
                />
              </AppCardContent>
            </AppCard>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
