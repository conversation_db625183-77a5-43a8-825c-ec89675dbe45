"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function HomePage() {
  const router = useRouter();

  useEffect(() => {
    // In a real app, check authentication status
    const isAuthenticated = typeof window !== "undefined" && localStorage.getItem("accessToken");
    
    if (isAuthenticated) {
      // Get user data to check userType
      const storedUser = localStorage.getItem("user");
      const user = storedUser ? JSON.parse(storedUser) : null;
      
      if (user) {
        // Check userType and route accordingly
        if (user.userType === "0") {
          // Self Service user - set operatorMode to false and route to self-service
          localStorage.setItem('operatorMode', JSON.stringify(false));
          router.replace("/self-service");
        } else if (user.userType === "1") {
          // Operator user - set operatorMode to true and route to dashboard
          localStorage.setItem('operatorMode', JSON.stringify(true));
          router.replace("/dashboard");
        } else {
          // Default fallback - route to dashboard
          localStorage.setItem('operatorMode', JSON.stringify(true));
          router.replace("/dashboard");
        }
      } else {
        // No user data, default to dashboard
        localStorage.setItem('operatorMode', JSON.stringify(true));
        router.replace("/dashboard");
      }
    } else {
      // User is not logged in, redirect to login
      router.replace("/auth/login");
    }
  }, [router]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-background">
      <div className="flex flex-col items-center space-y-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent"></div>
        <p className="text-sm text-muted-foreground">Loading...</p>
      </div>
    </div>
  );
}
