/**
 * Token refresh utilities for testing and manual refresh
 */

import { authService } from '@/services/auth/authService';

/**
 * Get cookie value by name
 */
export const getCookie = (name: string): string | null => {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
};

/**
 * Load tokens from cookies and store in localStorage
 */
export const loadTokensFromCookies = (): { accessToken: string | null, refreshToken: string | null } => {
  const accessToken = getCookie('accessToken');
  const refreshToken = getCookie('refreshToken');
  
  // Store in localStorage if found
  if (accessToken) {
    localStorage.setItem('accessToken', accessToken);
  }
  if (refreshToken) {
    localStorage.setItem('refreshToken', refreshToken);
  }
  
  return { accessToken, refreshToken };
};

/**
 * Manually refresh token for testing purposes
 */
export const manualRefreshToken = async () => {
  try {
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    console.log('🔄 Refreshing token...');
    const response = await authService.refreshToken(refreshToken);
    
    if (response.success) {
      // Ensure tokens are present
      if (!response.data.tokens) {
        throw new Error('No tokens received from refresh');
      }
      
      // Store updated tokens
      localStorage.setItem('accessToken', response.data.tokens.accessToken);
      localStorage.setItem('refreshToken', response.data.tokens.refreshToken);
      localStorage.setItem('user', JSON.stringify(response.data.user));
      
      console.log('✅ Token refreshed successfully');
      return response.data.tokens.accessToken;
    }
    
    throw new Error('Token refresh failed');
  } catch (error) {
    console.error('❌ Token refresh failed:', error);
    throw error;
  }
};

/**
 * Check if current token is expired or about to expire
 */
export const checkTokenExpiry = () => {
  const token = localStorage.getItem('accessToken');
  if (!token) {
    console.log('❌ No access token found');
    return true;
  }

  const isExpired = authService.isTokenExpired(token);
  if (isExpired) {
    console.log('⚠️ Token is expired or expires soon');
  } else {
    console.log('✅ Token is still valid');
  }
  
  return isExpired;
};

/**
 * Log token information for debugging
 */
export const logTokenInfo = () => {
  const token = localStorage.getItem('accessToken');
  if (!token) {
    console.log('❌ No access token found');
    return;
  }

  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const now = Math.floor(Date.now() / 1000);
    const expiresIn = payload.exp - now;
    const expiresAt = new Date(payload.exp * 1000).toLocaleString();
    
    console.log('🔍 Token Info:', {
      userId: payload.userId,
      email: payload.email,
      role: payload.role,
      tenantId: payload.tenantId,
      expiresAt,
      expiresInSeconds: expiresIn,
      expiresInMinutes: Math.floor(expiresIn / 60),
      isExpired: expiresIn <= 0,
      expiresWithin5Min: expiresIn < 300
    });
  } catch (error) {
    console.error('❌ Error parsing token:', error);
  }
};

// Global functions for testing in browser console
if (typeof window !== 'undefined') {
  (window as any).refreshToken = manualRefreshToken;
  (window as any).checkToken = checkTokenExpiry;
  (window as any).logToken = logTokenInfo;
  
  console.log('🔧 Token utilities available:');
  console.log('- window.refreshToken() - Manually refresh token');
  console.log('- window.checkToken() - Check if token is expired');
  console.log('- window.logToken() - Log token information');
}