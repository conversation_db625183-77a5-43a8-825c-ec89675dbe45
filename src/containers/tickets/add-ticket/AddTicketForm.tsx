"use client";

import * as React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { ArrowLeft, Plus, X, Upload, File } from "lucide-react";
import {
  App<PERSON>ard,
  AppCardContent,
  App<PERSON>ard<PERSON>eader,
  AppCardTitle,
  AppButton,
  AppInput,
  AppTextarea,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Label,
  AppBadge,
  Checkbox
} from "@/components/ui-toolkit";
import { useCreateTicketMutation } from "@/services/api/tickets";
import { useCreateSubTaskMutation } from "@/services/api/subtasks";
import { useGetDepartmentsQuery } from "@/redux/slices/departments/departmentSlice";
import { toast } from "sonner";
import { TYPES } from "@/lib/enums";

const PRIORITIES = [
  'Top Priority',
  'Urgent', 
  'Normal',
  'Low',
  'Later'
];

const PREDEFINED_TAGS = [
  "urgent",
  "hardware",
  "software",
  "network",
  "email",
  "printer",
  "laptop",
  "desktop",
  "mobile",
  "security",
  "password",
  "vpn",
  "wifi",
  "bug",
  "login",
  "server",
  "database"
];

export default function AddTicketForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [createTicket, { isLoading: creatingTicket }] = useCreateTicketMutation();
  const [createSubTask, { isLoading: creatingSubTask }] = useCreateSubTaskMutation();
  const { data: departments = [] } = useGetDepartmentsQuery();
  
  const creating = creatingTicket || creatingSubTask;
  
 

  // Get parent ticket info from URL params
  const parentId = searchParams.get('parentId');
  const parentTitle = searchParams.get('parentTitle') ? decodeURIComponent(searchParams.get('parentTitle')!) : null;

  // Form state
  const [title, setTitle] = React.useState(parentId && parentTitle ? `Sub-ticket: ${parentTitle}` : "");
  const [description, setDescription] = React.useState(parentId && parentTitle ? `This is a sub-ticket related to: ${parentTitle} (ID: ${parentId})` : "");
  const [priority, setPriority] = React.useState("Normal");
  const [departmentId, setDepartmentId] = React.useState<string | undefined>(undefined);
  const [type, setType] = React.useState(1); // default to ticket (1)
  const [tags, setTags] = React.useState<string[]>(parentId ? ['sub-ticket'] : []);
  const [newTag, setNewTag] = React.useState("");
  const [attachments, setAttachments] = React.useState<File[]>([]);
  const [canDescriptionAndTitleChange, setCanDescriptionAndTitleChange] = React.useState(true);
  const [isDragging, setIsDragging] = React.useState(false);

  // Pre-fill form if creating sub ticket
  React.useEffect(() => {
    if (parentId && parentTitle) {
      setTitle(`Sub-ticket: ${parentTitle}`);
      setDescription(`This is a sub-ticket related to: ${parentTitle} (ID: ${parentId})`);
      setTags(['sub-ticket']);
    }
  }, [parentId, parentTitle]);

  const addTag = (tag: string) => {
    const trimmedTag = tag.trim().toLowerCase();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      setTags(prev => [...prev, trimmedTag]);
    }
    setNewTag("");
  };

  const removeTag = (tagToRemove: string) => {
    setTags(prev => prev.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && newTag.trim()) {
      e.preventDefault();
      addTag(newTag);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    
    if (imageFiles.length !== files.length) {
      toast.error("Only image files are allowed");
      return;
    }
    
    // Check file size (max 5MB per file)
    const maxSize = 5 * 1024 * 1024; // 5MB
    const oversizedFiles = imageFiles.filter(file => file.size > maxSize);
    
    if (oversizedFiles.length > 0) {
      toast.error("Each file must be less than 5MB");
      return;
    }
    
    setAttachments(prev => [...prev, ...imageFiles]);
    // Reset input
    e.target.value = '';
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    
    if (imageFiles.length !== files.length) {
      toast.error("Only image files are allowed");
      return;
    }
    
    // Check file size (max 5MB per file)
    const maxSize = 5 * 1024 * 1024; // 5MB
    const oversizedFiles = imageFiles.filter(file => file.size > maxSize);
    
    if (oversizedFiles.length > 0) {
      toast.error("Each file must be less than 5MB");
      return;
    }
    
    setAttachments(prev => [...prev, ...imageFiles]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) {
      toast.error("Title is required");
      return;
    }

    if (!description.trim()) {
      toast.error("Description is required");
      return;
    }

    try {
      if (parentId) {
        // Create sub-task
        const payload = {
          title: title.trim(),
          description: description.trim(),
          canDescriptionAndTitleChange,
          priority,
          ...(departmentId && departmentId.trim() && { assigneeId: departmentId }), // Use departmentId as assigneeId for subtasks
          ...(attachments.length > 0 && { attachments })
        };
        
        console.log('Sending subtask payload:', payload);
        
        const response = await createSubTask({
          ticketId: parentId,
          data: payload
        }).unwrap();
        
        console.log('Subtask API Response:', response);
        
        toast.success("Sub-task created successfully");
        
        // Route back to parent ticket
        router.push(`/tickets?ticket=${parentId}`);
      } else {
        // Create regular ticket
        const payload = {
          title: title.trim(),
          description: description.trim(),
          canDescriptionAndTitleChange,
          priority,
          type,
          tags,
          ...(departmentId && departmentId.trim() && { departmentId }),
          ...(attachments.length > 0 && { attachments })
        };
        
        console.log('Sending ticket payload:', payload);
        
        const response = await createTicket(payload).unwrap();
        
        console.log('API Response:', response);
        
        toast.success("Ticket created successfully");
        
        // Route to the newly created ticket
        if (response.success && response.data.ticketKey) {
          router.push(`/tickets?ticket=${response.data.ticketKey}`);
        } else {
          router.push("/tickets");
        }
      }
    } catch (error: any) {
      console.error("Failed to create:", error);
      console.error("Error details:", error?.data);
      toast.error(error?.data?.message || `Failed to create ${parentId ? 'sub-task' : 'ticket'}`);
    }
  };


   console.log('Departments data:', departments);
  console.log('Selected departmentId:', departmentId);
  
  console.log('Departments data:', departments);
  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center space-x-2">
        <AppButton
          variant="ghost"
          size="sm"
          onClick={() => router.push("/tickets")}
        >
          <ArrowLeft className="h-4 w-4" />
          
        </AppButton>
        <div>
          <h1 className="text-2xl font-bold">
            {parentId ? 'Create Sub Ticket' : 'Create'}
          </h1>
          <p className="text-muted-foreground">
            {parentId 
              ? `Creating a sub-ticket for: ${parentTitle}` 
              : 'Fill in the details to create a new item'
            }
          </p>
        </div>
      </div>

      {/* Form */}
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>
            {parentId ? 'Sub Ticket Details' : 'Ticket Details'}
          </AppCardTitle>
        </AppCardHeader>
        <AppCardContent>
          {parentId && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-1">Parent Ticket</h3>
              <p className="text-sm text-blue-700">
                <span className="font-medium">ID:</span> {parentId}
              </p>
              <p className="text-sm text-blue-700">
                <span className="font-medium">Title:</span> {parentTitle}
              </p>
            </div>
          )}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label className="mb-2 block">Title *</Label>
              <AppInput
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Brief description of the issue..."
                required
              />
            </div>

            <div>
              <Label className="mb-2 block">Description *</Label>
              <AppTextarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Detailed description of the issue..."
                rows={6}
                required
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="canDescriptionAndTitleChange"
                checked={canDescriptionAndTitleChange}
                onCheckedChange={(checked) => setCanDescriptionAndTitleChange(checked as boolean)}
              />
              <Label htmlFor="canDescriptionAndTitleChange" className="text-sm">
                Allow editing title and description after creation
              </Label>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="mb-2 block">Priority</Label>
                <Select value={priority} onValueChange={setPriority}>
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {PRIORITIES.map((priorityOption) => (
                      <SelectItem key={priorityOption} value={priorityOption}>
                        {priorityOption}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="mb-2 block">Department</Label>
                <Select value={departmentId} onValueChange={(value) => setDepartmentId(value)}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select department (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.filter(dept => dept.id).map((dept) => (
                      <SelectItem key={dept.id} value={dept.id!}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label className="mb-2 block">Tags (optional)</Label>
              <div className="space-y-3">
                <AppInput
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type a tag and press Enter..."
                />
                
                {/* Predefined Tags */}
                {/* <div className="space-y-2">
                  <div className="flex flex-wrap gap-2">
                    {PREDEFINED_TAGS.map((tag) => (
                      <AppButton
                        key={tag}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addTag(tag)}
                        disabled={tags.includes(tag)}
                        className="text-xs"
                      >
                        {tag}
                      </AppButton>
                    ))}
                  </div>
                </div> */}

                {/* Selected Tags */}
                {tags.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Selected tags:</p>
                    <div className="flex flex-wrap gap-2">
                      {tags.map((tag) => (
                        <AppBadge
                          key={tag}
                          variant="secondary"
                          className="flex items-center gap-1 px-2 py-1"
                        >
                          {tag}
                          <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="ml-1 hover:bg-destructive hover:text-destructive-foreground rounded-full p-0.5 transition-colors"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </AppBadge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div>
              <Label className="mb-2 block">Attachments (Images)</Label>
              <div className="space-y-3">
                <div 
                  className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                    isDragging 
                      ? 'border-blue-400 bg-blue-50' 
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                  onDragOver={handleDragOver}
                  onDragEnter={handleDragEnter}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                    id="file-upload"
                  />
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <p className="text-sm text-gray-600">
                      Click to upload images or drag and drop
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      PNG, JPG, GIF up to 5MB each
                    </p>
                  </label>
                </div>
                
                {/* Selected Attachments */}
                {attachments.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Selected images:</p>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {attachments.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg"
                        >
                          <File className="h-8 w-8 text-gray-500 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {file.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeAttachment(index)}
                            className="p-1 hover:bg-red-100 rounded-full transition-colors"
                          >
                            <X className="h-4 w-4 text-red-500" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <AppButton
                type="button"
                variant="outline"
                onClick={() => router.push("/create")}
              >
                Cancel
              </AppButton>
              <AppButton 
                type="submit" 
                loading={creating}
                // icon={<Plus className="h-4 w-4" />}
              >
                Create
              </AppButton>
            </div>
          </form>
        </AppCardContent>
      </AppCard>
    </div>
  );
}