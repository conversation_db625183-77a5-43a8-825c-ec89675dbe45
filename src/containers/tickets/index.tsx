"use client";

import React, { useEffect, useState, useMemo } from 'react';
import ZGrid from '@/shared/ZGrid';
import getTicketColumns from './columns';
import { useGetTicketsQuery } from '@/services/api/tickets';
import { useAuth } from '@/hooks/useAuth';
import { useGetStatusesQuery } from '@/redux/slices/statuses';

interface TicketsContainerProps {
  status: string;
  priority: string;
  search: string;
  sortBy: string;
  sortOrder: string;
  page: number;
  size: number;
  assigneeId?: string[];
  departmentId?: string[];
  onPageChange: (page: number) => void;
  onRowClick?: (row: any) => void;
}

const TicketsContainer: React.FC<TicketsContainerProps> = ({
  status,
  priority,
  search,
  sortBy,
  sortOrder,
  page,
  size,
  assigneeId,
  departmentId,
  onPageChange,
  onRowClick,
}) => {
  const { tenant } = useAuth();
  const [debouncedSearch, setDebouncedSearch] = useState<string>(search);

  // Fetch statuses for dynamic styling
  const { data: statuses = [] } = useGetStatusesQuery(tenant?.tenantId || '', { skip: !tenant?.tenantId });

  // Helper function to get status color
  const getStatusColor = (statusName: string) => {
    const status = statuses.find(s => s.name === statusName);
    return status?.color || '#6b7280'; // Default gray color
  };

  // Debounce search input to reduce requests
  useEffect(() => {
    const t = setTimeout(() => setDebouncedSearch(search), 350);
    return () => clearTimeout(t);
  }, [search]);

  // Build query params conditionally
  const queryParams: any = {
    search: debouncedSearch,
    page,
    size,
    sortBy,
    sortOrder
  };
  if (status) queryParams.statusId = status;
  if (priority) queryParams.priority = priority;
  if (assigneeId && assigneeId.length > 0) queryParams.assigneeId = assigneeId;
  if (departmentId && departmentId.length > 0) queryParams.departmentId = departmentId;

  const { data: response, isLoading, error, isFetching } = useGetTicketsQuery(queryParams);

  // Extract tickets from API response
  const tickets = response?.data || [];
  const pagination = response?.pagination || { total: 0, pages: 1 };

  // Map API shapes to grid rows
  const rows = useMemo(() => {
    const mappedRows = tickets.map((t: any) => ({
      id: t._id,
      ticketKey: t.ticketKey,
      title: t.title || '-',
      subject: t.title || '-', // For backward compatibility
      status: t.status || '-',
      priority: t.priority || '-',
      requester: t.requester ? { name: t.requester.name } : { name: '-' },
      assignee: t.assignee ? { name: t.assignee.name } : { name: 'Unassigned' },
      department: t.department ? { name: t.department.name } : { name: '-' },
      createdAt: t.createdAt || new Date().toISOString(),
      mergedTickets: t.mergedTickets || [],
      mergedCount: (t.mergedTickets || []).length
    }));

    // Sort: tickets with merged tickets first, then by the specified sort
    return mappedRows.sort((a, b) => {
      // First priority: tickets with merged tickets come first
      if (a.mergedCount > 0 && b.mergedCount === 0) return -1;
      if (a.mergedCount === 0 && b.mergedCount > 0) return 1;
      
      // If both have merged tickets or neither do, use the specified sort
      if (sortBy) {
        const aValue = a[sortBy as keyof typeof a];
        const bValue = b[sortBy as keyof typeof b];
        
        let comparison = 0;
        if (aValue < bValue) comparison = -1;
        if (aValue > bValue) comparison = 1;
        
        return sortOrder === 'desc' ? -comparison : comparison;
      }
      
      return 0;
    });
  }, [tickets, sortBy, sortOrder]);

  // Get dynamic columns with status colors
  const ticketColumns = useMemo(() => getTicketColumns(statuses), [statuses]);

  return (
    <div style={{ marginTop: '1rem' }}>
      <ZGrid
        columns={ticketColumns}
        data={rows}
        loading={isLoading || isFetching}
        pagination={true} // server-side pagination handled below
        selectable={false}
        showSearch={false}
        onRowClick={onRowClick}
        pageSize={20}
        showFilters={false}
        filterPosition="header"
        sortIcons="arrows"
        // Zebra stripes with status-based colors
        rowClassName={(row, index) => {
          console.log("row :::", row);
          const baseClass = index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
          return baseClass
        }}
        rowStyle={(row, index) => ({
          borderLeft: `4px solid ${getStatusColor(row.status)}`
        })}
        headerClassName="bg-gray-50 text-black font-semibold"
      />

      <div className="flex items-center justify-between mt-3">
        <div className="text-sm text-muted-foreground">
          Showing {rows.length} of {pagination.total} results
        </div>
        <div className="flex items-center gap-2">
          <button
            disabled={page <= 1}
            onClick={() => onPageChange(Math.max(1, page - 1))}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Previous
          </button>
          <div className="px-3 py-1 border rounded">
            Page {page} of {pagination.pages}
          </div>
          <button
            disabled={page >= pagination.pages}
            onClick={() => onPageChange(page + 1)}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>


    </div>
  );
};

export default TicketsContainer;
