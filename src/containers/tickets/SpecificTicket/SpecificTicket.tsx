"use client";

import * as React from "react";
import { useRouter, usePara<PERSON>, useSearchParams } from "next/navigation";
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  MessageSquare, 
  Clock, 
  UserIcon, 
  Building2, 
  Tag, 
  AlertCircle, 
  CheckCircle2, 
  XCircle, 
  Pause, 
  Play, 
  MoreHorizontal,
  History,
  UserCheck,
  FileText,
  Send,
  Eye,
  EyeOff,
  Settings,
  Bell,
  Link,
  Timer,
  Plus
} from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  AppBadge,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Separator,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  AppInput,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Label,
  AppTextarea,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui-toolkit";
import { ticketService, userService, cannedResponseService } from "@/lib/demo/api";
import { useGetTicketByIdQuery } from "@/services/api/tickets";
import { type Ticket, type Comment, type User, type CannedResponse } from "@/lib/demo/types";
import { toast } from "sonner";

export default function SpecificTicket() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const ticketId = params.id as string;

  // Check for parent ticket parameters
  const parentId = searchParams.get('parentId');
  const parentTitle = searchParams.get('parentTitle');

  // Check if ticketId looks like a ticketKey (e.g., CYR250900002)
  const isTicketKey = /^[A-Z]+\d+$/.test(ticketId);
  
  // Use real API for ticketKey format, skip for demo IDs
  const { data: realTicketData, isLoading: isRealTicketLoading } = useGetTicketByIdQuery(
    ticketId,
    { skip: !isTicketKey }
  );

  const [ticket, setTicket] = React.useState<Ticket | null>(null);
  const [comments, setComments] = React.useState<Comment[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [editDialogOpen, setEditDialogOpen] = React.useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [commentDialogOpen, setCommentDialogOpen] = React.useState(false);
  const [assigneeDialogOpen, setAssigneeDialogOpen] = React.useState(false);
  const [cannedResponseDialogOpen, setCannedResponseDialogOpen] = React.useState(false);
  const [updating, setUpdating] = React.useState(false);
  const [deleting, setDeleting] = React.useState(false);
  const [isWatching, setIsWatching] = React.useState(false);
  
  // Edit form state
  const [editSubject, setEditSubject] = React.useState("");
  const [editStatus, setEditStatus] = React.useState<Ticket["status"]>("new");
  const [editPriority, setEditPriority] = React.useState<Ticket["priority"]>("medium");
  const [editDescription, setEditDescription] = React.useState("");

  // Comment form state
  const [commentBody, setCommentBody] = React.useState("");
  const [commentType, setCommentType] = React.useState<"public" | "internal">("public");
  
  // New state for enhanced features
  const [availableUsers, setAvailableUsers] = React.useState<User[]>([]);
  const [selectedAssignee, setSelectedAssignee] = React.useState<string>("");
  const [cannedResponses, setCannedResponses] = React.useState<CannedResponse[]>([]);
  const [activityLog, setActivityLog] = React.useState<Array<{
    id: string;
    action: string;
    user: User;
    timestamp: Date;
    details: string;
  }>>([]);
  const [activeTab, setActiveTab] = React.useState("details");
  
  // Time tracking state
  const [timeLogDialogOpen, setTimeLogDialogOpen] = React.useState(false);
  const [timeHistoryDialogOpen, setTimeHistoryDialogOpen] = React.useState(false);
  const [timeEntries, setTimeEntries] = React.useState<Array<{
    id: string;
    user: User;
    hours: number;
    description: string;
    createdAt: Date;
  }>>([]);
  const [timeForm, setTimeForm] = React.useState({
    timeInput: '', // Changed from hours to timeInput to support string format
    description: ''
  });
  const [estimatedTime, setEstimatedTime] = React.useState<number | null>(null);
  const [estimateDialogOpen, setEstimateDialogOpen] = React.useState(false);
  const [newEstimate, setNewEstimate] = React.useState('');

  // Utility function to parse time strings like "2h 30m", "1.5h", "90m", "1d", etc.
  const parseTimeString = (timeStr: string): number => {
    if (!timeStr.trim()) return 0;
    
    // Remove spaces and convert to lowercase
    const normalized = timeStr.toLowerCase().replace(/\s+/g, '');
    
    // Handle formats like "2h30m", "2h", "30m", "1.5h", "90m", "2.5", "1d"
    let totalHours = 0;
    
    // Extract days (convert to hours, 1d = 8h)
    const daysMatch = normalized.match(/(\d+(?:\.\d+)?)d/);
    if (daysMatch) {
      totalHours += parseFloat(daysMatch[1]) * 8;
    }
    
    // Extract hours
    const hoursMatch = normalized.match(/(\d+(?:\.\d+)?)h/);
    if (hoursMatch) {
      totalHours += parseFloat(hoursMatch[1]);
    }
    
    // Extract minutes
    const minutesMatch = normalized.match(/(\d+(?:\.\d+)?)m/);
    if (minutesMatch) {
      totalHours += parseFloat(minutesMatch[1]) / 60;
    }
    
    // If no h, m, or d suffix, treat as hours
    if (!hoursMatch && !minutesMatch && !daysMatch) {
      const numericValue = parseFloat(normalized);
      if (!isNaN(numericValue)) {
        totalHours = numericValue;
      }
    }
    
    return Math.round(totalHours * 100) / 100; // Round to 2 decimal places
  };

  // Utility function to format hours as time string
  const formatTimeString = (hours: number): string => {
    if (hours === 0) return '0m';
    
    const days = Math.floor(hours / 8);
    const remainingHours = hours % 8;
    const wholeHours = Math.floor(remainingHours);
    const minutes = Math.round((remainingHours - wholeHours) * 60);
    
    let result = '';
    
    if (days > 0) {
      result += `${days}d`;
      if (wholeHours > 0 || minutes > 0) result += ' ';
    }
    
    if (wholeHours > 0) {
      result += `${wholeHours}h`;
      if (minutes > 0) result += ' ';
    }
    
    if (minutes > 0 && days === 0) {
      result += `${minutes}m`;
    }
    
    // If only fractional hours less than 1 hour and no days
    if (result === '' && hours > 0) {
      const totalMinutes = Math.round(hours * 60);
      result = `${totalMinutes}m`;
    }
    
    return result || '0m';
  };

  // Handle real API data
  React.useEffect(() => {
    if (isTicketKey && realTicketData && !isRealTicketLoading) {
      // Convert real API data to Ticket format
      const convertedTicket: Ticket = {
        id: realTicketData._id,
        number: realTicketData.ticketKey,
        ticketKey: realTicketData.ticketKey,
        subject: realTicketData.title,
        status: realTicketData.status as any,
        priority: realTicketData.priority as any,
        requester: realTicketData.requester ? {
          id: realTicketData.requester.userId,
          name: realTicketData.requester.name,
          email: realTicketData.requester.email,
          role: {
            id: 'user-role',
            name: 'User',
            type: 'system' as const,
            permissions: {
              tickets: { read: true, write: false, delete: false },
              users: { read: false, write: false, delete: false },
              settings: { read: false, write: false, delete: false },
              reports: { read: false, write: false, delete: false }
            }
          },
          status: 'active' as const,
          department: {
            id: 'dept-1',
            name: 'Support',
            status: 'active' as const,
            memberCount: 0
          }
        } : {
          id: 'unknown',
          name: 'Unknown',
          email: '',
          role: {
            id: 'user-role',
            name: 'User',
            type: 'system' as const,
            permissions: {
              tickets: { read: true, write: false, delete: false },
              users: { read: false, write: false, delete: false },
              settings: { read: false, write: false, delete: false },
              reports: { read: false, write: false, delete: false }
            }
          },
          status: 'active' as const,
          department: {
            id: 'dept-1',
            name: 'Support',
            status: 'active' as const,
            memberCount: 0
          }
        },
        assignee: undefined, // You may need to fetch assignee details separately
        department: {
          id: 'dept-1',
          name: 'Support',
          status: 'active' as const,
          memberCount: 0
        },
        category: undefined,
        tags: realTicketData.tags || [],
        createdAt: new Date(realTicketData.createdAt),
        updatedAt: new Date(realTicketData.updatedAt),
        description: realTicketData.description
      };

      setTicket(convertedTicket);
      setComments([]); // No comments for now
      setLoading(false);

      // Initialize edit form
      setEditSubject(convertedTicket.subject);
      setEditStatus(convertedTicket.status);
      setEditPriority(convertedTicket.priority);
      setEditDescription(convertedTicket.description || "");
      setSelectedAssignee(convertedTicket.assignee?.id || "unassigned");
    }
  }, [isTicketKey, realTicketData, isRealTicketLoading]);

  React.useEffect(() => {
    const loadTicketData = async () => {
      // Skip if we're using real API data
      if (isTicketKey && (isRealTicketLoading || realTicketData)) {
        return;
      }
      
      try {
        const [ticketData, commentsData, usersData, cannedResponsesData] = await Promise.all([
          ticketService.getById(ticketId),
          ticketService.getComments(ticketId),
          userService.getAll(),
          cannedResponseService.getAll()
        ]);
        
        setTicket(ticketData);
        setComments(commentsData);
        setAvailableUsers(usersData);
        setCannedResponses(cannedResponsesData);
        
        // Initialize edit form
        if (ticketData) {
          setEditSubject(ticketData.subject);
          setEditStatus(ticketData.status);
          setEditPriority(ticketData.priority);
          setEditDescription(ticketData.description || "");
          setSelectedAssignee(ticketData.assignee?.id || "unassigned");
          
          // Mock activity log
          setActivityLog([
            {
              id: "1",
              action: "created",
              user: ticketData.requester,
              timestamp: ticketData.createdAt,
              details: "Ticket created"
            },
            {
              id: "2", 
              action: "status_changed",
              user: ticketData.assignee || ticketData.requester,
              timestamp: ticketData.updatedAt,
              details: `Status changed to ${ticketData.status}`
            }
          ]);
        }
      } catch (error) {
        console.error("Failed to load ticket:", error);
        toast.error("Failed to load ticket details");
        router.push("/tickets");
      } finally {
        setLoading(false);
      }
    };

    if (ticketId) {
      loadTicketData();
    }
  }, [ticketId, router, isTicketKey, isRealTicketLoading, realTicketData]);

  const handleUpdateTicket = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!ticket) return;

    setUpdating(true);
    try {
      const updatedTicket = await ticketService.update(ticket.id, {
        subject: editSubject.trim(),
        status: editStatus,
        priority: editPriority,
        description: editDescription.trim(),
      });
      
      setTicket(updatedTicket);
      toast.success("Ticket updated successfully");
      setEditDialogOpen(false);
    } catch (error) {
      console.error("Failed to update ticket:", error);
      toast.error("Failed to update ticket");
    } finally {
      setUpdating(false);
    }
  };

  const handleQuickStatusUpdate = async (newStatus: Ticket["status"]) => {
    if (!ticket || newStatus === ticket.status) return;
    
    try {
      const updatedTicket = await ticketService.update(ticket.id, {
        status: newStatus,
      });
      
      setTicket(updatedTicket);
      setEditStatus(newStatus);
      toast.success("Status updated successfully");
      
      // Add to activity log
      setActivityLog(prev => [{
        id: Date.now().toString(),
        action: "status_changed",
        user: availableUsers[0] || ticket.requester,
        timestamp: new Date(),
        details: `Status changed from ${ticket.status} to ${newStatus}`
      }, ...prev]);
    } catch (error) {
      console.error("Failed to update status:", error);
      toast.error("Failed to update status");
    }
  };

  const handleQuickPriorityUpdate = async (newPriority: Ticket["priority"]) => {
    if (!ticket || newPriority === ticket.priority) return;
    
    try {
      const updatedTicket = await ticketService.update(ticket.id, {
        priority: newPriority,
      });
      
      setTicket(updatedTicket);
      setEditPriority(newPriority);
      toast.success("Priority updated successfully");
      
      // Add to activity log
      setActivityLog(prev => [{
        id: Date.now().toString(),
        action: "priority_changed",
        user: availableUsers[0] || ticket.requester,
        timestamp: new Date(),
        details: `Priority changed from ${ticket.priority} to ${newPriority}`
      }, ...prev]);
    } catch (error) {
      console.error("Failed to update priority:", error);
      toast.error("Failed to update priority");
    }
  };

  const handleDeleteTicket = async () => {
    if (!ticket) return;

    setDeleting(true);
    try {
      await ticketService.delete(ticket.id);
      toast.success("Ticket deleted successfully");
      router.push("/tickets");
    } catch (error) {
      console.error("Failed to delete ticket:", error);
      toast.error("Failed to delete ticket");
    } finally {
      setDeleting(false);
    }
  };

  const handleAddComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!ticket || !commentBody.trim()) return;

    try {
      const newComment = await ticketService.addComment(ticket.id, {
        body: commentBody.trim(),
        type: commentType,
      });
      
      setComments(prev => [...prev, newComment]);
      toast.success("Comment added successfully");
      setCommentDialogOpen(false);
      setCommentBody("");
    } catch (error) {
      console.error("Failed to add comment:", error);
      toast.error("Failed to add comment");
    }
  };

  const handleAssigneeUpdate = async () => {
    if (!ticket) return;
    
    const currentAssigneeId = ticket.assignee?.id || "unassigned";
    
    if (selectedAssignee === currentAssigneeId) return;
    
    setUpdating(true);
    try {
      let assignee = null;
      if (selectedAssignee && selectedAssignee !== "unassigned") {
        assignee = availableUsers.find(user => user.id === selectedAssignee);
      }
      
      const updatedTicket = await ticketService.update(ticket.id, {
        assignee: assignee || undefined
      });
      
      setTicket(updatedTicket);
      
      // Add to activity log
      const newActivity = {
        id: Date.now().toString(),
        action: assignee ? "assigned" : "unassigned",
        user: assignee || availableUsers[0] || ticket.requester,
        timestamp: new Date(),
        details: assignee ? `Ticket assigned to ${assignee.name}` : "Ticket unassigned"
      };
      setActivityLog(prev => [newActivity, ...prev]);
      
      toast.success("Assignee updated successfully");
      setAssigneeDialogOpen(false);
    } catch (error) {
      console.error("Failed to update assignee:", error);
      toast.error("Failed to update assignee");
    } finally {
      setUpdating(false);
    }
  };

  const handleQuickAssigneeUpdate = async (newAssigneeId: string) => {
    if (!ticket) return;
    
    const currentAssigneeId = ticket.assignee?.id || "unassigned";
    
    if (newAssigneeId === currentAssigneeId) return;
    
    try {
      let assignee = null;
      if (newAssigneeId && newAssigneeId !== "unassigned") {
        assignee = availableUsers.find(user => user.id === newAssigneeId);
      }
      
      const updatedTicket = await ticketService.update(ticket.id, {
        assignee: assignee || undefined
      });
      
      setTicket(updatedTicket);
      setSelectedAssignee(newAssigneeId);
      
      // Add to activity log
      const newActivity = {
        id: Date.now().toString(),
        action: assignee ? "assigned" : "unassigned",
        user: assignee || availableUsers[0] || ticket.requester,
        timestamp: new Date(),
        details: assignee ? `Ticket assigned to ${assignee.name}` : "Ticket unassigned"
      };
      setActivityLog(prev => [newActivity, ...prev]);
      
      toast.success("Assignee updated successfully");
    } catch (error) {
      console.error("Failed to update assignee:", error);
      toast.error("Failed to update assignee");
    }
  };

  const handleCannedResponseSelect = (response: CannedResponse) => {
    setCommentBody(response.content);
    setCannedResponseDialogOpen(false);
  };

  const handleToggleWatch = () => {
    setIsWatching(!isWatching);
    toast.success(isWatching ? "Stopped watching ticket" : "Now watching ticket");
  };

  const copyTicketUrl = () => {
    const url = window.location.href;
    navigator.clipboard.writeText(url);
    toast.success("Ticket URL copied to clipboard");
  };

  // Time tracking functions
  const handleAddTime = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!ticket || !timeForm.timeInput.trim()) {
      toast.error("Please enter time to log");
      return;
    }

    const hours = parseTimeString(timeForm.timeInput);
    if (hours <= 0) {
      toast.error("Please enter a valid time (e.g., '2h', '30m', '1h 30m', '1.5')");
      return;
    }

    const currentUser = availableUsers[0]; // Mock current user
    const newTimeEntry = {
      id: Date.now().toString(),
      user: currentUser,
      hours,
      description: timeForm.description.trim() || 'Work logged', // Default description if empty
      createdAt: new Date(),
    };

    setTimeEntries(prev => [...prev, newTimeEntry]);
    
    // Add to activity log
    const newActivity = {
      id: Date.now().toString(),
      action: "time_logged",
      user: currentUser,
      timestamp: new Date(),
      details: `Logged ${formatTimeString(hours)}${timeForm.description.trim() ? ` - ${timeForm.description}` : ''}`
    };
    setActivityLog(prev => [newActivity, ...prev]);

    toast.success(`Added ${formatTimeString(hours)} to ticket`);
    setTimeLogDialogOpen(false);
    setTimeForm({ timeInput: '', description: '' });
  };

  const handleSetEstimate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newEstimate.trim()) {
      toast.error("Please enter an estimate");
      return;
    }

    const estimate = parseTimeString(newEstimate);
    if (estimate <= 0) {
      toast.error("Please enter a valid time estimate (e.g., '8h', '2d', '1h 30m')");
      return;
    }

    setEstimatedTime(estimate);
    
    // Add to activity log
    const currentUser = availableUsers[0]; // Mock current user
    const newActivity = {
      id: Date.now().toString(),
      action: "estimate_set",
      user: currentUser,
      timestamp: new Date(),
      details: `Set estimated time to ${formatTimeString(estimate)}`
    };
    setActivityLog(prev => [newActivity, ...prev]);

    toast.success(`Estimated time set to ${formatTimeString(estimate)}`);
    setEstimateDialogOpen(false);
    setNewEstimate('');
  };

  const getTotalTimeLogged = () => {
    return timeEntries.reduce((total, entry) => total + entry.hours, 0);
  };

  const getStatusIcon = (status: Ticket["status"]) => {
    switch (status) {
      case "new": return <AlertCircle className="h-4 w-4" />;
      case "triage": return <Clock className="h-4 w-4" />;
      case "in_progress": return <Play className="h-4 w-4" />;
      case "waiting": return <Pause className="h-4 w-4" />;
      case "resolved": return <CheckCircle2 className="h-4 w-4" />;
      case "closed": return <XCircle className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: Ticket["status"]) => {
    switch (status) {
      case "new": return "bg-blue-100 text-blue-800 border-blue-300";
      case "triage": return "bg-yellow-100 text-yellow-800 border-yellow-300";
      case "in_progress": return "bg-orange-100 text-orange-800 border-orange-300";
      case "waiting": return "bg-purple-100 text-purple-800 border-purple-300";
      case "resolved": return "bg-green-100 text-green-800 border-green-300";
      case "closed": return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  const getPriorityColor = (priority: Ticket["priority"]) => {
    switch (priority) {
      case "low": return "bg-gray-100 text-gray-800 border-gray-300";
      case "medium": return "bg-blue-100 text-blue-800 border-blue-300";
      case "high": return "bg-orange-100 text-orange-800 border-orange-300";
      case "urgent": return "bg-red-100 text-red-800 border-red-300";
    }
  };

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (!ticket) {
    return (
      <div className="space-y-6 p-6">
        <div className="text-center">
          <p className="text-muted-foreground">Ticket not found</p>
          <AppButton 
            onClick={() => parentId ? router.push(`/tickets/${parentId}`) : router.push("/tickets")} 
            className="mt-4"
          >
            {/* {parentId ? `Back to Parent Ticket` : 'Back to Tickets'} */}
          </AppButton>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <AppButton
            variant="outline"
            size="sm"
            onClick={() => parentId ? router.push(`/tickets/${parentId}`) : router.push("/tickets")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {parentId ? `Back to Parent Ticket` : 'Back to Tickets'}
          </AppButton>
          <div>
            <div className="flex items-center space-x-3">
              <h1 className="text-2xl font-bold">{ticket.number}</h1>
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(ticket.status)}`}>
                {getStatusIcon(ticket.status)}
                <span className="ml-1 capitalize">{ticket.status.replace('_', ' ')}</span>
              </div>
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(ticket.priority)}`}>
                <span className="capitalize">{ticket.priority}</span>
              </div>
            </div>
            <p className="text-muted-foreground mt-1">{ticket.subject}</p>
            <p className="text-sm text-muted-foreground mt-1">
              Requested by: <span className="font-medium">{ticket.requester.email}</span>
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <AppButton
                variant="outline"
                size="sm"
                onClick={handleToggleWatch}
              >
                {isWatching ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </AppButton>
            </TooltipTrigger>
            <TooltipContent>
              {isWatching ? "Stop watching" : "Watch ticket"}
            </TooltipContent>
          </Tooltip>
          
          <Tooltip>
            <TooltipTrigger asChild>
              <AppButton
                variant="outline"
                size="sm"
                onClick={copyTicketUrl}
              >
                <Link className="h-4 w-4" />
              </AppButton>
            </TooltipTrigger>
            <TooltipContent>
              Copy ticket URL
            </TooltipContent>
          </Tooltip>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <AppButton variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </AppButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setEditDialogOpen(true)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Ticket
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setAssigneeDialogOpen(true)}>
                <UserCheck className="h-4 w-4 mr-2" />
                Change Assignee
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setCommentDialogOpen(true)}>
                <MessageSquare className="h-4 w-4 mr-2" />
                Add Comment
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => {
                const params = new URLSearchParams({
                  parentId: ticket.id,
                  parentTitle: ticket.subject
                });
                router.push(`/create?${params.toString()}`);
              }}>
                <Plus className="h-4 w-4 mr-2" />
                Create Sub Ticket
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => setDeleteDialogOpen(true)}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Ticket
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
            <DialogTrigger asChild>
              <AppButton variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </AppButton>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Ticket</DialogTitle>
                <DialogDescription>
                  Update the ticket details below.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleUpdateTicket} className="space-y-4">
                <div>
                  <Label className="mb-2 block">Subject</Label>
                  <AppInput
                    value={editSubject}
                    onChange={(e) => setEditSubject(e.target.value)}
                    placeholder="Ticket subject..."
                    required
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="mb-2 block">Status</Label>
                    <Select value={editStatus} onValueChange={(value: Ticket["status"]) => setEditStatus(value)}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="new">New</SelectItem>
                        <SelectItem value="triage">Triage</SelectItem>
                        <SelectItem value="in_progress">In Progress</SelectItem>
                        <SelectItem value="waiting">Waiting</SelectItem>
                        <SelectItem value="resolved">Resolved</SelectItem>
                        <SelectItem value="closed">Closed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label className="mb-2 block">Priority</Label>
                    <Select value={editPriority} onValueChange={(value: Ticket["priority"]) => setEditPriority(value)}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div>
                  <Label className="mb-2 block">Description</Label>
                  <AppTextarea
                    value={editDescription}
                    onChange={(e) => setEditDescription(e.target.value)}
                    placeholder="Ticket description..."
                    rows={4}
                  />
                </div>
                
                <div className="flex justify-end space-x-2 pt-4">
                  <AppButton
                    type="button"
                    variant="outline"
                    onClick={() => setEditDialogOpen(false)}
                  >
                    Cancel
                  </AppButton>
                  <AppButton type="submit" loading={updating}>
                    Update Ticket
                  </AppButton>
                </div>
              </form>
            </DialogContent>
          </Dialog>

          <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete Ticket</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete this ticket? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-end space-x-2 pt-4">
                <AppButton
                  variant="outline"
                  onClick={() => setDeleteDialogOpen(false)}
                >
                  Cancel
                </AppButton>
                <AppButton
                  variant="destructive"
                  onClick={handleDeleteTicket}
                  loading={deleting}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  <Trash2 className="h-4 w-4 mr-2 text-white" />
                  Delete Ticket
                </AppButton>
              </div>
            </DialogContent>
          </Dialog>

          {/* Assignee Update Dialog */}
          <Dialog open={assigneeDialogOpen} onOpenChange={setAssigneeDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Change Assignee</DialogTitle>
                <DialogDescription>
                  Select a new assignee for this ticket.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label className="mb-2 block">Assignee</Label>
                  <Select value={selectedAssignee} onValueChange={setSelectedAssignee}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select an assignee..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unassigned">Unassigned</SelectItem>
                      {availableUsers.map((user) => (
                        <SelectItem key={user.id} value={user.id}>
                          <div className="flex items-center space-x-2">
                            <Avatar className="h-5 w-5">
                              <AvatarImage src={user.avatar} />
                              <AvatarFallback className="text-xs">{user.name.slice(0, 2)}</AvatarFallback>
                            </Avatar>
                            <span>{user.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end space-x-2 pt-4">
                  <AppButton
                    variant="outline"
                    onClick={() => setAssigneeDialogOpen(false)}
                  >
                    Cancel
                  </AppButton>
                  <AppButton onClick={handleAssigneeUpdate} loading={updating}>
                    Update Assignee
                  </AppButton>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="comments">Comments ({comments.length})</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
            </TabsList>
            
            <TabsContent value="details" className="space-y-4">
              {/* Ticket Details */}
              <AppCard>
                <AppCardHeader>
                  <AppCardTitle>Ticket Details</AppCardTitle>
                </AppCardHeader>
                <AppCardContent>
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-sm text-muted-foreground mb-2">Subject</h3>
                      <p className="text-lg font-medium">{ticket.subject}</p>
                    </div>
                    
                    {ticket.description && (
                      <div>
                        <h3 className="font-medium text-sm text-muted-foreground mb-2">Description</h3>
                        <div className="bg-muted/20 border rounded-lg p-4">
                          <p className="whitespace-pre-wrap">{ticket.description}</p>
                        </div>
                      </div>
                    )}
                    
                    {ticket.tags.length > 0 && (
                      <div>
                        <h3 className="font-medium text-sm text-muted-foreground mb-2">Tags</h3>
                        <div className="flex flex-wrap gap-2">
                          {ticket.tags.map((tag) => (
                            <AppBadge key={tag} variant="secondary">
                              <Tag className="h-3 w-3 mr-1" />
                              {tag}
                            </AppBadge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </AppCardContent>
              </AppCard>
            </TabsContent>

            <TabsContent value="comments" className="space-y-4">
              {/* Comments Section */}
              <AppCard>
                <AppCardHeader>
                  <div className="flex items-center justify-between">
                    <AppCardTitle>Comments ({comments.length})</AppCardTitle>
                    <div className="flex space-x-2">
                      <Dialog open={cannedResponseDialogOpen} onOpenChange={setCannedResponseDialogOpen}>
                        <DialogTrigger asChild>
                          <AppButton variant="outline" size="sm">
                            <FileText className="h-4 w-4 mr-2" />
                            Canned Responses
                          </AppButton>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Select Canned Response</DialogTitle>
                            <DialogDescription>
                              Choose a pre-written response to use in your comment.
                            </DialogDescription>
                          </DialogHeader>
                          <div className="max-h-96 overflow-y-auto space-y-2">
                            {cannedResponses.map((response) => (
                              <div 
                                key={response.id} 
                                className="border rounded-lg p-3 cursor-pointer hover:bg-muted/50 transition-colors"
                                onClick={() => handleCannedResponseSelect(response)}
                              >
                                <div className="flex items-center justify-between mb-2">
                                  <h4 className="font-medium">{response.title}</h4>
                                  <div className="flex space-x-1">
                                    {response.tags.map((tag) => (
                                      <AppBadge key={tag} variant="outline" className="text-xs">
                                        {tag}
                                      </AppBadge>
                                    ))}
                                  </div>
                                </div>
                                <p className="text-sm text-muted-foreground line-clamp-2">
                                  {response.content}
                                </p>
                              </div>
                            ))}
                          </div>
                        </DialogContent>
                      </Dialog>
                      
                      <Dialog open={commentDialogOpen} onOpenChange={setCommentDialogOpen}>
                        <DialogTrigger asChild>
                          <AppButton size="sm">
                            <MessageSquare className="h-4 w-4 mr-2" />
                            Add Comment
                          </AppButton>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Add Comment</DialogTitle>
                            <DialogDescription>
                              Add a comment to this ticket.
                            </DialogDescription>
                          </DialogHeader>
                          <form onSubmit={handleAddComment} className="space-y-4">
                            <div>
                              <Label className="mb-2 block">Comment Type</Label>
                              <Select value={commentType} onValueChange={(value: "public" | "internal") => setCommentType(value)}>
                                <SelectTrigger className="w-full">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="public">Public</SelectItem>
                                  <SelectItem value="internal">Internal</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            
                            <div>
                              <div className="flex items-center justify-between mb-2">
                                <Label>Comment</Label>
                                <AppButton
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setCannedResponseDialogOpen(true)}
                                >
                                  <FileText className="h-4 w-4 mr-1" />
                                  Use Template
                                </AppButton>
                              </div>
                              <AppTextarea
                                value={commentBody}
                                onChange={(e) => setCommentBody(e.target.value)}
                                placeholder="Write your comment..."
                                rows={4}
                                required
                              />
                            </div>
                            
                            <div className="flex justify-end space-x-2 pt-4">
                              <AppButton
                                type="button"
                                variant="outline"
                                onClick={() => setCommentDialogOpen(false)}
                              >
                                Cancel
                              </AppButton>
                              <AppButton type="submit">
                                <Send className="h-4 w-4 mr-2" />
                                Add Comment
                              </AppButton>
                            </div>
                          </form>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                </AppCardHeader>
                <AppCardContent>
                  {comments.length === 0 ? (
                    <div className="text-center py-12">
                      <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No comments yet. Be the first to add one!</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {comments.map((comment) => (
                        <div key={comment.id} className="border rounded-lg p-4 hover:shadow-sm transition-shadow">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={comment.author.avatar} />
                                <AvatarFallback>{comment.author.name.slice(0, 2)}</AvatarFallback>
                              </Avatar>
                              <div>
                                <span className="font-medium text-sm">{comment.author.name}</span>
                                <AppBadge 
                                  variant={comment.type === "public" ? "default" : "secondary"}
                                  className="ml-2"
                                >
                                  {comment.type}
                                </AppBadge>
                              </div>
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {comment.createdAt.toLocaleDateString()} at {comment.createdAt.toLocaleTimeString()}
                            </span>
                          </div>
                          <div className="bg-muted/20 border rounded p-3">
                            <p className="whitespace-pre-wrap">{comment.body}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </AppCardContent>
              </AppCard>
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              {/* Activity Log */}
              <AppCard>
                <AppCardHeader>
                  <AppCardTitle>Activity History</AppCardTitle>
                </AppCardHeader>
                <AppCardContent>
                  <div className="space-y-4">
                    {activityLog.map((activity) => (
                      <div key={activity.id} className="flex items-start space-x-3 pb-4 border-b last:border-b-0">
                        <Avatar className="h-8 w-8 mt-1">
                          <AvatarImage src={activity.user.avatar} />
                          <AvatarFallback>{activity.user.name.slice(0, 2)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className="text-sm">
                              <span className="font-medium">{activity.user.name}</span>
                              <span className="text-muted-foreground ml-1">{activity.details}</span>
                            </p>
                            <span className="text-xs text-muted-foreground">
                              {activity.timestamp.toLocaleDateString()} at {activity.timestamp.toLocaleTimeString()}
                            </span>
                          </div>
                        </div>
                        <div className="flex-shrink-0">
                          {activity.action === "created" && <AlertCircle className="h-4 w-4 text-blue-500" />}
                          {activity.action === "assigned" && <UserCheck className="h-4 w-4 text-green-500" />}
                          {activity.action === "status_changed" && <Settings className="h-4 w-4 text-orange-500" />}
                        </div>
                      </div>
                    ))}
                  </div>
                </AppCardContent>
              </AppCard>
            </TabsContent>
          </Tabs>
        </div>

        {/* Unified Ticket Info Panel - Jira Style */}
        <div className="space-y-6">
          <AppCard>
            <AppCardHeader>
              <AppCardTitle className="text-sm">Ticket Information</AppCardTitle>
            </AppCardHeader>
            <AppCardContent className="space-y-4">
              {/* Status */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status</span>
                <Select value={ticket.status} onValueChange={(value: Ticket["status"]) => handleQuickStatusUpdate(value)}>
                  <SelectTrigger className="w-auto h-auto p-0 border-0 bg-transparent hover:bg-muted/50 rounded-md">
                    <div className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium cursor-pointer ${getStatusColor(ticket.status)}`}>
                      {getStatusIcon(ticket.status)}
                      <span className="ml-1 capitalize">{ticket.status.replace('_', ' ')}</span>
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="new">New</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Priority */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Priority</span>
                <Select value={ticket.priority} onValueChange={(value: Ticket["priority"]) => handleQuickPriorityUpdate(value)}>
                  <SelectTrigger className="w-auto h-auto p-0 border-0 bg-transparent hover:bg-muted/50 rounded-md">
                    <div className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium cursor-pointer ${getPriorityColor(ticket.priority)}`}>
                      <span className="capitalize">{ticket.priority}</span>
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Assignee */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Assignee</span>
                <Select value={selectedAssignee} onValueChange={handleQuickAssigneeUpdate}>
                  <SelectTrigger className="w-auto h-auto p-0 border-0 bg-transparent hover:bg-muted/50 rounded-md">
                    {ticket.assignee ? (
                      <div className="flex items-center space-x-2 px-2 py-1 rounded-md border cursor-pointer hover:bg-muted/50">
                        <Avatar className="h-5 w-5">
                          <AvatarImage src={ticket.assignee.avatar} />
                          <AvatarFallback className="text-xs">{ticket.assignee.name.slice(0, 2)}</AvatarFallback>
                        </Avatar>
                        <span className="text-xs font-medium">{ticket.assignee.name}</span>
                      </div>
                    ) : (
                      <div className="px-2 py-1 rounded-md border border-dashed cursor-pointer hover:bg-muted/50">
                        <span className="text-xs text-muted-foreground">Unassigned</span>
                      </div>
                    )}
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unassigned">Unassigned</SelectItem>
                    {availableUsers.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-5 w-5">
                            <AvatarImage src={user.avatar} />
                            <AvatarFallback className="text-xs">{user.name.slice(0, 2)}</AvatarFallback>
                          </Avatar>
                          <span>{user.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Time Tracking */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Time Estimate</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">
                      {estimatedTime ? formatTimeString(estimatedTime) : 'Not set'}
                    </span>
                    <Dialog open={estimateDialogOpen} onOpenChange={setEstimateDialogOpen}>
                      <DialogTrigger asChild>
                        <AppButton variant="ghost" size="sm" className="h-6 w-6 p-0">
                          <Edit className="h-3 w-3" />
                        </AppButton>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Set Time Estimate</DialogTitle>
                          <DialogDescription>
                            Set the estimated time for this ticket.
                          </DialogDescription>
                        </DialogHeader>
                        <form onSubmit={handleSetEstimate} className="space-y-4">
                          <div>
                            <Label htmlFor="estimate">Estimated Time</Label>
                            <AppInput
                              id="estimate"
                              type="text"
                              value={newEstimate}
                              onChange={(e) => setNewEstimate(e.target.value)}
                              placeholder="e.g., 8h, 2d, 1h 30m"
                              required
                            />
                            <p className="text-xs text-muted-foreground mt-1">
                              Enter time like: 8h, 2 days, 1h 30m, or 8
                            </p>
                          </div>
                          <div className="flex justify-end space-x-2">
                            <AppButton
                              type="button"
                              variant="outline"
                              onClick={() => setEstimateDialogOpen(false)}
                            >
                              Cancel
                            </AppButton>
                            <AppButton type="submit">
                              Set Estimate
                            </AppButton>
                          </div>
                        </form>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Time Logged</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">
                      {timeEntries.length > 0 ? formatTimeString(getTotalTimeLogged()) : '0m'}
                    </span>
                    <Dialog open={timeLogDialogOpen} onOpenChange={setTimeLogDialogOpen}>
                      <DialogTrigger asChild>
                        <AppButton variant="ghost" size="sm" className="h-6 w-6 p-0">
                          <Plus className="h-3 w-3" />
                        </AppButton>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Log Time</DialogTitle>
                          <DialogDescription>
                            Log time spent working on this ticket.
                          </DialogDescription>
                        </DialogHeader>
                        <form onSubmit={handleAddTime} className="space-y-4">
                          <div>
                            <Label htmlFor="timeInput">Time</Label>
                            <AppInput
                              id="timeInput"
                              type="text"
                              value={timeForm.timeInput}
                              onChange={(e) => setTimeForm(prev => ({ ...prev, timeInput: e.target.value }))}
                              placeholder="e.g., 2h 30m, 1.5h, 90m"
                              required
                            />
                            <p className="text-xs text-muted-foreground mt-1">
                              Enter time like: 2h, 30m, 1h 30m, or 1.5
                            </p>
                          </div>
                          <div>
                            <Label htmlFor="time-description">Description (optional)</Label>
                            <AppTextarea
                              id="time-description"
                              value={timeForm.description}
                              onChange={(e) => setTimeForm(prev => ({ ...prev, description: e.target.value }))}
                              placeholder="What did you work on?"
                              rows={3}
                            />
                          </div>
                          <div className="flex justify-end space-x-2">
                            <AppButton
                              type="button"
                              variant="outline"
                              onClick={() => setTimeLogDialogOpen(false)}
                            >
                              Cancel
                            </AppButton>
                            <AppButton type="submit">
                              Log Time
                            </AppButton>
                          </div>
                        </form>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
                
                {/* Time Progress Bar */}
                {estimatedTime && timeEntries.length > 0 && (
                  <div className="space-y-2">
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${Math.min((getTotalTimeLogged() / estimatedTime) * 100, 100)}%` }}
                      />
                    </div>
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>{formatTimeString(getTotalTimeLogged())} logged</span>
                      <span>{formatTimeString(estimatedTime)} estimated</span>
                    </div>
                  </div>
                )}
              </div>

              <Separator />

              {/* Requester */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Requester</span>
                <div 
                  className="flex items-center space-x-2 cursor-pointer hover:bg-muted/50 p-1 rounded"
                  onClick={() => router.push(`/users/${ticket.requester.id}`)}
                >
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={ticket.requester.avatar} />
                    <AvatarFallback className="text-xs">{ticket.requester.name.slice(0, 2)}</AvatarFallback>
                  </Avatar>
                  <span className="text-sm font-medium">{ticket.requester.name}</span>
                </div>
              </div>

              {/* Department */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Department</span>
                <AppBadge variant="secondary" className="text-xs">
                  <Building2 className="h-3 w-3 mr-1" />
                  {ticket.department.name}
                </AppBadge>
              </div>

              {/* Created & Updated */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Created</span>
                <div className="text-right">
                  <p className="text-sm font-medium">{ticket.createdAt.toLocaleDateString()}</p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Updated</span>
                <div className="text-right">
                  <p className="text-sm font-medium">{ticket.updatedAt.toLocaleDateString()}</p>
                </div>
              </div>

              {/* SLA Target */}
              {ticket.slaTarget && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">SLA Target</span>
                  <div className="text-right flex items-center space-x-2">
                    <p className="text-sm font-medium">{ticket.slaTarget.toLocaleDateString()}</p>
                    {ticket.slaTarget < new Date() && (
                      <AppBadge variant="destructive" className="text-xs">
                        Overdue
                      </AppBadge>
                    )}
                  </div>
                </div>
              )}

              <Separator />

              {/* Actions */}
              <div className="space-y-2">
                <div className="flex space-x-2">
                  <AppButton
                    variant="outline"
                    size="sm"
                    onClick={() => setActiveTab("history")}
                    className="flex-1"
                  >
                    <History className="h-4 w-4 mr-1" />
                    History
                  </AppButton>
                  {timeEntries.length > 0 && (
                    <Dialog open={timeHistoryDialogOpen} onOpenChange={setTimeHistoryDialogOpen}>
                      <DialogTrigger asChild>
                        <AppButton variant="outline" size="sm" className="flex-1">
                          <Timer className="h-4 w-4 mr-1" />
                          Time Logs
                        </AppButton>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>Time History</DialogTitle>
                          <DialogDescription>
                            View all time entries logged for this ticket.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="max-h-96 overflow-y-auto space-y-3">
                          {timeEntries.map((entry) => (
                            <div key={entry.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={entry.user.avatar} />
                                <AvatarFallback>{entry.user.name.slice(0, 2)}</AvatarFallback>
                              </Avatar>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between">
                                  <p className="text-sm font-medium">{entry.user.name}</p>
                                  <div className="text-right">
                                    <AppBadge variant="secondary" className="text-xs">
                                      {formatTimeString(entry.hours)}
                                    </AppBadge>
                                    <p className="text-xs text-muted-foreground mt-1">
                                      {entry.createdAt.toLocaleDateString()} at {entry.createdAt.toLocaleTimeString()}
                                    </p>
                                  </div>
                                </div>
                                <p className="text-sm text-muted-foreground mt-1">
                                  {entry.description}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                        <div className="flex justify-between items-center pt-4 border-t">
                          <p className="text-sm font-medium">
                            Total logged: {formatTimeString(getTotalTimeLogged())}
                          </p>
                          <AppButton onClick={() => setTimeHistoryDialogOpen(false)}>
                            Close
                          </AppButton>
                        </div>
                      </DialogContent>
                    </Dialog>
                  )}
                </div>
                
                <AppButton
                  variant="outline"
                  size="sm"
                  onClick={() => setIsWatching(!isWatching)}
                  className="w-full"
                >
                  {isWatching ? (
                    <>
                      <EyeOff className="h-4 w-4 mr-1" />
                      Stop Watching
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4 mr-1" />
                      Watch Ticket
                    </>
                  )}
                </AppButton>
              </div>
            </AppCardContent>
          </AppCard>

        </div>
      </div>
      </div>
    </TooltipProvider>
  );
}