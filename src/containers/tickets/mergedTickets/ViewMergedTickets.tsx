"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON><PERSON><PERSON>, GitMerge, Eye } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  SectionHeader,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  AppBadge,
  AppEmpty,
} from "@/components/ui-toolkit";

import { useGetMergedTicketsQuery } from "@/services/api/tickets";
import { MergedTicketData } from "@/services/api/tickets";

function ViewMergedTickets() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = React.useState(1);
  const pageSize = 20;

  const { data: apiResponse, isLoading } = useGetMergedTicketsQuery({
    page: currentPage,
    size: pageSize
  });

  const mergedTickets = apiResponse?.data || [];

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <SectionHeader
        title="Merged Tickets"
        description="View all merged tickets with their sub-tickets"
        actions={
          <AppButton
            variant="outline"
            icon={<ArrowLeft className="h-4 w-4" />}
            onClick={() => router.back()}
          >
            Back
          </AppButton>
        }
      />

      {mergedTickets.length === 0 ? (
        <AppEmpty
          title="No merged tickets found"
          description="There are no merged tickets available"
        />
      ) : (
        <div className="space-y-4">
          {mergedTickets.map((mergedTicket: MergedTicketData, index: number) => (
            <AppCard key={index}>
              <AppCardHeader>
                <AppCardTitle className="flex items-center gap-2">
                  <GitMerge className="h-5 w-5" />
                  {mergedTicket.mainTicket.ticketKey}: {mergedTicket.mainTicket.title}
                </AppCardTitle>
              </AppCardHeader>
              <AppCardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Status:</span>
                      <AppBadge status={mergedTicket.mainTicket.status as any} className="ml-2">
                        {mergedTicket.mainTicket.status}
                      </AppBadge>
                    </div>
                    <div>
                      <span className="font-medium">Priority:</span>
                      <AppBadge priority={mergedTicket.mainTicket.priority as any} className="ml-2">
                        {mergedTicket.mainTicket.priority}
                      </AppBadge>
                    </div>
                    <div>
                      <span className="font-medium">Created:</span>
                      <span className="ml-2">{new Date(mergedTicket.mainTicket.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Merged Tickets ({mergedTicket.mergedTickets.length})</h4>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Ticket Key</TableHead>
                          <TableHead>Title</TableHead>
                          <TableHead>Requester</TableHead>
                          <TableHead>Assignee</TableHead>
                          <TableHead>Comments</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mergedTicket?.mergedTickets?.map((subTicket) => (
                          <TableRow key={subTicket.id}>
                            <TableCell className="font-medium">{subTicket.id}</TableCell>
                            <TableCell className="max-w-xs truncate">{subTicket.title}</TableCell>
                            <TableCell>{subTicket.requester?.name || 'N/A'}</TableCell>
                            <TableCell>{subTicket.assigneeId || 'Unassigned'}</TableCell>
                            <TableCell>{subTicket.comments}</TableCell>
                            <TableCell>
                              <AppButton
                                variant="ghost"
                                size="sm"
                                icon={<Eye className="h-4 w-4" />}
                                onClick={() => router.push(`/tickets/${subTicket.id}`)}
                              >
                                View
                              </AppButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </AppCardContent>
            </AppCard>
          ))}
        </div>
      )}
    </div>
  );
}

export default ViewMergedTickets;