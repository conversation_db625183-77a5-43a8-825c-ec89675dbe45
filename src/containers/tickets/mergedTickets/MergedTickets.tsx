"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft, GitMerge, Search } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardHeader,
  AppCard<PERSON>itle,
  AppButton,
  SectionHeader,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  AppBadge,
  Checkbox,
  AppEmpty,
  Separator,
  AppInput,
} from "@/components/ui-toolkit";

import { type Ticket } from "@/lib/demo/types";
import { useGetTicketsQuery } from "@/services/api/tickets";
import { useMergeTicketsMutation } from "@/services/api/tickets";
import { type TicketData } from "@/services/api/tickets";
import { toast } from "sonner";
function MergedTickets() {
  const router = useRouter();
  const [selectedTickets, setSelectedTickets] = React.useState<string[]>([]);
  const [primaryTicket, setPrimaryTicket] = React.useState<string>("");
  const [searchTerm, setSearchTerm] = React.useState<string>("");

  const { data: apiResponse, isLoading } = useGetTicketsQuery({
    search: searchTerm,
    page: 1,
    size: 100
  });

  const [mergeTickets, { isLoading: isMerging }] = useMergeTicketsMutation();

  const [tickets, setTickets] = React.useState<Ticket[]>([]);

  React.useEffect(() => {
    if (apiResponse?.data) {
      const convertedTickets: Ticket[] = apiResponse.data.map((apiTicket: TicketData) => ({
        id: apiTicket._id,
        number: apiTicket.ticketKey,
        ticketKey: apiTicket.ticketKey,
        subject: apiTicket.title,
        status: apiTicket.status as any,
        priority: apiTicket.priority as any,
        requester: apiTicket.requester,
        assignee: undefined, // TODO: map from assigneeId if needed
        createdAt: new Date(apiTicket.createdAt),
        updatedAt: new Date(apiTicket.updatedAt || apiTicket.createdAt),
        department: {
          id: 'dept-1',
          name: 'Support',
          status: 'active' as const,
          memberCount: 0
        },
        tags: apiTicket.tags || [],
        description: apiTicket.description
      }));
      setTickets(convertedTickets);
    }
  }, [apiResponse]);

  const handleSelectTicket = (ticketId: string) => {
    setSelectedTickets(prev =>
      prev.includes(ticketId)
        ? prev.filter(id => id !== ticketId)
        : [...prev, ticketId]
    );
  };

  const handleMergeTickets = async () => {
    if (selectedTickets.length < 2) {
      toast.error("Please select at least 2 tickets to merge");
      return;
    }

    if (!primaryTicket) {
      toast.error("Please select a primary ticket");
      return;
    }

    try {
      // Filter out the primary ticket from selected tickets for merging
      const ticketIdsToMerge = selectedTickets.filter(id => id !== primaryTicket);
      
      const result = await mergeTickets({
        mainTicketId: primaryTicket,
        ticketIds: ticketIdsToMerge,
        comment: "Tickets merged via UI"
      }).unwrap();

      if (result.success) {
        toast.success("Tickets merged successfully!");
        router.push("/tickets");
      } else {
        toast.error("Failed to merge tickets: " + (result.message || "Unknown error"));
      }
    } catch (error) {
      console.error("Failed to merge tickets:", error);
      toast.error("Failed to merge tickets");
    }
  };

  const selectedTicketObjects = tickets.filter(ticket => selectedTickets.includes(ticket.id));

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* <SectionHeader
        title="Merge Tickets"
        description="Select multiple tickets to merge them into one"
        actions={
          <AppButton
            variant="outline"
            icon={<ArrowLeft className="h-4 w-4" />}
            onClick={() => router.back()}
          >
            Back
          </AppButton>
        }
      /> */}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Ticket Selection */}
        <AppCard>
          <AppCardHeader>
            <AppCardTitle>Select Tickets to Merge</AppCardTitle>
          </AppCardHeader>
          <AppCardContent>
            <div className="mb-4">
              <AppInput
                label="Search Tickets"
                placeholder="Search by ticket key or subject..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            {tickets.length === 0 ? (
              <AppEmpty
                title="No tickets found"
                description="There are no tickets available to merge"
              />
            ) : (
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedTickets.length === tickets.length}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedTickets(tickets.map(t => t.id));
                            } else {
                              setSelectedTickets([]);
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead>Key</TableHead>
                      <TableHead>Subject</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Priority</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tickets.map((ticket) => (
                      <TableRow key={ticket.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedTickets.includes(ticket.id)}
                            onCheckedChange={() => handleSelectTicket(ticket.id)}
                          />
                        </TableCell>
                        <TableCell>{ticket.ticketKey}</TableCell>
                        <TableCell className="max-w-xs truncate">
                          {ticket.subject}
                        </TableCell>
                        <TableCell>
                          <AppBadge status={ticket.status}>
                            {ticket.status}
                          </AppBadge>
                        </TableCell>
                        <TableCell>
                          <AppBadge priority={ticket.priority}>
                            {ticket.priority}
                          </AppBadge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </AppCardContent>
        </AppCard>

        {/* Merge Configuration */}
        <AppCard>
          <AppCardHeader>
            <AppCardTitle>Merge Configuration</AppCardTitle>
          </AppCardHeader>
          <AppCardContent>
            {selectedTickets.length < 2 ? (
              <AppEmpty
                title="Select tickets"
                description="Please select at least 2 tickets to merge"
              />
            ) : (
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Selected Tickets ({selectedTickets.length})</h4>
                  <div className="space-y-2">
                    {selectedTicketObjects.map((ticket) => (
                      <div key={ticket.id} className="flex items-center space-x-2 p-2 border rounded">
                        <span className="text-sm font-medium">{ticket.ticketKey}</span>
                        <span className="text-sm text-muted-foreground truncate">
                          {ticket.subject}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium mb-2">Choose Primary Ticket</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    The primary ticket will retain its key and basic information. Other tickets will be merged into it.
                  </p>
                  <div className="space-y-2">
                    {selectedTicketObjects.map((ticket) => (
                      <div
                        key={ticket.id}
                        className={`flex items-center space-x-2 p-3 border rounded cursor-pointer ${
                          primaryTicket === ticket.id ? "border-primary bg-primary/5" : ""
                        }`}
                        onClick={() => setPrimaryTicket(ticket.id)}
                      >
                        <Checkbox
                          checked={primaryTicket === ticket.id}
                          onChange={() => setPrimaryTicket(ticket.id)}
                        />
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{ticket.ticketKey}</span>
                            <AppBadge status={ticket.status}>
                              {ticket.status}
                            </AppBadge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {ticket.subject}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                <AppButton
                  onClick={handleMergeTickets}
                  disabled={!primaryTicket || isMerging}
                  icon={<GitMerge className="h-4 w-4" />}
                  className="w-full"
                >
                  {isMerging ? "Merging..." : `Merge ${selectedTickets.length} Tickets`}
                </AppButton>
              </div>
            )}
          </AppCardContent>
        </AppCard>
      </div>
    </div>
  );
}

export default MergedTickets;