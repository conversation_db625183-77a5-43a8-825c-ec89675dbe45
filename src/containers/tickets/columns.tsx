import React from 'react';
import { ZGridColumn } from '@/shared/ZGrid';
import { Status } from '@/redux/slices/statuses';
import { GitMerge } from 'lucide-react';

// Dynamic status badge renderer
const createStatusBadge = (statuses: Status[]) => (value: string) => {
  const status = statuses.find(s => s.name === value);
  const color = status?.color || '#6b7280'; // Default gray color
  
  return (
    <span 
      className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors"
      style={{ 
        borderColor: color, 
        backgroundColor: 'white',
        color: color 
      }}
    >
      {value || '-'}
    </span>
  );
};

// Priority badge renderer
const priorityBadge = (value: string) => {
  const priorityMap: Record<string, { variant: any; className: string }> = {
    'Top Priority': { variant: 'destructive', className: 'bg-red-100 text-red-800 border-red-200' },
    'Urgent': { variant: 'default', className: 'bg-orange-100 text-orange-800 border-orange-200' },
    'Normal': { variant: 'default', className: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
    'Low': { variant: 'secondary', className: 'bg-green-100 text-green-800 border-green-200' },
    'Later': { variant: 'secondary', className: 'bg-gray-100 text-gray-800 border-gray-200' },
  };

  const config = priorityMap[value] || { variant: 'default', className: 'bg-gray-100 text-gray-800' };
  
  return (
    <span className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors ${config.className}`}>
      {value || '-'}
    </span>
  );
};

// Merged tickets indicator
const mergedTicketsRenderer = (value: any) => {
  const count = value?.length || 0;
  if (count === 0) return null;
  
  return (
    <div className="flex items-center gap-1">
      <GitMerge className="h-4 w-4 text-blue-500" />
      <span className="text-xs text-blue-600 font-medium">{count}</span>
    </div>
  );
};

export const getTicketColumns = (statuses: Status[]): ZGridColumn[] => [
  { 
    field: 'ticketKey', 
    headerName: 'Ticket', 
    width: '120px', 
    sortable: true 
  },
  { 
    field: 'title', 
    headerName: 'Title', 
    width: '240px', 
    sortable: true 
  },
  { 
    field: 'mergedTickets', 
    headerName: 'Merged', 
    width: '80px', 
    sortable: false,
    cellRenderer: mergedTicketsRenderer
  },
  { 
    field: 'status', 
    headerName: 'Status', 
    width: '110px', 
    sortable: true,
    cellRenderer: createStatusBadge(statuses)
  },
  { 
    field: 'priority', 
    headerName: 'Priority', 
    width: '100px', 
    sortable: true,
    cellRenderer: priorityBadge
  },
  { 
    field: 'requester.name', 
    headerName: 'Requester', 
    width: '140px', 
    sortable: false 
  },
  { 
    field: 'assignee.name', 
    headerName: 'Assignee', 
    width: '140px', 
    sortable: false 
  },
  { 
    field: 'department.name', 
    headerName: 'Department', 
    width: '140px', 
    sortable: false 
  },
  { 
    field: 'createdAt', 
    headerName: 'Created', 
    width: '120px', 
    sortable: true, 
    cellRenderer: (value) => new Date(value).toLocaleDateString() 
  },
];

export default getTicketColumns;
