"use client";

import * as React from "react";
import { useR<PERSON><PERSON>, useSearchPara<PERSON> } from "next/navigation";
import { Plus, Filter, Eye, Edit, Trash2, Kanban, List, X, ChevronLeft, ChevronRight, GitMerge } from "lucide-react";
import {
  App<PERSON>ard,
  AppCardContent,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  SectionHeader,
  FilterBar,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  AppBadge,
  KebabActions,
  AppEmpty,
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
  Checkbox,
  Separator,
} from "@/components/ui-toolkit";

import { type Ticket } from "@/lib/demo/types";
import { useGetTicketByIdQuery, useGetTicketsQuery } from "@/services/api/tickets";
import TicketsContainer from '@/containers/tickets';
import { type FilterChip } from "@/components/ui-toolkit";
import TicketsFilterDrawer from '@/components/ui/tickets-filter-drawer';
import { UserFilter } from '@/components/ui/user-filter';
import { DepartmentFilter } from '@/components/ui/department-filter';
import { useGetStatusesQuery } from '@/redux/slices/statuses/statusSlice';
import { useAuth } from '@/hooks/useAuth';

// Import the ticket details component content
import TicketDetailsView from "../../../components/ticket-details-view";
import { KanbanBoard } from '@/components/KanbanBoard';

interface OpenTab {
  id: string;
  ticketId: string;
  ticketKey?: string;
  title: string;
  isActive: boolean;
}

function AllTickets() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { tenant } = useAuth();
  const [tickets, setTickets] = React.useState<Ticket[]>([]);
  const [loading, setLoading] = React.useState(true);

  // Use real API for kanban tickets to match table view
  const { data: apiResponse, isLoading: isApiLoading, error: apiError } = useGetTicketsQuery({
    search: '',
    page: 1,
    size: 100 // Get more tickets for kanban view
  });

  // Fetch statuses from API
  const { data: statusesData } = useGetStatusesQuery(tenant?.tenantId || '', {
    skip: !tenant?.tenantId,
  });

  // Transform statuses for the filter drawer
  const statusOptions = React.useMemo(() => {
    if (!statusesData) return [];
    return statusesData.map(status => ({
      label: status.name,
      value: status._id,
    }));
  }, [statusesData]);
  const [selectedTickets, setSelectedTickets] = React.useState<string[]>([]);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [filters, setFilters] = React.useState<FilterChip[]>([]);
  const [view, setView] = React.useState<"table" | "kanban">("table");

  // New filter state for TicketsContainer
  const [filterStatus, setFilterStatus] = React.useState<string>("");
  const [filterPriority, setFilterPriority] = React.useState<string>("");
  const [filterSearch, setFilterSearch] = React.useState<string>("");
  const [filterAssignees, setFilterAssignees] = React.useState<string[]>([]);
  const [filterDepartments, setFilterDepartments] = React.useState<string[]>([]);
  const [filterSortBy, setFilterSortBy] = React.useState<string>("updatedAt");
  const [filterSortOrder, setFilterSortOrder] = React.useState<string>("desc");
  const [currentPage, setCurrentPage] = React.useState<number>(1);
  const [pageSize, setPageSize] = React.useState<number>(20);

  // Tab management state
  const [openTabs, setOpenTabs] = React.useState<OpenTab[]>([]);
  const [activeTabId, setActiveTabId] = React.useState<string | null>(null);
  const [showMainList, setShowMainList] = React.useState(true);

  // Ref to prevent URL effect from interfering with tab operations
  const openingTabRef = React.useRef<string | null>(null);

  React.useEffect(() => {
    if (apiResponse?.data) {
      // Convert API tickets to the expected Ticket format
      const convertedTickets: Ticket[] = apiResponse.data.map((apiTicket: any) => ({
        id: apiTicket._id,
        number: apiTicket.ticketKey,
        ticketKey: apiTicket.ticketKey,
        subject: apiTicket.title || apiTicket.subject,
        status: apiTicket.status.toLowerCase().replace(/\s+/g, '_'),
        priority: apiTicket.priority,
        requester: apiTicket.requester,
        assignee: apiTicket.assignee,
        createdAt: new Date(apiTicket.createdAt),
        updatedAt: new Date(apiTicket.updatedAt || apiTicket.createdAt),
        department: apiTicket.department || {
          id: 'dept-1',
          name: 'Support',
          status: 'active' as const,
          memberCount: 0
        },
        tags: apiTicket.tags || [],
        description: apiTicket.description,
        subTaskCount: apiTicket.subTaskCount || 0
      }));
      setTickets(convertedTickets);
    }
    setLoading(isApiLoading);
  }, [apiResponse, isApiLoading]);

  const handleSelectTicket = (ticketId: string) => {
    setSelectedTickets(prev =>
      prev.includes(ticketId)
        ? prev.filter(id => id !== ticketId)
        : [...prev, ticketId]
    );
  };

  const handleSelectAll = () => {
    setSelectedTickets(
      selectedTickets.length === tickets.length ? [] : tickets.map(t => t.id)
    );
  };

  const handleRemoveFilter = (filterId: string) => {
    setFilters(prev => prev.filter(f => f.id !== filterId));
  };

  // Filter handlers for TicketsContainer
  const handleStatusChange = (value: string) => {
    setFilterStatus(value);
    setCurrentPage(1);
  };

  const handlePriorityChange = (value: string) => {
    setFilterPriority(value);
    setCurrentPage(1);
  };

  const handleSearchChange = (value: string) => {
    setFilterSearch(value);
    setCurrentPage(1);
  };

  const handleAssigneeChange = (value: string[]) => {
    setFilterAssignees(value);
    setCurrentPage(1);
  };

  const handleDepartmentChange = (value: string[]) => {
    setFilterDepartments(value);
    setCurrentPage(1);
  };

  const handlePageSizeChange = (value: number) => {
    setPageSize(value);
    setCurrentPage(1);
  };

  const handleSortChange = (value: string) => {
    const [field, order] = value.split('-');
    setFilterSortBy(field);
    setFilterSortOrder(order);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleResetFilters = () => {
    setFilterStatus("");
    setFilterPriority("");
    setFilterSearch("");
    setFilterAssignees([]);
    setFilterDepartments([]);
    setFilterSortBy("updatedAt");
    setFilterSortOrder("desc");
    setPageSize(20);
    setCurrentPage(1);
  };

  const handleDeleteTicket = React.useCallback(async (ticketId: string) => {
    try {
      // Note: You'll need to implement delete API call here
      // await deleteTicketAPI(ticketId);
      setTickets(prev => prev.filter(t => t.id !== ticketId));
      setSelectedTickets(prev => prev.filter(id => id !== ticketId));
      // Close tab if it's open
      closeTicketTab(ticketId);
    } catch (error) {
      console.error("Failed to delete ticket:", error);
    }
  }, []);

  // Tab management functions
  const openTicketTab = React.useCallback((ticket: Ticket) => {
    const existingTab = openTabs.find(tab => tab.ticketId === ticket.id);

    // Update URL with ticket key
    const ticketKey = ticket.ticketKey || ticket.number;
    // mark as opening to prevent URL-effect from duplicating the tab
    openingTabRef.current = ticketKey;
    router.replace(`/tickets?ticket=${ticketKey}`, { scroll: false });

    if (existingTab) {
      // Switch to existing tab
      setActiveTabId(existingTab.id);
      setShowMainList(false);
    } else {
      // Create new tab
      const newTab: OpenTab = {
        id: `tab-${ticket.id}-${Date.now()}`,
        ticketId: ticket.id,
        ticketKey: ticket.ticketKey || ticket.number,
        title: `${(ticket.subject && ticket.subject.length > 20) ? ticket.subject.substring(0, 20) + '...' : (ticket.subject || 'Untitled')}`,
        isActive: true
      };

      // Deactivate all other tabs
      setOpenTabs(prev => [...prev.map(tab => ({ ...tab, isActive: false })), newTab]);
      setActiveTabId(newTab.id);
      setShowMainList(false);
    }
  }, [openTabs, router]);

  // Handle URL parameters to open ticket tabs
  React.useEffect(() => {
    const ticketParam = searchParams.get('ticket');

    // Skip if we're in the middle of opening/closing tabs or if no ticket param
    if (!ticketParam || openingTabRef.current) {
      return;
    }

    // Check if tab is already open
    const existingTab = openTabs.find(tab => tab.ticketKey === ticketParam);
    if (existingTab) {
      setActiveTabId(existingTab.id);
      setShowMainList(false);
      return;
    }

    // Find ticket by ticketKey or number
    const ticket = tickets.find(t => t.ticketKey === ticketParam || t.number === ticketParam);
    if (ticket) {
      openTicketTab(ticket);
    }
  }, [searchParams, tickets, openTabs, openTicketTab]);

  const closeTicketTab = (ticketId: string) => {
    const tabToClose = openTabs.find(tab => tab.ticketId === ticketId);
    if (!tabToClose) return;

    // Prevent URL effect from reopening this tab
    openingTabRef.current = 'closing';

    const updatedTabs = openTabs.filter(tab => tab.ticketId !== ticketId);

    // If closing the active tab
    if (tabToClose.id === activeTabId) {
      if (updatedTabs.length > 0) {
        // Switch to the last tab or first available tab
        const nextTab = updatedTabs[updatedTabs.length - 1];
        const nextTabWithActive = updatedTabs.map(tab =>
          tab.id === nextTab.id ? { ...tab, isActive: true } : { ...tab, isActive: false }
        );
        setOpenTabs(nextTabWithActive);
        setActiveTabId(nextTab.id);

        // Update URL to the next active tab's ticket
        if (nextTab.ticketKey) {
          router.replace(`/tickets?ticket=${nextTab.ticketKey}`, { scroll: false });
        }
      } else {
        // No tabs left, show main list and clear URL
        setOpenTabs([]);
        setActiveTabId(null);
        setShowMainList(true);
        router.replace('/tickets', { scroll: false });
      }
    } else {
      // Just remove the tab without changing active state
      setOpenTabs(updatedTabs);
    }

    // Clear the ref after a delay
    setTimeout(() => {
      openingTabRef.current = null;
    }, 200);
  };

  const switchToTab = (tabId: string) => {
    const targetTab = openTabs.find(tab => tab.id === tabId);
    if (targetTab) {
      // Update URL with the ticket key
      const ticketKey = targetTab.ticketKey;
      if (ticketKey) {
        router.replace(`/tickets?ticket=${ticketKey}`, { scroll: false });
      }

      setOpenTabs(prev => prev.map(tab => ({ ...tab, isActive: tab.id === tabId })));
      setActiveTabId(tabId);
      setShowMainList(false);
    }
  };

  const showMainTicketsList = () => {
    // Prevent URL effect from interfering
    openingTabRef.current = 'main';

    // Clear URL parameters when showing main list
    router.replace('/tickets', { scroll: false });

    setOpenTabs(prev => prev.map(tab => ({ ...tab, isActive: false })));
    setActiveTabId(null);
    setShowMainList(true);

    // Clear the ref after a delay
    setTimeout(() => {
      openingTabRef.current = null;
    }, 200);
  };

  const handleTicketUpdate = (updatedTicket: Ticket) => {
    setTickets(prev => prev.map(t => t.id === updatedTicket.id ? updatedTicket : t));
    // Update tab title if needed
    setOpenTabs(prev => prev.map(tab =>
      tab.ticketId === updatedTicket.id
        ? { ...tab, title: `${updatedTicket.number} - ${updatedTicket.subject.length > 20 ? updatedTicket.subject.substring(0, 20) + '...' : updatedTicket.subject}` }
        : tab
    ));
  };

  const handleTicketDelete = (ticketId: string) => {
    handleDeleteTicket(ticketId);
  };

  const getTicketActions = (ticket: Ticket) => [
    {
      label: "View Details",
      onClick: () => openTicketTab(ticket),
      icon: <Eye className="h-4 w-4" />,
    },
    {
      label: "Edit Ticket",
      onClick: () => openTicketTab(ticket),
      icon: <Edit className="h-4 w-4" />,
    },
    {
      label: "Delete",
      onClick: () => handleDeleteTicket(ticket.id),
      icon: <Trash2 className="h-4 w-4" />,
      variant: "destructive" as const,
      separator: true,
    },
  ];

  const filteredTickets = React.useMemo(() => {
    return tickets.filter(ticket =>
      ticket.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.requester.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [tickets, searchQuery]);

  const ticketsByStatus = React.useMemo(() => {
    if (!statusesData) return {};
    return statusesData.reduce((acc, status) => {
      const statusKey = status.name.toLowerCase().replace(/\s+/g, '_');
      acc[statusKey] = filteredTickets.filter(ticket => ticket.status === statusKey);
      return acc;
    }, {} as Record<string, Ticket[]>);
  }, [filteredTickets, statusesData]);

  // Memoize create ticket handler
  const handleCreateTicket = React.useCallback((status?: string) => {
    router.push("/create");
  }, [router]);

  const renderKanbanView = React.useCallback(() => {
    if (!statusesData) return <div>Loading statuses...</div>;

    const columns: { id: string; title: string; tickets: any[] }[] = statusesData.map(status => {
      const statusKey = status.name.toLowerCase().replace(/\s+/g, '_');
      const statusTickets = ticketsByStatus[statusKey] || [];
      const mappedTickets = statusTickets.map(ticket => ({
        id: ticket.id,
        title: ticket.subject || 'Untitled',
        ticketKey: ticket.ticketKey || ticket.number,
        status: ticket.status,
        priority: ticket.priority === 'Urgent' ? 'High' : ticket.priority === 'Normal' ? 'Medium' : ticket.priority === 'Low' ? 'Low' : 'Medium',
        assignee: typeof ticket.assignee === 'string' ? ticket.assignee : ticket.assignee?.name || '',
        requester: typeof ticket.requester === 'string' ? ticket.requester : ticket.requester?.name || '',
        subTickets: [] // Assuming no subTickets in current data
      }));
      return {
        id: statusKey,
        title: status.name,
        tickets: mappedTickets
      };
    });

    return (
      <KanbanBoard
        columns={columns}
        onCreateTicket={(columnId) => handleCreateTicket(columnId)}
        onTicketClick={(ticket) => {
          // Find the original ticket
          const originalTicket = tickets.find(t => t.id === ticket.id);
          if (originalTicket) openTicketTab(originalTicket);
        }}
      />
    );
  }, [statusesData, ticketsByStatus, handleCreateTicket, openTicketTab, tickets]);

  if (loading) {
    return (
      <div className="space-y-4 p-4">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  const renderTableView = () => (
    <div>
      <TicketsContainer
        status={filterStatus}
        priority={filterPriority}
        search={filterSearch}
        assigneeId={filterAssignees}
        departmentId={filterDepartments}
        sortBy={filterSortBy}
        sortOrder={filterSortOrder}
        page={currentPage}
        size={pageSize}
        onPageChange={handlePageChange}
        onRowClick={(row: any) => {
          // Create a ticket object from the row data for the tab
          const ticket: Ticket = {
            id: row.id,
            number: row.ticketKey || row.number,
            ticketKey: row.ticketKey,
            subject: row.title || row.subject,
            status: row.status,
            priority: row.priority,
            requester: row.requester,
            assignee: row.assignee,
            department: row.department || {
              id: 'dept-1',
              name: 'Support',
              status: 'active' as const,
              memberCount: 0
            },
            createdAt: new Date(row.createdAt),
            tags: [],
            updatedAt: new Date(row.createdAt || new Date().toISOString()),
          };
          openTicketTab(ticket);
        }}
      />
    </div>
  );

  // Draggable Ticket Component - moved to separate file

  // Droppable Column Component - moved to separate file

  return (
    <div className={`space-y-4 p-4 ${view === 'kanban' ? 'h-screen flex flex-col mt-2 overflow-hidden' : ''}`}>
      {/* Only show header when on main list view and no tabs are open */}
      {showMainList && openTabs.length === 0 && (
        <SectionHeader
          title="Tickets"
          description="Manage and track all support tickets"
          actions={
            <div className="flex items-center space-x-2">
              {/* phase 2 */}
              <Tabs value={view} onValueChange={(v) => setView(v as "table" | "kanban")}>
                <TabsList>
                  <TabsTrigger value="table">
                    <List className="h-4 w-4 mr-1" />
                    Table
                  </TabsTrigger>
                  <TabsTrigger value="kanban">
                    <Kanban className="h-4 w-4 mr-1" />
                    Kanban
                  </TabsTrigger>
                </TabsList>
              </Tabs>
              <AppButton icon={<Plus className="h-4 w-4" />} onClick={() => router.push("/create")}>
                Create New
              </AppButton>
              <AppButton icon={<GitMerge className="h-4 w-4" />} onClick={() => router.push("/merge-tickets")}>
                Merge Tickets
              </AppButton>
            </div>
          }
        />
      )}

      {/* Ticket Tabs */}
      {openTabs.length > 0 && (
        <div className="bg-background border-b">
          <div className="flex items-center space-x-1 overflow-x-auto">
            {/* Main Tab */}
            <button
              className={`flex items-center px-3 py-2 text-sm font-medium rounded-t-lg whitespace-nowrap ${showMainList
                ? "bg-background border-t border-l border-r border-b-background text-foreground"
                : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
                }`}
              onClick={showMainTicketsList}
            >
              <List className="h-4 w-4 mr-2" />
              Main
            </button>

            {/* Ticket Tabs */}
            {openTabs.map((tab) => (
              <div key={tab.id} className="flex items-center relative">
                <button
                  role="tab"
                  aria-selected={tab.id === activeTabId}
                  title={tab.title}
                  className={`group relative flex items-center gap-3 px-3 py-2 text-left rounded-t-xl min-w-[220px] max-w-[280px] border transition
    ${tab.id === activeTabId
                      ? "bg-background text-foreground border-border border-b-transparent shadow-sm"
                      : "text-muted-foreground hover:text-foreground hover:bg-muted/50 border-transparent"
                    }`}
                  onClick={() => switchToTab(tab.id)}
                >
                  <div className="flex flex-col leading-tight">
                    <span className={`${tab.id === activeTabId ? "text-primary" : "text-muted-foreground"} text-[10px] uppercase tracking-wide font-medium`}>
                      {(tab.ticketKey ?? tab.ticketId ?? tab.id) as string}
                    </span>
                    <span className="text-sm font-semibold max-w-[200px] truncate">
                      {tab.title}
                    </span>
                  </div>
                </button>

                <button
                  type="button"
                  className="absolute right-2 top-1/2 -translate-y-1/2 inline-flex items-center justify-center h-5 w-5 rounded-full hover:bg-muted group-hover:opacity-100 transition"
                  onClick={(e) => {
                    e.stopPropagation();
                    closeTicketTab(tab.ticketId ?? tab.id);
                  }}
                  aria-label="Close tab"
                >
                  <X className="h-3 w-3 text-black" />
                </button>
              </div>
            ))}

            {/* Scroll buttons if needed */}
            {/* {openTabs.length > 5 && (
              <div className="flex items-center space-x-1 pl-2">
                <AppButton variant="ghost" size="sm">
                  <ChevronLeft className="h-4 w-4" />
                </AppButton>
                <AppButton variant="ghost" size="sm">
                  <ChevronRight className="h-4 w-4" />
                </AppButton>
              </div>
            )} */}
          </div>
        </div>
      )}

      {/* Content Area */}
      {/* FilterBar always shown */}
      {showMainList && (
        <FilterBar
          searchValue={searchQuery}
          onSearchChange={setSearchQuery}
          searchPlaceholder="Search tickets..."
          chips={filters}
          onRemoveChip={handleRemoveFilter}
          leftActions={
            <div className="flex items-center space-x-2">
              <UserFilter
                selectedUsers={filterAssignees}
                onUserChange={handleAssigneeChange}
              />
              <DepartmentFilter
                selectedDepartments={filterDepartments}
                onDepartmentChange={handleDepartmentChange}
              />
            </div>
          }
          actions={
            <div className="flex items-center gap-2">
              <div className="h-10 flex items-center" style={{ height: '40px' }}>
                <TicketsFilterDrawer
                  status={filterStatus}
                  priority={filterPriority}
                  search={filterSearch}
                  sortBy={filterSortBy}
                  sortOrder={filterSortOrder}
                  pageSize={pageSize}
                  statuses={statusOptions}
                  onStatusChange={handleStatusChange}
                  onPriorityChange={handlePriorityChange}
                  onSearchChange={handleSearchChange}
                  onSortChange={handleSortChange}
                  onPageSizeChange={handlePageSizeChange}
                  onResetFilters={handleResetFilters}
                />
              </div>
              {
                showMainList && openTabs.length === 0 ||
                <AppButton icon={<Plus className="h-4 w-4" />} onClick={() => router.push("/create")} className="h-10">
                  Create New
                </AppButton>
              }

            </div>
          }
        />
      ) }

      {showMainList ? (
        <div className={view === 'kanban' ? 'flex-1 overflow-hidden min-h-0' : ''}>
          <div className={view === 'kanban' ? 'mt-2 h-full overflow-hidden' : ''}>
            {view === "table" ? renderTableView() : renderKanbanView()}
          </div>
        </div>
      ) : (
        // Render the active ticket tab content
        activeTabId && (
          <TicketDetailsView
            key={openTabs.find(tab => tab.id === activeTabId)?.ticketKey || openTabs.find(tab => tab.id === activeTabId)?.ticketId}
            ticketId={openTabs.find(tab => tab.id === activeTabId)?.ticketKey || openTabs.find(tab => tab.id === activeTabId)?.ticketId || ""}
            onTicketUpdate={handleTicketUpdate}
            onTicketDelete={handleTicketDelete}
          />
        )
      )}
    </div>
  );
}

export default AllTickets;