import * as React from "react";
import { Plus } from "lucide-react";
import { useDroppable } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { AppCard, AppCardContent, App<PERSON>ardHeader, AppCardTitle, AppBadge } from "@/components/ui-toolkit";
import { type Ticket } from "@/lib/demo/types";
import { DraggableTicket } from "./DraggableTicket";

interface DroppableColumnProps {
  status: string;
  statusTickets: Ticket[];
  onOpenTicket: (ticket: Ticket) => void;
  onCreateTicket?: (status: string) => void;
}

export const DroppableColumn: React.FC<DroppableColumnProps> = React.memo(({ status, statusTickets, onOpenTicket, onCreateTicket }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: status,
  });

  // Memoize ticket IDs to prevent unnecessary re-renders
  const ticketIds = React.useMemo(() => statusTickets.map(t => t.id), [statusTickets]);

  // Memoize onOpenTicket handler
  const handleOpenTicket = React.useCallback((ticket: Ticket) => {
    onOpenTicket(ticket);
  }, [onOpenTicket]);

  // Memoize onCreateTicket handler
  const handleCreateTicket = React.useCallback(() => {
    onCreateTicket?.(status);
  }, [onCreateTicket, status]);

  return (
    <div className="flex-shrink-0 w-72 h-full">
      <AppCard className={`h-full flex flex-col ${isOver ? 'ring-2 ring-primary' : ''}`}>
        <AppCardHeader className="pb-3 flex-shrink-0 sticky top-0 bg-card z-10 border-b">
          <AppCardTitle className="flex items-center justify-between text-sm">
            <span className="capitalize">{status.replace("_", " ")}</span>
            <div className="flex items-center space-x-2">
              <AppBadge variant="secondary" className="text-xs">
                {statusTickets.length}
              </AppBadge>
              <button 
                className="text-muted-foreground hover:text-foreground p-1 rounded hover:bg-muted transition-colors"
                onClick={handleCreateTicket}
                title="Create new ticket"
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>
          </AppCardTitle>
        </AppCardHeader>
        <AppCardContent
          ref={setNodeRef}
          className="flex-1 overflow-y-auto space-y-2 p-3 min-h-0"
        >
          <SortableContext items={ticketIds} strategy={verticalListSortingStrategy}>
            {statusTickets.map((ticket) => (
              <DraggableTicket key={ticket.id} ticket={ticket} onOpenTicket={handleOpenTicket} />
            ))}
          </SortableContext>
          {statusTickets.length === 0 && (
            <div className="text-center text-sm text-muted-foreground py-6 border-2 border-dashed border-muted rounded-md">
              <div className="text-muted-foreground/50 mb-2">
                <Plus className="h-5 w-5 mx-auto" />
              </div>
              <p className="text-xs">Drop tickets here</p>
            </div>
          )}
        </AppCardContent>
      </AppCard>
    </div>
  );
});