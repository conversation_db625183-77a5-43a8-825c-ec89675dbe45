import * as React from "react";
import { MoreHorizontal } from "lucide-react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { AppBadge } from "@/components/ui-toolkit";
import { type Ticket } from "@/lib/demo/types";

interface DraggableTicketProps {
  ticket: Ticket;
  onOpenTicket: (ticket: Ticket) => void;
}

export const DraggableTicket: React.FC<DraggableTicketProps> = React.memo(({ ticket, onOpenTicket }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: ticket.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // Memoize the double click handler
  const handleDoubleClick = React.useCallback(() => {
    onOpenTicket(ticket);
  }, [onOpenTicket, ticket]);

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`p-2.5 rounded-md border bg-card hover:shadow-sm transition-all cursor-pointer group ${
        isDragging ? 'opacity-50 shadow-md' : ''
      }`}
      onDoubleClick={handleDoubleClick}
    >
      <div className="space-y-2">
        <div className="flex items-start justify-between">
          <span className="text-xs font-medium text-muted-foreground bg-muted px-1.5 py-0.5 rounded text-[10px]">
            {ticket.number}
          </span>
          <div className="flex items-center space-x-1">
            <AppBadge priority={ticket.priority} className="text-xs px-1.5 py-0.5">
              {ticket.priority}
            </AppBadge>
            <button className="opacity-0 group-hover:opacity-100 text-muted-foreground hover:text-foreground p-0.5 rounded hover:bg-muted transition-opacity">
              <MoreHorizontal className="h-3 w-3" />
            </button>
          </div>
        </div>
        <p className="text-sm font-medium leading-tight line-clamp-2">
          {ticket.subject}
        </p>
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-1.5">
            {ticket.assignee ? (
              <div className="flex items-center space-x-1">
                <div className="w-4 h-4 bg-primary/10 rounded-full flex items-center justify-center text-primary text-xs font-medium">
                  {ticket.assignee.name.charAt(0).toUpperCase()}
                </div>
                <span className="truncate max-w-16 text-[10px]">{ticket.assignee.name}</span>
              </div>
            ) : (
              <span className="text-muted-foreground text-[10px]">Unassigned</span>
            )}
          </div>
          {ticket.updatedAt && (
            <span className="text-muted-foreground text-[10px]">
              {new Date(ticket.updatedAt).toLocaleDateString()}
            </span>
          )}
        </div>
      </div>
    </div>
  );
});