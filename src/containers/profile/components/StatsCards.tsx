"use client";

import { Ticket, Clock, Star } from "lucide-react";
import { AppCard, AppCardContent } from "@/components/ui-toolkit";
import { UserProfile } from '../types';

interface StatsCardsProps {
  profile: UserProfile;
  tickets: any[];
}

export function StatsCards({ profile, tickets }: StatsCardsProps) {
  const totalTickets = tickets.length;
  const resolvedTickets = tickets.filter(t => t.status === 'resolved' || t.status === 'closed').length;

  return (
    <div className="grid gap-4 md:grid-cols-3">
      <AppCard>
        <AppCardContent className="flex flex-col items-center justify-center p-6">
          <Ticket className="h-8 w-8 text-primary mb-2" />
          <div className="text-2xl font-bold">{totalTickets}</div>
          <p className="text-xs text-muted-foreground text-center">Total Tickets</p>
        </AppCardContent>
      </AppCard>

      <AppCard>
        <AppCardContent className="flex flex-col items-center justify-center p-6">
          <Ticket className="h-8 w-8 text-green-500 mb-2" />
          <div className="text-2xl font-bold">{resolvedTickets}</div>
          <p className="text-xs text-muted-foreground text-center">Resolved</p>
        </AppCardContent>
      </AppCard>

      <AppCard>
        <AppCardContent className="flex flex-col items-center justify-center p-6">
          <Clock className="h-8 w-8 text-blue-500 mb-2" />
          <div className="text-2xl font-bold">N/A</div>
          <p className="text-xs text-muted-foreground text-center">Avg Response</p>
        </AppCardContent>
      </AppCard>

      {/* <AppCard>
        <AppCardContent className="flex flex-col items-center justify-center p-6">
          <Star className="h-8 w-8 text-yellow-500 mb-2" />
          <div className="text-2xl font-bold">N/A</div>
          <p className="text-xs text-muted-foreground text-center">Rating</p>
        </AppCardContent>
      </AppCard> */}
    </div>
  );
}