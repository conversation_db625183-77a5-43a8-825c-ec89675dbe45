"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui-toolkit";
import { Ticket } from '../types';
import { useAuth } from '@/hooks/useAuth';
import { useGetStatusesQuery } from '@/redux/slices/statuses';

interface TicketsSectionProps {
  tickets: Ticket[];
}

export function TicketsSection({ tickets }: TicketsSectionProps) {
  const { tenant } = useAuth();
  const { data: statuses = [] } = useGetStatusesQuery(tenant?.tenantId || '', { skip: !tenant?.tenantId });

  const getStatusColor = (status: string): "secondary" | "destructive" | "default" | "outline" | "accent" => {
    const found = statuses.find(s => s.name === status);
    return (found?.color as "secondary" | "destructive" | "default" | "outline" | "accent") || 'secondary';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  const renderTicketList = (filteredTickets: Ticket[]) => {
    if (filteredTickets.length === 0) {
      return <div className="text-left py-0 px-2 text-muted-foreground">No tickets found</div>;
    }

    return (
      <div className="space-y-4">
        {filteredTickets.map((ticket) => (
          <div key={ticket._id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer">
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <span className="font-medium">{ticket.ticketKey}</span>
                <AppBadge variant={getStatusColor(ticket.status)}>
                  {ticket.status}
                </AppBadge>
                <AppBadge variant={getPriorityColor(ticket.priority)}>
                  {ticket.priority}
                </AppBadge>
              </div>
              <h4 className="font-medium">{ticket.title}</h4>
              <p className="text-sm text-muted-foreground">{new Date(ticket.createdAt).toLocaleDateString()}</p>
            </div>
            <AppButton
              variant="ghost"
              size="sm"
              onClick={() => window.location.href = `/tickets?ticket=${ticket.ticketKey}`}
            >
              View
            </AppButton>
          </div>
        ))}
      </div>
    );
  };

  return (
    <AppCard>
      <AppCardHeader>
        <AppCardTitle>Tickets</AppCardTitle>
      </AppCardHeader>
      <AppCardContent>
        <Tabs defaultValue="all" className="w-full">
          <TabsList>
            <TabsTrigger value="all">All Tickets</TabsTrigger>
            {statuses.map(status => (
              <TabsTrigger key={status._id} value={status.name}>
                {status.name}
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value="all" className="space-y-4 mt-4">
            {renderTicketList(tickets)}
          </TabsContent>

          {statuses.map(status => (
            <TabsContent key={status._id} value={status.name} className="space-y-4 mt-4">
              {renderTicketList(tickets.filter(t => t.status === status.name))}
            </TabsContent>
          ))}
        </Tabs>
      </AppCardContent>
    </AppCard>
  );
}