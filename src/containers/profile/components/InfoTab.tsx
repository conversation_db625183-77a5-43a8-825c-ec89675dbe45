"use client";

import { ProfileInfoCard } from './ProfileInfoCard';
import { StatsCards } from './StatsCards';
import { TicketsSection } from './TicketsSection';

interface InfoTabProps {
  profile: any;
  tickets: any[];
  onSaveProfile?: (updatedProfile: any) => void;
  onResetPassword?: () => void;
  onEditUserDetails?: () => void;
}

export function InfoTab({ profile, tickets, onSaveProfile, onResetPassword, onEditUserDetails }: InfoTabProps) {
  return (
    <div className="grid gap-6 md:grid-cols-3">
      {/* Profile Info Card */}
      <div className="md:col-span-1">
        <ProfileInfoCard profile={profile} onSaveProfile={onSaveProfile} onResetPassword={onResetPassword} onEditUserDetails={onEditUserDetails} />
      </div>

      {/* Stats and Details */}
      <div className="md:col-span-2 space-y-6">
        {/* Stats Cards */}
        <StatsCards profile={profile} tickets={tickets} />

        {/* Tickets Tab */}
        <TicketsSection tickets={tickets} />
      </div>
    </div>
  );
}