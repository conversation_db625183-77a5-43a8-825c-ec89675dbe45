"use client";

import React, { useState } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useGetUserTrackingQuery } from '@/services/api/users';

interface TrackingReportTabProps {
  userId: string;
}

export function TrackingReportTab({ userId }: TrackingReportTabProps) {
  // Configuration: 1 = Monday, 0 = Sunday, 5 = Friday, etc.
  const WEEK_START_DAY = 1; // Monday

  // Calculate current week dates
  const getCurrentWeekDates = () => {
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday

    // Calculate days to subtract to get to week start day
    const daysToSubtract = (dayOfWeek - WEEK_START_DAY + 7) % 7;

    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - daysToSubtract);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6); // Always 7 days (Monday to Sunday)

    return {
      fromDate: startOfWeek.toISOString().split('T')[0],
      toDate: endOfWeek.toISOString().split('T')[0],
      selectedFromDate: startOfWeek,
      selectedToDate: endOfWeek
    };
  };

  const currentWeek = getCurrentWeekDates();

  const [fromDate, setFromDate] = useState<Date | null>(currentWeek.selectedFromDate);
  const [toDate, setToDate] = useState<Date | null>(null);

  // View states
  const [showHeatmap, setShowHeatmap] = useState(false);
  const [viewMode, setViewMode] = useState<'compact' | 'detailed'>('compact');

  // Calculate date range based on selection
  const getDateRangeForDisplay = () => {
    if (!fromDate) return { start: currentWeek.fromDate, end: currentWeek.toDate };

    if (!toDate) {
      // Only from date selected - show that week
      const weekStart = new Date(fromDate);
      const dayOfWeek = weekStart.getDay();
      const daysToSubtract = (dayOfWeek - WEEK_START_DAY + 7) % 7;

      const startOfWeek = new Date(weekStart);
      startOfWeek.setDate(weekStart.getDate() - daysToSubtract);

      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);

      return {
        start: startOfWeek.toISOString().split('T')[0],
        end: endOfWeek.toISOString().split('T')[0]
      };
    } else {
      // Both dates selected - show the range
      return {
        start: fromDate.toISOString().split('T')[0],
        end: toDate.toISOString().split('T')[0]
      };
    }
  };

  const displayRange = getDateRangeForDisplay();

  // API call with date parameters
  const { data: trackingData, isLoading, error } = useGetUserTrackingQuery({
    userId,
    startDate: displayRange.start,
    endDate: displayRange.end
  });

  // Transform API data to match expected format
  const timeEntries = React.useMemo(() => {
    if (!trackingData?.data) return [];

    const { worklogs, tickets } = trackingData.data;
    
    // Group worklogs by ticket
    const worklogsByTicket: { [key: string]: any[] } = {};
    worklogs.forEach((worklog: any) => {
      const ticketKey = worklog.ticketId.ticketKey;
      if (!worklogsByTicket[ticketKey]) {
        worklogsByTicket[ticketKey] = [];
      }
      worklogsByTicket[ticketKey].push(worklog);
    });

    // Transform to expected format
    return Object.entries(worklogsByTicket).map(([ticketKey, ticketWorklogs]) => {
      const ticket = tickets.find((t: any) => t.ticketKey === ticketKey);
      if (!ticket) return null;

      // Group worklogs by date
      const times: { [key: string]: string } = {};
      let totalMinutes = 0;

      ticketWorklogs.forEach(worklog => {
        const date = new Date(worklog.createdAt).toISOString().split('T')[0];
        if (!times[date]) {
          times[date] = '0h 0m';
        }
        
        // Add minutes to existing time for this date
        const existingMatch = times[date].match(/(\d+)h\s*(\d+)m/);
        if (existingMatch) {
          const existingHours = parseInt(existingMatch[1]);
          const existingMins = parseInt(existingMatch[2]);
          const totalMins = existingHours * 60 + existingMins + worklog.minutes;
          const newHours = Math.floor(totalMins / 60);
          const newMins = totalMins % 60;
          times[date] = `${newHours}h ${newMins}m`;
        }
        
        totalMinutes += worklog.minutes;
      });

      // Calculate total time
      const totalHours = Math.floor(totalMinutes / 60);
      const totalMins = totalMinutes % 60;
      const total = totalHours > 0 ? `${totalHours}h ${totalMins}m` : `${totalMins}m`;

      return {
        ticket: ticket.ticketKey,
        title: ticket.title,
        times,
        total
      };
    }).filter(Boolean) as Array<{
      ticket: string;
      title: string;
      times: { [key: string]: string };
      total: string;
    }>;
  }, [trackingData]);

  // Generate date range
  const getDateRange = (start: string, end: string) => {
    const dates = [];
    const startDate = new Date(start);
    const endDate = new Date(end);
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      dates.push(currentDate.toISOString().split('T')[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }
    return dates;
  };

  const dateRange = getDateRange(displayRange.start, displayRange.end);

  // View control handlers
  const toggleHeatmap = () => {
    setShowHeatmap(!showHeatmap);
  };

  const setCompactView = () => {
    setViewMode('compact');
  };

  const setDetailedView = () => {
    setViewMode('detailed');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-600">Loading tracking data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-red-600">Failed to load tracking data</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-semibold text-gray-800">RETAIL TIMESHEETS AND WORKLOGS</h3>
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700">From:</label>
            <DatePicker
              selected={fromDate}
              onChange={setFromDate}
              dateFormat="MMM dd, yyyy"
              className="px-3 py-2 border rounded text-sm w-32"
              placeholderText="Start date"
            />
            <label className="text-sm font-medium text-gray-700">To:</label>
            <DatePicker
              selected={toDate}
              onChange={setToDate}
              dateFormat="MMM dd, yyyy"
              className="px-3 py-2 border rounded text-sm w-32"
              placeholderText="End date"
              isClearable
            />
          </div>
        </div>
      </div>

      {/* Time Tracking Table - Calendar View */}
      <div className="overflow-x-auto bg-white rounded-lg border">
        <table className="w-full">
          <thead>
            <tr className="bg-gray-50">
              <th className="px-3 py-2 text-left text-sm font-medium text-gray-700 w-20"></th>
              {dateRange.map(date => (
                <th key={date} className="px-2 py-2 text-center text-xs font-medium text-gray-700 min-w-[120px] border-l border-gray-200">
                  <div className="flex flex-col">
                    <span className="text-gray-900 font-semibold">
                      {new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}
                    </span>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="px-3 py-4 bg-gray-50 border-r border-gray-200"></td>
              {dateRange.map(date => {
                const dayEntries = timeEntries.filter(entry => (entry.times as any)[date]);
                
                // Calculate total time for the day
                const totalDayTime = dayEntries.reduce((total, entry) => {
                  const timeStr = (entry.times as any)[date];
                  if (!timeStr) return total;
                  
                  const match = timeStr.match(/(\d+)h\s*(\d+)?m?/);
                  if (match) {
                    const hours = parseInt(match[1]) || 0;
                    const minutes = parseInt(match[2]) || 0;
                    return {
                      hours: total.hours + hours,
                      minutes: total.minutes + minutes
                    };
                  }
                  return total;
                }, { hours: 0, minutes: 0 });
                
                // Convert to display format
                const displayTime = totalDayTime.hours > 0 || totalDayTime.minutes > 0 
                  ? `${totalDayTime.hours > 0 ? totalDayTime.hours + 'h' : ''} ${totalDayTime.minutes > 0 ? totalDayTime.minutes + 'm' : ''}`.trim()
                  : '';
                
                return (
                  <td 
                    key={date} 
                    className="p-0 border-l border-gray-200 align-top" 
                    style={{ backgroundColor: showHeatmap && dayEntries.length > 0 ? '#93C5FD' : showHeatmap ? '#E5E7EB' : 'transparent' }}
                  >
                    <div className={`min-h-[${viewMode === 'compact' ? '120px' : '200px'}] p-2 flex items-center justify-center`}>
                      {viewMode === 'compact' ? (
                        // Compact view: Show total daily time
                        displayTime && (
                          <div className="text-center">
                            <div className="text-lg font-bold text-blue-800">{displayTime}</div>
                            <div className="text-xs text-gray-600">{dayEntries.length} ticket{dayEntries.length !== 1 ? 's' : ''}</div>
                          </div>
                        )
                      ) : (
                        // Detailed view: Show individual ticket entries in columns
                        <div className="space-y-1">
                          {dayEntries.map((entry, index) => {
                            const timeLogged = (entry.times as any)[date];
                            return (
                              <div key={index} className="flex items-center justify-between p-1 bg-blue-50 rounded text-xs border">
                                <div className="flex-1 min-w-0">
                                  <div className="text-blue-600 text-xs ">{entry.title}</div>
                                  <div className="font-semibold text-blue-800 " style={{
                                    fontSize:8
                                  }}>{entry.ticket}</div>
                                </div>
                                <div className="font-medium text-blue-700 ml-2 flex-shrink-0">{timeLogged}</div>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  </td>
                );
              })}
            </tr>
          </tbody>
        </table>
      </div>

      {/* View Controls */}
      <div className="flex items-center justify-end gap-2">
        <button 
          onClick={toggleHeatmap}
          className={`px-3 py-2 rounded text-sm hover:bg-opacity-80 ${
            showHeatmap 
              ? 'bg-blue-600 text-white hover:bg-blue-700' 
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          {showHeatmap ? 'Hide Activity Colors' : 'Show Activity Colors'}
        </button>
        <button 
          onClick={setCompactView}
          className={`px-3 py-2 rounded text-sm hover:bg-opacity-80 ${
            viewMode === 'compact' 
              ? 'bg-blue-600 text-white hover:bg-blue-700' 
              : 'border hover:bg-gray-50'
          }`}
        >
          Compact View
        </button>
        <button 
          onClick={setDetailedView}
          className={`px-3 py-2 rounded text-sm hover:bg-opacity-80 ${
            viewMode === 'detailed' 
              ? 'bg-blue-600 text-white hover:bg-blue-700' 
              : 'border hover:bg-gray-50'
          }`}
        >
          Detailed View
        </button>
      </div>

      {/* Summary Statistics */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-sm font-semibold text-gray-800 mb-2">
          {viewMode === 'compact' ? 'Daily Time Summary' : 'Detailed Statistics'}
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="bg-white p-3 rounded border">
            <div className="text-gray-600">Total Tickets</div>
            <div className="text-lg font-semibold text-blue-600">{timeEntries.length}</div>
          </div>
          <div className="bg-white p-3 rounded border">
            <div className="text-gray-600">Active Days</div>
            <div className="text-lg font-semibold text-green-600">
              {dateRange.filter(date => 
                timeEntries.some(entry => (entry.times as any)[date])
              ).length}
            </div>
          </div>
          <div className="bg-white p-3 rounded border">
            <div className="text-gray-600">Total Hours</div>
            <div className="text-lg font-semibold text-purple-600">
              {timeEntries.reduce((total, entry) => {
                const hours = Object.values(entry.times).reduce((sum: number, time: any) => {
                  const match = time.match(/(\d+)h\s*(\d+)?m?/);
                  if (match) {
                    const hours = parseInt(match[1]) || 0;
                    const minutes = parseInt(match[2]) || 0;
                    return sum + hours + minutes / 60;
                  }
                  return sum;
                }, 0);
                return total + hours;
              }, 0).toFixed(1)}h
            </div>
          </div>
          <div className="bg-white p-3 rounded border">
  <div className="text-gray-600">Avg per Day</div>
  <div className="text-lg font-semibold text-orange-600">
    {(
      timeEntries.reduce((total, entry) => {
        const hours = Object.values(entry.times).reduce((sum, time) => {
          const match = time.match(/(\d+)h\s*(\d+)?m?/);
          if (match) {
            const hrs = parseInt(match[1]) || 0;
            const mins = parseInt(match[2]) || 0;
            return sum + hrs + mins / 60;
          }
          return sum;
        }, 0);
        return total + hours;
      }, 0) / dateRange.length
    ).toFixed(1)}h
  </div>
</div>

        </div>
        {viewMode === 'detailed' && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white p-3 rounded border">
              <div className="text-gray-600 mb-2">Most Active Day</div>
              <div className="text-sm">
                {(() => {
                  const dayActivity = dateRange.map(date => ({
                    date,
                    count: timeEntries.filter(entry => (entry.times as any)[date]).length
                  })).sort((a, b) => b.count - a.count);
                  const mostActive = dayActivity[0];
                  return mostActive?.count > 0 
                    ? `${new Date(mostActive.date).toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })} (${mostActive.count} entries)`
                    : 'No activity found';
                })()}
              </div>
            </div>
            <div className="bg-white p-3 rounded border">
              <div className="text-gray-600 mb-2">Top Contributor</div>
              <div className="text-sm">
                {(() => {
                  const contributorStats = timeEntries.map(entry => ({
                    ticket: entry.ticket,
                    title: entry.title,
                    hours: Object.values(entry.times).reduce((sum: number, time: any) => {
                      const match = time.match(/(\d+)h\s*(\d+)?m?/);
                      if (match) {
                        const hours = parseInt(match[1]) || 0;
                        const minutes = parseInt(match[2]) || 0;
                        return sum + hours + minutes / 60;
                      }
                      return sum;
                    }, 0)
                  })).sort((a, b) => b.hours - a.hours);
                  const topContributor = contributorStats[0];
                  return topContributor 
                    ? `${topContributor.ticket}: ${topContributor.hours.toFixed(1)}h`
                    : 'No data available';
                })()}
              </div>
            </div>
          </div>
        )}
        {viewMode === 'compact' && (
          <div className="mt-4">
            <div className="bg-white p-3 rounded border">
              <div className="text-gray-600 mb-2">Daily Totals Overview</div>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-7 gap-2 text-xs">
                {dateRange.map(date => {
                  const dayEntries = timeEntries.filter(entry => (entry.times as any)[date]);
                  const totalDayTime = dayEntries.reduce((total, entry) => {
                    const timeStr = (entry.times as any)[date];
                    if (!timeStr) return total;
                    
                    const match = timeStr.match(/(\d+)h\s*(\d+)?m?/);
                    if (match) {
                      const hours = parseInt(match[1]) || 0;
                      const minutes = parseInt(match[2]) || 0;
                      return {
                        hours: total.hours + hours,
                        minutes: total.minutes + minutes
                      };
                    }
                    return total;
                  }, { hours: 0, minutes: 0 });
                  
                  const displayTime = totalDayTime.hours > 0 || totalDayTime.minutes > 0 
                    ? `${totalDayTime.hours > 0 ? totalDayTime.hours + 'h' : ''} ${totalDayTime.minutes > 0 ? totalDayTime.minutes + 'm' : ''}`.trim()
                    : '0h';
                  
                  return (
                    <div key={date} className="text-center p-2 bg-gray-50 rounded">
                      <div className="font-medium text-gray-800">
                        {new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                      </div>
                      <div className="text-blue-600 font-semibold">{displayTime}</div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}