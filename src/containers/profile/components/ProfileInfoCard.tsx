"use client";

import { useState } from "react";
import { User, Mail, Phone, Calendar, MapPin, Edit, Key } from "lucide-react";
import {
  AppButton,
  AppCard,
  AppCardContent,
  AppCardHeader,
  AppCardTitle,
  AppInput,
  AppTextarea,
  Label,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui-toolkit";
import { UserProfile } from '../types';

interface ProfileInfoCardProps {
  profile: UserProfile;
  onSaveProfile?: (updatedProfile: Partial<UserProfile>) => void;
  onResetPassword?: () => void;
  onEditUserDetails?: () => void;
}

export function ProfileInfoCard({ profile, onSaveProfile, onResetPassword, onEditUserDetails }: ProfileInfoCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    name: profile.name,
    phone: profile.phone || '',
    timezone: profile.timezone || '',
    language: profile.language || '',
    avatar: null as File | null
  });

  const handleSaveProfile = () => {
    if (!onSaveProfile) return;
    const updatedData: Partial<UserProfile> = {
      name: editForm.name,
      phone: editForm.phone,
      timezone: editForm.timezone,
      language: editForm.language,
    };
    if (editForm.avatar) {
      // For now, skip avatar or handle differently
    }
    onSaveProfile(updatedData);
    setIsEditing(false);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setEditForm(prev => ({ ...prev, avatar: file }));
    }
  };

  return (
    <AppCard>
      <AppCardHeader className="text-center">
        <Avatar className="h-24 w-24 mx-auto mb-4">
          <AvatarImage src={profile.avatar} />
          <AvatarFallback className="text-lg">
            {profile.name.split(' ').map(n => n[0]).join('')}
          </AvatarFallback>
        </Avatar>
        <AppCardTitle>{profile.name}</AppCardTitle>
        <p className="text-muted-foreground">{typeof profile.role === 'object' ? profile.role.name : profile.role}</p>
      </AppCardHeader>
      <AppCardContent className="space-y-4">
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Mail className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{profile.email}</span>
          </div>
          {profile.phone && (
            <div className="flex items-center space-x-2">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{profile.phone}</span>
            </div>
          )}
          {profile.department && (
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{profile.department.name}</span>
            </div>
          )}
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">Joined {new Date(profile.createdAt).toLocaleDateString()}</span>
          </div>
        </div>

        <div className="flex space-x-2">
          {onSaveProfile && (
            <Dialog open={isEditing} onOpenChange={setIsEditing}>
              <DialogTrigger asChild>
                <AppButton variant="outline" className="flex-1">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Profile
                </AppButton>
              </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Profile</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Name</Label>
                  <AppInput
                    id="name"
                    value={editForm.name}
                    onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <AppInput
                    id="phone"
                    value={editForm.phone}
                    onChange={(e) => setEditForm(prev => ({ ...prev, phone: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="timezone">Timezone</Label>
                  <AppInput
                    id="timezone"
                    value={editForm.timezone}
                    onChange={(e) => setEditForm(prev => ({ ...prev, timezone: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="language">Language</Label>
                  <AppInput
                    id="language"
                    value={editForm.language}
                    onChange={(e) => setEditForm(prev => ({ ...prev, language: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="avatar">Profile Picture</Label>
                  <input
                    id="avatar"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                </div>
                <div className="flex space-x-2">
                  <AppButton onClick={handleSaveProfile} className="flex-1">
                    Save Changes
                  </AppButton>
                  <AppButton
                    variant="outline"
                    onClick={() => setIsEditing(false)}
                    className="flex-1"
                  >
                    Cancel
                  </AppButton>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          )}
          {onResetPassword && (
            <AppButton variant="outline" className="flex-1" onClick={onResetPassword}>
              <Key className="h-4 w-4 mr-2" />
              Reset Password
            </AppButton>
          )}
          {onEditUserDetails && (
            <AppButton variant="outline" className="flex-1" onClick={onEditUserDetails}>
              <Edit className="h-4 w-4 mr-2" />
              Edit User Details
            </AppButton>
          )}
        </div>
      </AppCardContent>
    </AppCard>
  );
}