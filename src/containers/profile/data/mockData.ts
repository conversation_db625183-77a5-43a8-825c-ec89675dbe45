import { UserProfile, Ticket } from '../types';

export const mockProfile: UserProfile = {
  _id: "1",
  tenantId: "tenant1",
  name: "<PERSON>",
  email: "<EMAIL>",
  emails: ["<EMAIL>"],
  phone: "+****************",
  role: "Senior Support Specialist",
  roles: [{
    _id: "role1",
    name: "Support Specialist",
    userType: "user",
    permissions: ["read", "write"]
  }],
  department: {
    _id: "dept1",
    name: "IT Support",
    description: "Information Technology Support Department"
  },
  permissions: ["read", "write"],
  status: "active",
  avatar: "/placeholder-avatar.jpg",
  timezone: "America/New_York",
  language: "en",
  lastLoginAt: "2023-09-29T10:00:00Z",
  createdAt: "2023-01-15T00:00:00Z",
  updatedAt: "2023-09-29T10:00:00Z",
  createdBy: {
    _id: "admin1",
    name: "Admin User",
    email: "<EMAIL>"
  }
};

export const mockTickets: Ticket[] = [
  {
    _id: "TKT-001",
    tenantId: "tenant1",
    ticketKey: "TKT-001",
    title: "Cannot access email account",
    description: "User cannot access their email account",
    priority: "high",
    status: "in-progress",
    requester: {
      userId: "user1",
      name: "Jane Smith",
      email: "<EMAIL>"
    },
    assigneeId: "1",
    watchers: [],
    tags: ["email", "access"],
    slaPaused: false,
    openedAt: "2023-09-29T08:00:00Z",
    subTaskCount: 0,
    ticketChecklistProgress: {
      done: 0,
      total: 0
    },
    commentCount: 2,
    worklogMinutes: 30,
    createdBy: "user1",
    lastActionAt: "2023-09-29T10:00:00Z",
    createdAt: "2023-09-29T08:00:00Z",
    updatedAt: "2023-09-29T10:00:00Z",
    __v: 0
  },
  {
    _id: "TKT-002",
    tenantId: "tenant1",
    ticketKey: "TKT-002",
    title: "Laptop screen flickering",
    description: "Laptop screen is flickering intermittently",
    priority: "medium",
    status: "resolved",
    requester: {
      userId: "user2",
      name: "Bob Johnson",
      email: "<EMAIL>"
    },
    assigneeId: "1",
    watchers: [],
    tags: ["hardware", "laptop"],
    slaPaused: false,
    openedAt: "2023-09-28T08:00:00Z",
    subTaskCount: 0,
    ticketChecklistProgress: {
      done: 0,
      total: 0
    },
    commentCount: 1,
    worklogMinutes: 45,
    createdBy: "user2",
    lastActionAt: "2023-09-28T12:00:00Z",
    createdAt: "2023-09-28T08:00:00Z",
    updatedAt: "2023-09-28T12:00:00Z",
    __v: 0
  },
  {
    _id: "TKT-003",
    tenantId: "tenant1",
    ticketKey: "TKT-003",
    title: "Software installation request",
    description: "Request to install new software",
    priority: "low",
    status: "open",
    requester: {
      userId: "user3",
      name: "Alice Brown",
      email: "<EMAIL>"
    },
    assigneeId: "1",
    watchers: [],
    tags: ["software", "installation"],
    slaPaused: false,
    openedAt: "2023-09-26T08:00:00Z",
    subTaskCount: 0,
    ticketChecklistProgress: {
      done: 0,
      total: 0
    },
    commentCount: 0,
    worklogMinutes: 0,
    createdBy: "user3",
    lastActionAt: "2023-09-26T08:00:00Z",
    createdAt: "2023-09-26T08:00:00Z",
    updatedAt: "2023-09-26T08:00:00Z",
    __v: 0
  }
];