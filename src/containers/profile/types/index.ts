export interface UserProfile {
  _id: string;
  tenantId: string;
  name: string;
  email: string;
  emails: string[];
  phone?: string;
  role: string | {
    _id: string;
    name: string;
    permissions: string[];
  };
  roles: Array<{
    _id: string;
    name: string;
    userType: string;
    permissions: string[];
  }>;
  department?: {
    _id: string;
    name: string;
    description: string;
  };
  permissions: string[];
  status: string;
  avatar?: string;
  timezone?: string;
  language?: string;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: {
    _id: string;
    name: string;
    email: string;
  };
}

export interface Ticket {
  _id: string;
  tenantId: string;
  ticketKey: string;
  title: string;
  description: string;
  priority: string;
  status: string;
  requester?: {
    userId: string;
    name: string;
    email: string;
  };
  assigneeId?: string;
  watchers: any[];
  tags: string[];
  slaPaused: boolean;
  respondBy?: string;
  resolveBy?: string;
  slaPolicyId?: string;
  openedAt: string;
  subTaskCount: number;
  ticketChecklistProgress: {
    done: number;
    total: number;
  };
  commentCount: number;
  worklogMinutes: number;
  estimatedTime?: number;
  createdBy: string;
  lastActionAt: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
  mergedTickets?: any[];
  attachments?: any[];
}