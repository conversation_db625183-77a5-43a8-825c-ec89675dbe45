"use client";

import { SectionHeader } from "@/components/ui-toolkit";
import { InfoTab } from './components/InfoTab';
import { TrackingReportTab } from './components/TrackingReportTab';
import { useGetProfileQuery, useUpdateProfileMutation } from '@/services/api/users';
import { useGetTicketsByUserQuery } from '@/services/api/tickets';
import { useAuth } from '@/hooks/useAuth';
import { skipToken } from '@reduxjs/toolkit/query/react';
import { toast } from "sonner";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui-toolkit";

export default function ProfileContainer() {
  const { user } = useAuth();
  const userId = user?._id || '';

  const { data: profileData, isLoading: profileLoading } = useGetProfileQuery({});
  const { data: ticketsData, isLoading: ticketsLoading } = useGetTicketsByUserQuery(
    userId ? { userId } : skipToken
  );

  const [updateProfile] = useUpdateProfileMutation();

  const handleSaveProfile = async (updatedProfile: any) => {
    try {
      await updateProfile(updatedProfile);
      toast.success("Profile updated successfully!");
      
    } catch (error) {
      console.error('Failed to update profile:', error);
    }
  };

  if (profileLoading || (userId && ticketsLoading)) {
    return <div>Loading...</div>;
  }

  const profile = profileData?.data?.user;
  const tickets = ticketsData?.data || [];

  if (!profile) {
    return <div>Failed to load profile</div>;
  }

  return (
    <div>
      <SectionHeader title="Profile" description="Manage your profile information and view your activity" />

      <Tabs defaultValue="info" className="w-full">
        <TabsList>
          <TabsTrigger value="info">Info</TabsTrigger>
          <TabsTrigger value="tracking-report">Tracking Report</TabsTrigger>
        </TabsList>

        <TabsContent value="info">
          <InfoTab profile={profile} tickets={tickets} onSaveProfile={handleSaveProfile} />
        </TabsContent>

        <TabsContent value="tracking-report" >
          <TrackingReportTab userId={userId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}