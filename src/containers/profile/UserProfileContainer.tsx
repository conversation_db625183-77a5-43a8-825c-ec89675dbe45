"use client";

import { useState } from 'react';
import { SectionHeader } from "@/components/ui-toolkit";
import { InfoTab } from './components/InfoTab';
import { TrackingReportTab } from './components/TrackingReportTab';
import { useGetProfileQuery, useUpdateProfileMutation } from '@/services/api/users';
import { useGetTicketsByUserQuery } from '@/services/api/tickets';
import { skipToken } from '@reduxjs/toolkit/query/react';
import { toast } from "sonner";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui-toolkit";
import { useResetPasswordMutation } from '@/redux/slices/users/userSlice';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, AppButton, AppInput, Label } from "@/components/ui-toolkit";

interface UserProfileContainerProps {
  userId: string;
}

export default function UserProfileContainer({ userId }: UserProfileContainerProps) {
  const [resetPasswordDialogOpen, setResetPasswordDialogOpen] = useState(false);
  const [resetPasswordResult, setResetPasswordResult] = useState<{ newPassword: string; message: string } | null>(null);

  const { data: profileData, isLoading: profileLoading } = useGetProfileQuery({ userId });
  const { data: ticketsData, isLoading: ticketsLoading } = useGetTicketsByUserQuery(
    userId ? { userId } : skipToken
  );

  const [updateProfile] = useUpdateProfileMutation();
  const [resetPassword, { isLoading: resetting }] = useResetPasswordMutation();

  const handleSaveProfile = async (updatedProfile: any) => {
    try {
      await updateProfile({ ...updatedProfile, userId });
      toast.success("Profile updated successfully!");
    } catch (error) {
      console.error('Failed to update profile:', error);
      toast.error("Failed to update profile");
    }
  };

  const handleResetPassword = async () => {
    if (!profile?.tenantId) return;
    try {
      const result = await resetPassword({ id: userId, tenantId: profile.tenantId });
      setResetPasswordResult(result?.data?.data || null);
    } catch (error) {
      console.error('Failed to reset password:', error);
      toast.error("Failed to reset password");
    }
  };

  const openResetPasswordDialog = () => {
    setResetPasswordDialogOpen(true);
    setResetPasswordResult(null);
  };

  const handleEditUserDetails = () => {
    // TODO: Implement edit user details dialog
    console.log('Edit user details');
  };

  if (profileLoading || ticketsLoading) {
    return <div>Loading...</div>;
  }

  const profile = profileData?.data?.user;
  const tickets = ticketsData?.data || [];

  if (!profile) {
    return <div>Failed to load profile</div>;
  }

  return (
    <div>
      <SectionHeader title="User Profile" description="View user profile information and activity" />

      <Tabs defaultValue="info" className="w-full">
        <TabsList>
          <TabsTrigger value="info">Info</TabsTrigger>
          <TabsTrigger value="tracking-report">Tracking Report</TabsTrigger>
        </TabsList>

        <TabsContent value="info">
          <InfoTab profile={profile} tickets={tickets} onSaveProfile={handleSaveProfile} onResetPassword={openResetPasswordDialog} />
        </TabsContent>

        <TabsContent value="tracking-report" >
          <TrackingReportTab userId={userId} />
        </TabsContent>
      </Tabs>

      <Dialog open={resetPasswordDialogOpen} onOpenChange={(open) => {
        setResetPasswordDialogOpen(open);
        if (!open) {
          setResetPasswordResult(null);
        }
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset Password</DialogTitle>
            {!resetPasswordResult ? (
              <DialogDescription>
                Are you sure you want to reset the password for {profile?.name}? A new temporary password will be generated and the user will be required to change it on next login.
              </DialogDescription>
            ) : (
              <DialogDescription>
                Password has been reset successfully. Please share the new credentials with the user.
              </DialogDescription>
            )}
          </DialogHeader>
          
          {resetPasswordResult ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label className="text-sm font-medium">Username</Label>
                  <AppInput
                    value={profile?.name}
                    readOnly
                    className="bg-muted"
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium">Email</Label>
                  <AppInput
                    value={profile?.email}
                    readOnly
                    className="bg-muted"
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium">New Password</Label>
                  <AppInput
                    value={resetPasswordResult.newPassword}
                    readOnly
                    className="bg-muted font-mono"
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-2 pt-4">
                <AppButton
                  variant="outline"
                  onClick={() => {
                    setResetPasswordDialogOpen(false);
                    setResetPasswordResult(null);
                  }}
                >
                  Close
                </AppButton>
              </div>
            </div>
          ) : (
            <div className="flex justify-end space-x-2 pt-4">
              <AppButton
                variant="outline"
                onClick={() => setResetPasswordDialogOpen(false)}
              >
                Cancel
              </AppButton>
              <AppButton
                onClick={handleResetPassword}
                disabled={resetting}
              >
                {resetting ? 'Resetting...' : 'Reset Password'}
              </AppButton>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}