"use client";

import * as React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Plus } from "lucide-react";
import {
  AppButton,
  SectionHeader,
} from "@/components/ui-toolkit";
import { dashboardService, ticketService } from "@/lib/demo/api";
import type { Ticket as TicketType } from "@/lib/demo/types";
import { useGetRecentTicketsQuery } from "@/services/api/tickets";
import { useAuth } from "@/hooks/useAuth";
import { useAppDispatch } from "@/redux/store";
import { setUser, setTenant, setSSOLoginData } from "@/redux/slices/auth";
import { useGetStatusesQuery } from "@/redux/slices/statuses";
import { DashboardStats } from "./DashboardStats";
import { DashboardCharts } from "./DashboardCharts";
import { RecentTickets } from "./RecentTickets";

export function DashboardContainer() {
  const { tenant } = useAuth();
  const dispatch = useAppDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [stats, setStats] = React.useState({
    totalTickets: 0,
    openTickets: 0,
    resolvedToday: 0,
    avgResolutionTime: "0 hours",
  });
  const [chartData, setChartData] = React.useState<{
    ticketVolume: { name: string; value: number }[];
    slaCompliance: { name: string; value: number }[];
    agentWorkload: { name: string; value: number }[];
  }>({
    ticketVolume: [],
    slaCompliance: [],
    agentWorkload: [],
  });
  const [statusFilter, setStatusFilter] = React.useState<string>("");
  const [priorityFilter, setPriorityFilter] = React.useState<string>("");
  const [loading, setLoading] = React.useState(true);

  // Use RTK Query for recent tickets
  const { data: recentTicketsData, isLoading: recentTicketsLoading } = useGetRecentTicketsQuery(
    tenant?.tenantId ? {
      tenantId: tenant.tenantId,
      size: 5,
      ...(statusFilter && { status: statusFilter }),
      ...(priorityFilter && { priority: priorityFilter })
    } : { tenantId: '', size: 5 },
    { skip: !tenant?.tenantId }
  );

  // Fetch statuses for global state
  const { data: statuses } = useGetStatusesQuery(tenant?.tenantId || '', { skip: !tenant?.tenantId });

  // Transform API data to component format
  const recentTickets: TicketType[] = React.useMemo(() => {
    if (!recentTicketsData?.data) return [];

    return recentTicketsData.data.map(ticket => ({
      id: ticket._id,
      number: ticket.ticketKey || ticket._id,
      ticketKey: ticket.ticketKey,
      subject: ticket.title,
      status: ticket.status as any,
      priority: ticket.priority as any,
      assignee: ticket.assigneeId ? {
        id: ticket.assigneeId,
        name: 'Assigned User',
        email: '',
        role: { id: '', name: '', type: 'custom', userType: 'agent', permissions: [], isActive: true },
        department: { id: '1', name: 'General', status: 'active', memberCount: 0 },
        status: 'active'
      } : undefined,
      requester: ticket.requester || { name: 'Unknown' },
      department: { id: '1', name: 'General', status: 'active', memberCount: 0 }, // TODO: Get department from API
      category: '',
      tags: ticket.tags,
      createdAt: new Date(ticket.createdAt),
      updatedAt: new Date(ticket.updatedAt),
      description: ticket.description,
    }));
  }, [recentTicketsData]);

  // Handle SSO success redirect
  React.useEffect(() => {
    const ssoSuccess = searchParams.get('sso_success');
    const ssoToken = searchParams.get('sso_token');

    // Only process SSO if user is not already authenticated
    const existingUser = localStorage.getItem('user');
    const existingToken = localStorage.getItem('accessToken');
    
    if (ssoSuccess && ssoToken && !existingUser && !existingToken) {
      console.log('SSO Success - Retrieved SSO token:', ssoToken);
      
      // Call backend API to get SSO session data
      const retrieveSSOSession = async () => {
        try {
          const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/v1';
          const response = await fetch(`${API_URL}/auth/sso-session`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ssoToken })
          });

          const result = await response.json();
          
          if (result.success && result.data) {
            console.log('SSO Session Data Retrieved:', result.data);
            
            const { user, tokens, tenant } = result.data;
            
            // Save to localStorage exactly like normal login
            localStorage.setItem('user', JSON.stringify(user));
            localStorage.setItem('accessToken', tokens.accessToken);
            localStorage.setItem('refreshToken', tokens.refreshToken);
            localStorage.setItem('tenant', JSON.stringify(tenant));
            localStorage.setItem('tenantSlug', tenant.slug);
            localStorage.setItem('tenantName', tenant.name || tenant.companyName);
            
            if (tenant.branding) {
              localStorage.setItem('branding', JSON.stringify(tenant.branding));
            }

            // Update Redux store exactly like normal login
            dispatch(setUser(user));
            dispatch(setTenant(tenant));

            console.log('SSO Success - User and tenant data saved to localStorage and Redux');
            
            // Clean up URL params
            const url = new URL(window.location.href);
            url.searchParams.delete('sso_success');
            url.searchParams.delete('sso_token');
            window.history.replaceState({}, document.title, url.pathname);
            
            // Show success message
            // toast.success(`Welcome back, ${user.firstName || user.email}!`);
            
          } else {
            console.log('SSO Session Error:', result.message);
            // toast.error('SSO authentication failed. Please try logging in again.');
            router.push('/auth/login?error=sso_session_failed');
          }
        } catch (error) {
          console.error('SSO Session Retrieval Error:', error);
          // toast.error('Failed to complete SSO authentication.');
          router.push('/auth/login?error=sso_retrieval_failed');
        }
      };

      retrieveSSOSession();
    } else if (ssoSuccess && (existingUser || existingToken)) {
      // User is already authenticated, just clean up URL params
      console.log('SSO Success - User already authenticated, cleaning up URL');
      const url = new URL(window.location.href);
      url.searchParams.delete('sso_success');
      url.searchParams.delete('sso_token');
      window.history.replaceState({}, document.title, url.pathname);
    }
  }, [searchParams, dispatch, router]);

  React.useEffect(() => {
    const loadDashboardData = async () => {
      try {
        const [statsData, chartsData] = await Promise.all([
          dashboardService.getStats(),
          dashboardService.getChartData(),
        ]);

        setStats(statsData);
        setChartData(chartsData);
      } catch (error) {
        console.error("Failed to load dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  if (loading || recentTicketsLoading) {
    return (
      <div className="space-y-4 p-4">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 p-4">
      <SectionHeader
        title="Dashboard"
        description="Overview of your ticket system performance"

      />

      <DashboardStats stats={stats} />
      {/* <DashboardCharts chartData={chartData} /> */}
      <RecentTickets
        recentTickets={recentTickets}
        statusFilter={statusFilter}
        priorityFilter={priorityFilter}
        onStatusFilterChange={setStatusFilter}
        onPriorityFilterChange={setPriorityFilter}
      />
    </div>
  );
}