"use client";

import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
} from "@/components/ui-toolkit";
import { AppChart } from "@/components/ui-toolkit/app-chart";

interface DashboardChartsProps {
  chartData: {
    ticketVolume: { name: string; value: number }[];
    slaCompliance: { name: string; value: number }[];
    agentWorkload: { name: string; value: number }[];
  };
}

export function DashboardCharts({ chartData }: DashboardChartsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2">
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Ticket Volume</AppCardTitle>
          <AppCardDescription>
            Number of tickets created over the last 6 months
          </AppCardDescription>
        </AppCardHeader>
        <AppCardContent>
          <AppChart
            type="pie"
            data={chartData.ticketVolume}
            height={300}
            dataKey="value"
            nameKey="name"
          />
        </AppCardContent>
      </AppCard>

      <AppCard>
        <AppCardHeader>
          <AppCardTitle>SLA Compliance</AppCardTitle>
          <AppCardDescription>
            Percentage of tickets resolved within SLA
          </AppCardDescription>
        </AppCardHeader>
        <AppCardContent>
          <AppChart
            type="pie"
            data={chartData.slaCompliance}
            height={300}
            dataKey="value"
            nameKey="name"
          />
        </AppCardContent>
      </AppCard>
    </div>
  );
}