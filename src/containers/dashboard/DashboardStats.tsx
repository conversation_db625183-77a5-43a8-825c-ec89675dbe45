"use client";

import { Ticket, Clock, CheckCircle, TrendingUp } from "lucide-react";
import { AppStat } from "@/components/ui-toolkit";

interface DashboardStatsProps {
  stats: {
    totalTickets: number;
    openTickets: number;
    resolvedToday: number;
    avgResolutionTime: string;
  };
}

export function DashboardStats({ stats }: DashboardStatsProps) {
  return (
    <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
      <AppStat
        title="Total Tickets"
        value={stats.totalTickets}
        icon={<Ticket className="h-4 w-4" />}
        delta={{
          value: 12,
          type: "positive",
          label: "vs last month",
        }}
      />
      <AppStat
        title="Open Tickets"
        value={stats.openTickets}
        icon={<Clock className="h-4 w-4" />}
        delta={{
          value: -8,
          type: "positive",
          label: "vs last week",
        }}
      />
      <AppStat
        title="Resolved Today"
        value={stats.resolvedToday}
        icon={<CheckCircle className="h-4 w-4" />}
        delta={{
          value: 15,
          type: "positive",
          label: "vs yesterday",
        }}
      />
      <AppStat
        title="Avg Resolution Time"
        value={stats.avgResolutionTime}
        icon={<TrendingUp className="h-4 w-4" />}
        delta={{
          value: -5,
          type: "positive",
          label: "vs last month",
        }}
      />
    </div>
  );
}