"use client";

import * as React from "react";
import {
  App<PERSON>ard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppEmpty,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  AppBadge,
} from "@/components/ui-toolkit";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PRIORITY_OPTIONS } from "@/lib/enums";
import { useAuth } from "@/hooks/useAuth";
import type { Ticket as TicketType } from "@/lib/demo/types";
import { useGetStatusesQuery } from "@/redux/slices/statuses";

interface RecentTicketsProps {
  recentTickets: TicketType[];
  statusFilter?: string;
  priorityFilter?: string;
  onStatusFilterChange?: (value: string) => void;
  onPriorityFilterChange?: (value: string) => void;
}

export function RecentTickets({ 
  recentTickets, 
  statusFilter = "", 
  priorityFilter = "",
  onStatusFilterChange,
  onPriorityFilterChange 
}: RecentTicketsProps) {
  const { tenant } = useAuth();
  
  // Fetch statuses from store
  const { data: statuses = [] } = useGetStatusesQuery(tenant?.tenantId || '', { skip: !tenant?.tenantId });

  // Helper function to get status color
  const getStatusColor = (statusName: string) => {
    const status = statuses.find(s => s.name === statusName);
    return status?.color || '#6b7280'; // Default gray color
  };

  // Status badge renderer
  const renderStatusBadge = (statusValue: string) => {
    const color = getStatusColor(statusValue);
    return (
      <span 
        className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors"
        style={{ 
          borderColor: color, 
          backgroundColor: 'white',
          color: color 
        }}
      >
        {statusValue.replace("_", " ")}
      </span>
    );
  };
  return (
    <AppCard>
      <AppCardHeader>
        <div className="flex items-center justify-between">
          <div>
            <AppCardTitle>Recent Tickets</AppCardTitle>
            <AppCardDescription>
              Latest tickets that need attention
            </AppCardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={statusFilter} onValueChange={onStatusFilterChange}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map((status) => (
                  <SelectItem key={status._id} value={status.name}>
                    {status.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={onPriorityFilterChange}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="All Priority" />
              </SelectTrigger>
              <SelectContent>
                {PRIORITY_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </AppCardHeader>
      <AppCardContent>
        {recentTickets.length === 0 ? (
          <AppEmpty
            title="No Recent Tickets"
            description="There are no recent tickets to display at the moment."
            icon="ticket"
          />
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Ticket</TableHead>
                <TableHead>Subject</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Assignee</TableHead>
                <TableHead>Created</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentTickets.map((ticket) => (
                <TableRow key={ticket.id}>
                  <TableCell className="font-medium">{ticket.number}</TableCell>
                  <TableCell>{ticket.subject}</TableCell>
                  <TableCell>
                    {renderStatusBadge(ticket.status)}
                  </TableCell>
                  <TableCell>
                    <AppBadge priority={ticket.priority}>
                      {ticket.priority}
                    </AppBadge>
                  </TableCell>
                  <TableCell>
                    {ticket.assignee ? ticket.assignee.name : "Unassigned"}
                  </TableCell>
                  <TableCell>
                    {ticket.createdAt.toLocaleDateString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </AppCardContent>
    </AppCard>
  );
}