import { FileText, Database, Users, Settings } from "lucide-react";

export const exportTypes = [
  { value: "tickets", label: "Tickets", icon: FileText, description: "Export all ticket data including status, priority, and history" },
  { value: "users", label: "Users", icon: Users, description: "Export user profiles and contact information" },
  { value: "settings", label: "Settings", icon: Settings, description: "Export system configuration and preferences" },
  { value: "all", label: "Complete Backup", icon: Database, description: "Export all system data for backup purposes" },
];

export const exportFormats = [
  { value: "csv", label: "CSV", description: "Comma-separated values format" },
  { value: "json", label: "JSON", description: "JavaScript Object Notation format" },
  { value: "xlsx", label: "Excel", description: "Microsoft Excel format" },
];