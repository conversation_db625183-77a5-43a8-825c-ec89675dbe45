import * as React from "react";
import { toast } from "sonner";
import <PERSON> from "papapar<PERSON>";
import * as XLSX from "xlsx";
import { exportTypes } from "../constants";
import axiosInstance from "@/lib/axios";

interface ImportProgress {
  type: 'start' | 'progress' | 'complete';
  message: string;
  total: number;
  processed: number;
  successful: number;
  failed: number;
  skipped: number;
  currentUser?: string;
  pdfReportUrl?: string;
  results?: any[];
  errors?: string[];
}

export const useExportImport = () => {
  const [selectedExportType, setSelectedExportType] = React.useState("");
  const [selectedFormat, setSelectedFormat] = React.useState("csv");
  const [dateRange, setDateRange] = React.useState("all");
  const [includeAttachments, setIncludeAttachments] = React.useState(false);
  const [exporting, setExporting] = React.useState(false);
  const [importing, setImporting] = React.useState(false);
  const [selectedImportFile, setSelectedImportFile] = React.useState<File | null>(null);
  const [importType, setImportType] = React.useState<string>("");
  const [importProgress, setImportProgress] = React.useState<ImportProgress | null>(null);
  const [showProgressModal, setShowProgressModal] = React.useState(false);

  const handleExport = async () => {
    if (!selectedExportType) {
      toast.error("Please select data type to export");
      return;
    }

    setExporting(true);

    // Simulate export process
    await new Promise(resolve => setTimeout(resolve, 2000));

    toast.success(`${exportTypes.find(t => t.value === selectedExportType)?.label} exported successfully!`);
    setExporting(false);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>, type: string) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedImportFile(file);
      setImportType(type);
    }
  };

  const handleImport = async () => {
    if (!selectedImportFile || !importType) {
      toast.error("Please select a file to import");
      return;
    }

    if (importType !== 'users') {
      toast.error("Currently only user import is supported");
      return;
    }

    setImporting(true);
    setShowProgressModal(true);

    try {
      let data: any[] = [];

      // Parse the file
      if (selectedImportFile.name.endsWith('.csv')) {
        const text = await selectedImportFile.text();
        const result = Papa.parse(text, {
          header: true,
          skipEmptyLines: true,
        });
        data = result.data;
      } else if (selectedImportFile.name.endsWith('.xlsx') || selectedImportFile.name.endsWith('.xls')) {
        const arrayBuffer = await selectedImportFile.arrayBuffer();
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (rawData.length > 0) {
          const headers = rawData[0] as string[];
          data = rawData.slice(1).map((row: any) => {
            const obj: any = {};
            headers.forEach((header, index) => {
              obj[header] = row[index];
            });
            return obj;
          });
        }
      } else if (selectedImportFile.name.endsWith('.json')) {
        const text = await selectedImportFile.text();
        data = JSON.parse(text);
        if (!Array.isArray(data)) {
          data = [data];
        }
      } else {
        toast.error("Unsupported file format. Please upload CSV, Excel, or JSON files.");
        setImporting(false);
        setShowProgressModal(false);
        return;
      }

      // Validate data structure
      if (!data || data.length === 0) {
        toast.error("No data found in the file");
        setImporting(false);
        setShowProgressModal(false);
        return;
      }

      // Transform data to match API expectations
      const users = data.map((item: any) => ({
        email: item.email || item.Email,
        name: item.name || item.Name,
        phone: item.phone || item.Phone,
        role: item.role || item.Role,
        department: item.department || item.Department,
        timezone: item.timezone || item.Timezone || 'UTC',
        language: item.language || item.Language || 'en',
        generatePassword: item.generatePassword !== false
      })).filter(user => user.email && user.name); // Filter out invalid entries

      if (users.length === 0) {
        toast.error("No valid users found in the file. Please check the format.");
        setImporting(false);
        setShowProgressModal(false);
        return;
      }

      const total = users.length;
      let processed = 0;

      // Simulate progress for better UX
      setImportProgress({
        type: 'start',
        message: 'Starting bulk import...',
        total,
        processed: 0,
        successful: 0,
        failed: 0,
        skipped: 0
      });

      // Quick progress simulation
      for (let i = 1; i <= total; i += Math.max(1, Math.floor(total / 10))) {
        await new Promise(resolve => setTimeout(resolve, 100));
        setImportProgress({
          type: 'progress',
          message: `Processing users...`,
          total,
          processed: Math.min(i, total),
          successful: 0,
          failed: 0,
          skipped: 0
        });
      }

      // Import all users at once (remove batching)
      console.log(`Sending ${users.length} users to API...`);
      
      let allResults: any[] = [];
      let pdfReportUrl = '';
      let allErrors: string[] = [];
      
      try {
        // Import all users in one API call
        const response = await axiosInstance.post('/users/bulk-import', { 
          users: users 
        });

        const batchResult = response.data as any;
        
        // Collect results from the response
        allResults = batchResult.data?.results || [];
        pdfReportUrl = batchResult.data?.pdfReportUrl || '';
        allErrors = batchResult.data?.errors || [];
        
        processed = users.length;
        
        setImportProgress({
          type: 'progress',
          message: `Processed ${processed} of ${total} users...`,
          total,
          processed,
          successful: allResults.filter((r: any) => r.status === 'success').length,
          failed: allResults.filter((r: any) => r.status === 'failed').length,
          skipped: allResults.filter((r: any) => r.status === 'skipped').length
        });

        // Small delay to show progress
        await new Promise(resolve => setTimeout(resolve, 500));
        
      } catch (error: any) {
        console.error('Import error:', error);
        // If API call fails, mark all as failed
        processed = users.length;
        setImportProgress({
          type: 'progress',
          message: `Import failed: ${error.response?.data?.message || error.message}`,
          total,
          processed,
          successful: 0,
          failed: users.length,
          skipped: 0
        });
        setImporting(false);
        setShowProgressModal(false);
        return;
      }

      // Complete
      const successful = allResults.filter((r: any) => r.status === 'success').length;
      const failed = allResults.filter((r: any) => r.status === 'failed').length;
      const skipped = allResults.filter((r: any) => r.status === 'skipped').length;

      setImportProgress({
        type: 'complete',
        message: `Import completed! ${successful} successful, ${failed} failed, ${skipped} skipped.`,
        total,
        processed: total,
        successful,
        failed,
        skipped,
        results: allResults,
        pdfReportUrl,
        errors: allErrors
      });

      setImporting(false);
      toast.success(`Import completed! ${successful} successful, ${failed} failed, ${skipped} skipped.`);

      // Reset form
      setSelectedImportFile(null);
      setImportType("");

    } catch (error) {
      console.error('Import error:', error);
      toast.error("Failed to import users. Please try again.");
      setImporting(false);
      setShowProgressModal(false);
    }
  };

  const closeProgressModal = () => {
    setShowProgressModal(false);
    setImportProgress(null);
  };

  return {
    selectedExportType,
    setSelectedExportType,
    selectedFormat,
    setSelectedFormat,
    dateRange,
    setDateRange,
    includeAttachments,
    setIncludeAttachments,
    exporting,
    importing,
    selectedImportFile,
    importType,
    handleExport,
    handleFileSelect,
    handleImport,
    importProgress,
    showProgressModal,
    closeProgressModal,
  };
};