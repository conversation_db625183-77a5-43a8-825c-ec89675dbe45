import * as React from "react";
import { Download } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  Label,
  Checkbox,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui-toolkit";
import { exportTypes, exportFormats } from "../constants";

interface ExportSectionProps {
  selectedExportType: string;
  setSelectedExportType: (value: string) => void;
  selectedFormat: string;
  setSelectedFormat: (value: string) => void;
  dateRange: string;
  setDateRange: (value: string) => void;
  includeAttachments: boolean;
  setIncludeAttachments: (checked: boolean) => void;
  exporting: boolean;
  onExport: () => void;
}

export const ExportSection: React.FC<ExportSectionProps> = ({
  selectedExportType,
  setSelectedExportType,
  selectedFormat,
  setSelectedFormat,
  dateRange,
  setDateRange,
  includeAttachments,
  setIncludeAttachments,
  exporting,
  onExport,
}) => {
  return (
    <AppCard>
      <AppCardHeader>
        <AppCardTitle className="flex items-center gap-2">
          <Download className="h-5 w-5" />
          Export Data
        </AppCardTitle>
        <AppCardDescription>
          Export your system data to various formats
        </AppCardDescription>
      </AppCardHeader>
      <AppCardContent className="space-y-6">
        {/* Data Type Selection */}
        <div className="space-y-3">
          <Label htmlFor="exportType">Select Data Type</Label>
          <div className="grid gap-3">
            {exportTypes.map((type) => {
              const Icon = type.icon;
              return (
                <div
                  key={type.value}
                  className={`flex items-start space-x-3 rounded-lg border p-3 cursor-pointer transition-colors hover:bg-accent ${
                    selectedExportType === type.value ? "border-primary bg-accent" : ""
                  }`}
                  onClick={() => setSelectedExportType(type.value)}
                >
                  <div className="flex items-center space-x-2 flex-1">
                    <Icon className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <div className="font-medium">{type.label}</div>
                      <div className="text-sm text-muted-foreground">{type.description}</div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      selectedExportType === type.value ? "border-primary bg-primary" : "border-muted-foreground"
                    }`}>
                      {selectedExportType === type.value && (
                        <div className="w-2 h-2 rounded-full bg-primary-foreground m-1" />
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Format Selection */}
        <div className="space-y-2">
          <Label htmlFor="format">Export Format</Label>
          <Select value={selectedFormat} onValueChange={setSelectedFormat}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {exportFormats.map((format) => (
                <SelectItem key={format.value} value={format.value}>
                  <div>
                    <div className="font-medium">{format.label}</div>
                    <div className="text-xs text-muted-foreground">{format.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Date Range */}
        <div className="space-y-2">
          <Label htmlFor="dateRange">Date Range</Label>
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="last-month">Last Month</SelectItem>
              <SelectItem value="last-3-months">Last 3 Months</SelectItem>
              <SelectItem value="last-6-months">Last 6 Months</SelectItem>
              <SelectItem value="last-year">Last Year</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Additional Options */}
        <div className="space-y-3">
          <Label>Additional Options</Label>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="attachments"
              checked={includeAttachments}
              onCheckedChange={(checked) => setIncludeAttachments(checked === true)}
            />
            <Label htmlFor="attachments" className="text-sm font-normal">
              Include file attachments
            </Label>
          </div>
        </div>

        <AppButton
          onClick={onExport}
          disabled={!selectedExportType || exporting}
          className="w-full"
        >
          {exporting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
              Exporting...
            </>
          ) : (
            <>
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </>
          )}
        </AppButton>
      </AppCardContent>
    </AppCard>
  );
};