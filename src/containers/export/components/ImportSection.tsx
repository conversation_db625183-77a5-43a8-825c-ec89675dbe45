import * as React from "react";
import { Upload, Users, Ticket, Download, X, Co<PERSON>, Check } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  Label,
} from "@/components/ui-toolkit";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";

interface ImportProgress {
  type: 'start' | 'progress' | 'complete';
  message: string;
  total: number;
  processed: number;
  successful: number;
  failed: number;
  skipped: number;
  currentUser?: string;
  pdfReportUrl?: string;
  results?: any[];
  errors?: string[];
}

interface ImportSectionProps {
  importing: boolean;
  selectedImportFile: File | null;
  importType: string;
  onFileSelect: (event: React.ChangeEvent<HTMLInputElement>, type: string) => void;
  onImport: () => void;
  importProgress: ImportProgress | null;
  showProgressModal: boolean;
  onCloseProgressModal: () => void;
}

export const ImportSection: React.FC<ImportSectionProps> = ({
  importing,
  selectedImportFile,
  importType,
  onFileSelect,
  onImport,
  importProgress,
  showProgressModal,
  onCloseProgressModal,
}) => {
  const [copiedPasswords, setCopiedPasswords] = React.useState<Set<string>>(new Set());

  const copyToClipboard = async (text: string, userId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedPasswords(prev => new Set(prev).add(userId));
      toast.success("Password copied to clipboard!");
      setTimeout(() => {
        setCopiedPasswords(prev => {
          const newSet = new Set(prev);
          newSet.delete(userId);
          return newSet;
        });
      }, 2000);
    } catch (err) {
      toast.error("Failed to copy password");
    }
  };
  return (
    <AppCard>
      <AppCardHeader>
        <AppCardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Import Data
        </AppCardTitle>
        <AppCardDescription>
          Import data from files or other systems
        </AppCardDescription>
      </AppCardHeader>
      <AppCardContent className="space-y-6">
        <Tabs defaultValue="users" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Import Users
            </TabsTrigger>
            <TabsTrigger value="tickets" className="flex items-center gap-2">
              <Ticket className="h-4 w-4" />
              Import Tickets
            </TabsTrigger>
          </TabsList>

          {/* Users Import Tab */}
          <TabsContent value="users" className="space-y-4 mt-6">
            <div className="space-y-4">
              <label htmlFor="import-users-file" className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center cursor-pointer block">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <div className="space-y-2">
                  <p className="text-sm font-medium">Import Users from File</p>
                  <p className="text-xs text-muted-foreground">
                    Supports CSV, JSON, and Excel files
                  </p>
                </div>
                <AppButton variant="outline" className="mt-4 pointer-events-none">
                  {selectedImportFile && importType === 'users' ? selectedImportFile.name : 'Select Users File'}
                </AppButton>
                <input
                  type="file"
                  accept=".csv,.json,.xlsx,.xls"
                  onChange={(e) => onFileSelect(e, 'users')}
                  className="hidden"
                  id="import-users-file"
                />
              </label>

              <div className="space-y-3">
                <Label>Required CSV/Excel Headers for Users</Label>
                <div className="bg-muted rounded-lg p-4">
                  <div className="text-sm space-y-2">
                    <div className="font-medium text-foreground mb-3">Your file must include these columns:</div>
                    <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                      <div>• <span className="font-mono bg-background px-1 rounded">email</span> - Email address (required)</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">name</span> - Full name (required)</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">phone</span> - Phone number (optional)</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">role</span> - User role (required, e.g., "Support Agent")</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">department</span> - Department ID (optional)</div>
                    </div>
                    <div className="mt-3 text-xs text-muted-foreground">
                      <p>💡 <strong>Tip:</strong> Download a sample CSV template from the Export section to see the correct format.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Tickets Import Tab */}
          <TabsContent value="tickets" className="space-y-4 mt-6">
            <div className="space-y-4">
              <label htmlFor="import-tickets-file" className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center cursor-pointer block">
                <Ticket className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <div className="space-y-2">
                  <p className="text-sm font-medium">Import Tickets from File</p>
                  <p className="text-xs text-muted-foreground">
                    Supports CSV, JSON, and Excel files
                  </p>
                </div>
                <AppButton variant="outline" className="mt-4 pointer-events-none">
                  {selectedImportFile && importType === 'tickets' ? selectedImportFile.name : 'Select Tickets File'}
                </AppButton>
                <input
                  type="file"
                  accept=".csv,.json,.xlsx,.xls"
                  onChange={(e) => onFileSelect(e, 'tickets')}
                  className="hidden"
                  id="import-tickets-file"
                />
              </label>

              <div className="space-y-3">
                <Label>Required CSV/Excel Headers for Tickets</Label>
                <div className="bg-muted rounded-lg p-4">
                  <div className="text-sm space-y-2">
                    <div className="font-medium text-foreground mb-3">Your file must include these columns:</div>
                    <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                      <div>• <span className="font-mono bg-background px-1 rounded">id</span> - Ticket ID (optional for new tickets)</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">title</span> - Ticket subject (required)</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">description</span> - Ticket description</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">status</span> - open/in-progress/resolved/closed</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">priority</span> - low/medium/high/urgent</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">category</span> - Ticket category</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">requester_email</span> - Customer email (required)</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">requester_name</span> - Customer name</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">assignee_email</span> - Assigned agent email</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">department</span> - Department name</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">created_at</span> - Creation date (YYYY-MM-DD)</div>
                      <div>• <span className="font-mono bg-background px-1 rounded">tags</span> - Comma-separated tags</div>
                    </div>
                    <div className="mt-3 text-xs text-muted-foreground">
                      <p>💡 <strong>Tip:</strong> Download a sample CSV template from the Export section to see the correct format.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="space-y-3 mt-6">
          <Label>Import Guidelines</Label>
          <div className="text-sm text-muted-foreground space-y-1">
            <p>• Ensure your file follows the correct format</p>
            <p>• Maximum file size: 50MB</p>
            <p>• Duplicate entries will be skipped</p>
            <p>• Import process may take a few minutes for large files</p>
            <p>• Invalid data rows will be logged and skipped</p>
          </div>
        </div>

        <div className="mt-6">
          <AppButton
            variant="solid"
            onClick={onImport}
            disabled={importing || !selectedImportFile}
            className="w-full"
          >
            {importing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                Importing {importType === 'users' ? 'Users' : 'Tickets'}...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Import {importType === 'users' ? 'Users' : 'Tickets'}
              </>
            )}
          </AppButton>
        </div>
      </AppCardContent>

      {/* Progress Modal */}
      <Dialog open={showProgressModal} onOpenChange={onCloseProgressModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Importing {importType === 'users' ? 'Users' : 'Tickets'}
            </DialogTitle>
            <DialogDescription>
              Please wait while we import your {importType}. This process may take a few minutes.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>
                  {importProgress?.processed || 0} / {importProgress?.total || 0}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: importProgress
                      ? `${((importProgress.processed || 0) / (importProgress.total || 1)) * 100}%`
                      : '0%'
                  }}
                />
              </div>
            </div>

            {/* Current Status */}
            <div className="text-sm text-gray-600">
              {importProgress?.message || 'Initializing...'}
            </div>

            {/* Statistics */}
            {importProgress && (
              <div className="grid grid-cols-3 gap-4 text-center">
                <div className="space-y-1">
                  <div className="text-2xl font-bold text-green-600">
                    {importProgress.successful || 0}
                  </div>
                  <div className="text-xs text-gray-500">Successful</div>
                </div>
                <div className="space-y-1">
                  <div className="text-2xl font-bold text-red-600">
                    {importProgress.failed || 0}
                  </div>
                  <div className="text-xs text-gray-500">Failed</div>
                </div>
                <div className="space-y-1">
                  <div className="text-2xl font-bold text-yellow-600">
                    {importProgress.skipped || 0}
                  </div>
                  <div className="text-xs text-gray-500">Skipped</div>
                </div>
              </div>
            )}

            {/* Completion Actions */}
            {importProgress?.type === 'complete' && (
              <div className="space-y-4 pt-4 border-t">
                <div className="text-sm text-green-600 font-medium">
                  Import completed successfully!
                </div>

                {/* Import Results */}
                {importProgress.results && importProgress.results.length > 0 && (
                  <div className="space-y-3">
                    <div className="text-sm font-medium text-gray-700">
                      Imported Users & Generated Passwords:
                    </div>
                    <div className="max-h-60 overflow-y-auto space-y-2">
                      {importProgress.results.map((result: any, index: number) => (
                        <div key={result.userId || index} className="bg-gray-50 rounded-lg p-3 border">
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="text-sm font-medium text-gray-900">
                                {result.email}
                              </div>
                              <div className="text-xs text-gray-500">
                                User ID: {result.userId}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="text-sm font-mono bg-white px-2 py-1 rounded border">
                                {result.generatedPassword}
                              </div>
                              <AppButton
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(result.generatedPassword, result.userId)}
                                className="h-8 w-8 p-0"
                              >
                                {copiedPasswords.has(result.userId) ? (
                                  <Check className="h-4 w-4 text-green-600" />
                                ) : (
                                  <Copy className="h-4 w-4" />
                                )}
                              </AppButton>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded">
                      <strong>Security Note:</strong> These passwords are shown only once. Please save them securely and share with users through secure channels.
                    </div>
                  </div>
                )}

                {/* Import Errors */}
                {importProgress.errors && importProgress.errors.length > 0 && (
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-red-700">
                      Errors:
                    </div>
                    <div className="max-h-32 overflow-y-auto space-y-1">
                      {importProgress.errors.map((error: string, index: number) => (
                        <div key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                          {error}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {importProgress.pdfReportUrl && (
                  <AppButton
                    variant="outline"
                    className="w-full"
                    onClick={() => window.open(importProgress.pdfReportUrl, '_blank')}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download PDF Report
                  </AppButton>
                )}

                <AppButton
                  variant="solid"
                  className="w-full"
                  onClick={onCloseProgressModal}
                >
                  Close
                </AppButton>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </AppCard>
  );
};