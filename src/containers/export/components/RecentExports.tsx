import * as React from "react";
import { Calendar, FileText, Download } from "lucide-react";
import {
  AppCard,
  AppCardContent,
  AppCardDescription,
  AppCardHeader,
  App<PERSON>ard<PERSON>itle,
  App<PERSON><PERSON>on,
} from "@/components/ui-toolkit";

const recentExports = [
  { name: "tickets_export_2025-08-18.csv", size: "2.4 MB", date: "18 Aug 2025, 10:30 AM" },
  { name: "users_backup_2025-08-15.json", size: "890 KB", date: "15 Aug 2025, 3:15 PM" },
  { name: "complete_backup_2025-08-10.xlsx", size: "15.2 MB", date: "10 Aug 2025, 9:00 AM" },
];

export const RecentExports: React.FC = () => {
  return (
    <AppCard>
      <AppCardHeader>
        <AppCardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Recent Exports
        </AppCardTitle>
        <AppCardDescription>
          View and download your recent export files
        </AppCardDescription>
      </AppCardHeader>
      <AppCardContent>
        <div className="space-y-3">
          {recentExports.map((file, index) => (
            <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-3">
                <FileText className="h-5 w-5 text-muted-foreground" />
                <div>
                  <div className="font-medium">{file.name}</div>
                  <div className="text-sm text-muted-foreground">{file.size} • {file.date}</div>
                </div>
              </div>
              <AppButton variant="ghost" size="sm">
                <Download className="h-4 w-4" />
              </AppButton>
            </div>
          ))}
        </div>
      </AppCardContent>
    </AppCard>
  );
};