import * as React from "react";
import { SectionHeader } from "@/components/ui-toolkit";
import { useExportImport } from "./hooks/useExportImport";
import { ExportSection } from "./components/ExportSection";
import { ImportSection } from "./components/ImportSection";
import { RecentExports } from "./components/RecentExports";

export const ExportImportContainer: React.FC = () => {
  const {
    selectedExportType,
    setSelectedExportType,
    selectedFormat,
    setSelectedFormat,
    dateRange,
    setDateRange,
    includeAttachments,
    setIncludeAttachments,
    exporting,
    importing,
    selectedImportFile,
    importType,
    handleExport,
    handleFileSelect,
    handleImport,
    importProgress,
    showProgressModal,
    closeProgressModal,
  } = useExportImport();

  return (
    <div className="space-y-6 mx-3">
      <SectionHeader
        title="Export & Import"
        description="Export your data for backup or import data from other systems"
      />

      <div className="grid gap-6 lg:grid-cols-2">
        <ExportSection
          selectedExportType={selectedExportType}
          setSelectedExportType={setSelectedExportType}
          selectedFormat={selectedFormat}
          setSelectedFormat={setSelectedFormat}
          dateRange={dateRange}
          setDateRange={setDateRange}
          includeAttachments={includeAttachments}
          setIncludeAttachments={setIncludeAttachments}
          exporting={exporting}
          onExport={handleExport}
        />

        <ImportSection
          importing={importing}
          selectedImportFile={selectedImportFile}
          importType={importType}
          onFileSelect={handleFileSelect}
          onImport={handleImport}
          importProgress={importProgress}
          showProgressModal={showProgressModal}
          onCloseProgressModal={closeProgressModal}
        />
      </div>

      <RecentExports />
    </div>
  );
};