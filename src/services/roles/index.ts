import { baseApi } from '../api';

export interface Role {
  id: string;
  name: string;
}

export interface CreateRoleRequest {
  name: string;
}

export const rolesApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getAllRoles: builder.query<Role[], void>({
      query: () => ({
        url: '/roles',
        method: 'GET',
      }),
    }),
    getRoleById: builder.query<Role, string>({
      query: (id) => ({
        url: `/roles/${id}`,
        method: 'GET',
      }),
    }),
    createRole: builder.mutation<Role, CreateRoleRequest>({
      query: (body) => ({
        url: '/roles',
        method: 'POST',
        data: body,
      }),
    }),
    updateRole: builder.mutation<Role, { id: string; name: string }>({
      query: ({ id, ...body }) => ({
        url: `/roles/${id}`,
        method: 'PUT',
        data: body,
      }),
    }),
  }),
  overrideExisting: false,
});

export const { useGetAllRolesQuery, useGetRoleByIdQuery, useCreateRoleMutation, useUpdateRoleMutation } = rolesApi;
