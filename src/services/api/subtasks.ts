import { baseApi } from '../api';

export interface SubTask {
  _id: string;
  tenantId: string;
  parentTicketId: string;
  title: string;
  description?: string;
  canDescriptionAndTitleChange: boolean;
  status: string;
  priority?: string;
  assignee?: {
    userId: string;
    name: string;
    email: string;
  };
  dueDate?: string;
  originalEstimateMins?: number;
  remainingEstimateMins?: number;
  rank: number;
  attachments?: Array<{
    url: string;
    publicId: string;
    filename?: string;
    size?: number;
    uploadedAt: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSubTaskRequest {
  title: string;
  description?: string;
  canDescriptionAndTitleChange?: boolean;
  priority?: string;
  assigneeId?: string;
  dueDate?: string;
  originalEstimateMins?: number;
  attachments?: File[];
}

export interface SubTasksResponse {
  success: boolean;
  data: SubTask[];
  pagination?: {
    page: number;
    size: number;
    total: number;
    pages: number;
  };
}

const subtasksApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getSubTasks: builder.query<SubTasksResponse, { ticketId: string; page?: number; size?: number }>({
      query: ({ ticketId, page = 1, size = 20 }) => ({
        url: `/${ticketId}/subtasks`,
        method: 'GET',
        params: { page, size },
      }),
      providesTags: ['SubTasks'],
    }),
    createSubTask: builder.mutation<{ success: boolean; data: SubTask }, { ticketId: string; data: CreateSubTaskRequest }>({
      query: ({ ticketId, data }) => {
        const formData = new FormData();

        // Add basic fields
        formData.append('title', data.title);
        if (data.description) formData.append('description', data.description);
        if (data.canDescriptionAndTitleChange !== undefined) {
          formData.append('canDescriptionAndTitleChange', data.canDescriptionAndTitleChange.toString());
        }
        if (data.priority) formData.append('priority', data.priority);
        if (data.assigneeId) formData.append('assigneeId', data.assigneeId);
        if (data.dueDate) formData.append('dueDate', data.dueDate);
        if (data.originalEstimateMins) {
          formData.append('originalEstimateMins', data.originalEstimateMins.toString());
        }

        // Add attachments if present
        if (data.attachments && data.attachments.length > 0) {
          data.attachments.forEach((file, index) => {
            formData.append('attachments', file);
          });
        }

        return {
          url: `/${ticketId}/subtasks`,
          method: 'POST',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        };
      },
      invalidatesTags: ['SubTasks', 'Tickets'],
    }),
  }),
  overrideExisting: false,
});

export const { useGetSubTasksQuery, useCreateSubTaskMutation } = subtasksApi;