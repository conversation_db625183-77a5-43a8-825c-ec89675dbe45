import { baseApi } from '../api';

export interface ChecklistItem {
  itemId: string;
  text: string;
  isDone: boolean;
  isMandatory: boolean;
  assigneeId?: string;
  dueDate?: string;
  note?: string;
  linkedSubTaskId?: string;
}

export interface Checklist {
  _id: string;
  tenantId: string;
  ticketId?: string;
  subTaskId?: string;
  title?: string;
  items: ChecklistItem[];
  doneCount: number;
  totalCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateChecklistPayload {
  title?: string;
  items: Array<{
    text: string;
    isMandatory?: boolean;
    assigneeId?: string;
    dueDate?: string;
    note?: string;
  }>;
}

export interface UpdateChecklistItemPayload {
  text?: string;
  isDone?: boolean;
  isMandatory?: boolean;
  assigneeId?: string;
  dueDate?: string;
  note?: string;
}

export interface PromoteItemToSubTaskPayload {
  title?: string;
  description?: string;
  priority?: string;
  assigneeId?: string;
  dueDate?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
}

export interface PromoteItemResponse {
  subTask: any;
  checklist: Checklist;
}

export const checklistApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get checklists for a ticket
    getChecklistsForTicket: builder.query<Checklist[], string>({
      query: (ticketId) => ({
        url: `/tickets/${ticketId}/checklists`,
        method: 'GET',
      }),
      transformResponse: (response: ApiResponse<Checklist[]>) => response.data,
      providesTags: (result, error, ticketId) => [
        { type: 'Checklists' as const, id: `ticket-${ticketId}` },
        ...(result?.map(checklist => ({ type: 'Checklists' as const, id: checklist._id })) || [])
      ],
    }),

    // Get checklists for a subtask
    getChecklistsForSubTask: builder.query<Checklist[], string>({
      query: (subTaskId) => ({
        url: `/subtasks/${subTaskId}/checklists`,
        method: 'GET',
      }),
      transformResponse: (response: ApiResponse<Checklist[]>) => response.data,
      providesTags: (result, error, subTaskId) => [
        { type: 'Checklists' as const, id: `subtask-${subTaskId}` },
        ...(result?.map(checklist => ({ type: 'Checklists' as const, id: checklist._id })) || [])
      ],
    }),

    // Create checklist for ticket
    createChecklistForTicket: builder.mutation<Checklist, { ticketId: string; data: CreateChecklistPayload }>({
      query: ({ ticketId, data }) => ({
        url: `/tickets/${ticketId}/checklists`,
        method: 'POST',
        data,
      }),
      transformResponse: (response: ApiResponse<Checklist>) => response.data,
      invalidatesTags: (result, error, { ticketId }) => [
        { type: 'Checklists' as const, id: `ticket-${ticketId}` },
        { type: 'Tickets' as const, id: ticketId }
      ],
    }),

    // Create checklist for subtask
    createChecklistForSubTask: builder.mutation<Checklist, { subTaskId: string; data: CreateChecklistPayload }>({
      query: ({ subTaskId, data }) => ({
        url: `/subtasks/${subTaskId}/checklists`,
        method: 'POST',
        data,
      }),
      transformResponse: (response: ApiResponse<Checklist>) => response.data,
      invalidatesTags: (result, error, { subTaskId }) => [
        { type: 'Checklists' as const, id: `subtask-${subTaskId}` }
      ],
    }),

    // Add item to checklist
    addChecklistItem: builder.mutation<Checklist, { checklistId: string; data: Omit<ChecklistItem, 'itemId' | 'isDone'> }>({
      query: ({ checklistId, data }) => ({
        url: `/checklists/${checklistId}/items`,
        method: 'POST',
        data,
      }),
      transformResponse: (response: ApiResponse<Checklist>) => response.data,
      invalidatesTags: (result, error, { checklistId }) => [
        { type: 'Checklists' as const, id: checklistId }
      ],
    }),

    // Update checklist item
    updateChecklistItem: builder.mutation<Checklist, { checklistId: string; itemId: string; data: UpdateChecklistItemPayload }>({
      query: ({ checklistId, itemId, data }) => ({
        url: `/checklists/${checklistId}/items/${itemId}`,
        method: 'PATCH',
        data,
      }),
      transformResponse: (response: ApiResponse<Checklist>) => response.data,
      invalidatesTags: (result, error, { checklistId }) => [
        { type: 'Checklists' as const, id: checklistId }
      ],
    }),

    // Remove checklist item
    removeChecklistItem: builder.mutation<Checklist, { checklistId: string; itemId: string }>({
      query: ({ checklistId, itemId }) => ({
        url: `/checklists/${checklistId}/items/${itemId}`,
        method: 'DELETE',
      }),
      transformResponse: (response: ApiResponse<Checklist>) => response.data,
      invalidatesTags: (result, error, { checklistId }) => [
        { type: 'Checklists' as const, id: checklistId }
      ],
    }),

    // Promote item to subtask
    promoteItemToSubTask: builder.mutation<PromoteItemResponse, { checklistId: string; itemId: string; data?: PromoteItemToSubTaskPayload }>({
      query: ({ checklistId, itemId, data }) => ({
        url: `/checklists/${checklistId}/items/${itemId}/promote-to-subtask`,
        method: 'POST',
        data: data || {},
      }),
      transformResponse: (response: ApiResponse<PromoteItemResponse>) => response.data,
      invalidatesTags: (result, error, { checklistId }) => [
        { type: 'Checklists' as const, id: checklistId },
        { type: 'Tickets' as const, id: result?.checklist.ticketId || result?.checklist.subTaskId }
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetChecklistsForTicketQuery,
  useGetChecklistsForSubTaskQuery,
  useCreateChecklistForTicketMutation,
  useCreateChecklistForSubTaskMutation,
  useAddChecklistItemMutation,
  useUpdateChecklistItemMutation,
  useRemoveChecklistItemMutation,
  usePromoteItemToSubTaskMutation,
} = checklistApi;