import { baseApi } from '../api';
import type {
  Comment,
  CommentAuthor,
  CreateCommentRequest,
  UpdateCommentRequest,
  CommentsQueryParams,
  CommentsResponse,
  CommentResponse,
  CreateCommentResponse,
  UpdateCommentResponse,
  DeleteCommentResponse,
} from '../../types/api';

export const commentsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get comments for a ticket or subtask
    getComments: builder.query<CommentsResponse, { ticketId: string; params?: CommentsQueryParams }>({
      query: ({ ticketId, params }) => {
        const entityType = params?.entityType;
        const endpoint = entityType === '1' ? 'subtasks' : 'tickets';
        return {
          url: `/${endpoint}/${ticketId}/comments`,
          method: 'GET',
          params: params || {},
        };
      },
      transformResponse: (response: CommentsResponse) => {
        // Transform the response to match expected frontend structure
        if (response.data && Array.isArray(response.data)) {
          response.data = response.data.map(comment => {
            // If authorId is an object, move it to author field
            if (typeof comment.authorId === 'object' && comment.authorId !== null) {
              comment.author = comment.authorId as CommentAuthor;
              comment.authorId = (comment.authorId as CommentAuthor)._id;
            }
            return comment;
          });
        }
        return response;
      },
      providesTags: (result, error, { ticketId }) => [
        { type: 'Comments', id: `ticket-${ticketId}` },
        'Comments'
      ],
    }),

    // Create a new comment
    createComment: builder.mutation<CreateCommentResponse, { ticketId: string; data: CreateCommentRequest }>({
      query: ({ ticketId, data }) => {
        const entityType = data.entityType;
        const endpoint = entityType === '1' ? 'subtasks' : 'tickets';
        return {
          url: `/${endpoint}/${ticketId}/comments`,
          method: 'POST',
          data,
        };
      },
      transformResponse: (response: CreateCommentResponse) => {
        // Transform the response to match expected frontend structure
        if (response.data && typeof response.data.authorId === 'object') {
          response.data.author = response.data.authorId as CommentAuthor;
          response.data.authorId = (response.data.authorId as CommentAuthor)._id;
        }
        return response;
      },
      invalidatesTags: (result, error, { ticketId }) => [
        { type: 'Comments', id: `ticket-${ticketId}` },
        { type: 'Tickets', id: ticketId },
        { type: 'TicketHistory', id: ticketId },
        'Comments'
      ],
    }),

    // Update a comment
    updateComment: builder.mutation<UpdateCommentResponse, { commentId: string; ticketId: string; data: UpdateCommentRequest }>({
      query: ({ commentId, data }) => ({
        url: `/comments/${commentId}`,
        method: 'PATCH',
        data,
      }),
      transformResponse: (response: UpdateCommentResponse) => {
        // Transform the response to match expected frontend structure
        if (response.data && typeof response.data.authorId === 'object') {
          response.data.author = response.data.authorId as CommentAuthor;
          response.data.authorId = (response.data.authorId as CommentAuthor)._id;
        }
        return response;
      },
      invalidatesTags: (result, error, { ticketId }) => [
        { type: 'Comments', id: ticketId },
        { type: 'Tickets', id: ticketId },
        { type: 'TicketHistory', id: ticketId },
        'Comments'
      ],
    }),

    // Delete a comment
    deleteComment: builder.mutation<DeleteCommentResponse, { commentId: string; ticketId: string }>({
      query: ({ commentId }) => ({
        url: `/comments/${commentId}`,
        method: 'DELETE',
      }),
      transformResponse: (response: DeleteCommentResponse) => response,
      invalidatesTags: (result, error, { ticketId }) => [
        { type: 'Comments', id: ticketId },
        { type: 'Tickets', id: ticketId },
        { type: 'TicketHistory', id: ticketId },
        'Comments'
      ],
    }),

    // Get a single comment by ID
    getCommentById: builder.query<CommentResponse, string>({
      query: (commentId) => ({
        url: `/comments/${commentId}`,
        method: 'GET',
      }),
      transformResponse: (response: CommentResponse) => {
        // Transform the response to match expected frontend structure
        if (response.data && typeof response.data.authorId === 'object') {
          response.data.author = response.data.authorId as CommentAuthor;
          response.data.authorId = (response.data.authorId as CommentAuthor)._id;
        }
        return response;
      },
      providesTags: (result, error, commentId) => [
        { type: 'Comments', id: commentId }
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetCommentsQuery,
  useCreateCommentMutation,
  useUpdateCommentMutation,
  useDeleteCommentMutation,
  useGetCommentByIdQuery,
} = commentsApi;