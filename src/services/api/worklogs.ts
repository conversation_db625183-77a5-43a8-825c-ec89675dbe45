import { baseApi } from '../api';
import { ApiResponse } from '@/types/api';

export interface Worklog {
  _id: string;
  tenantId: string;
  ticketId?: string;
  subTaskId?: string;
  agentId: string;
  minutes: number;
  note?: string;
  billable: boolean;
  endDateTime?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateWorklogPayload {
  minutes: number;
  note?: string;
  billable?: boolean;
  endDateTime?: string;
}

export interface UpdateWorklogPayload {
  minutes?: number;
  note?: string;
  billable?: boolean;
  endDateTime?: string;
}

export interface WorklogSummary {
  totalMinutes: number;
  billableMinutes: number;
  totalHours: number;
  billableHours: number;
  agentBreakdown?: Array<{
    _id: string;
    totalMinutes: number;
    billableMinutes: number;
    entryCount: number;
  }>;
}

export interface WorklogsResponse {
  success: boolean;
  data: Worklog[];
  pagination: {
    page: number;
    size: number;
    total: number;
    pages: number;
  };
  summary: {
    totalMinutes: number;
    billableMinutes: number;
    totalHours: number;
    billableHours: number;
  };
}

export interface WorklogSummaryResponse {
  success: boolean;
  data: WorklogSummary;
}

export type CreateWorklogResponse = ApiResponse<Worklog>;
export type UpdateWorklogResponse = ApiResponse<Worklog>;
export type DeleteWorklogResponse = ApiResponse<{ message: string }>;

export const worklogsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Create worklog for ticket
    createWorklog: builder.mutation<CreateWorklogResponse, { ticketId: string; data: CreateWorklogPayload }>({
      query: ({ ticketId, data }) => ({
        url: `/tickets/${ticketId}/worklogs`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['Worklogs', 'Tickets'],
    }),

    // List worklogs for ticket
    getWorklogs: builder.query<WorklogsResponse, { ticketId: string; page?: number; size?: number; agentId?: string }>({
      query: ({ ticketId, ...params }) => ({
        url: `/tickets/${ticketId}/worklogs`,
        method: 'GET',
        params,
      }),
      providesTags: ['Worklogs'],
    }),

    // Get worklog summary for ticket
    getWorklogSummary: builder.query<WorklogSummaryResponse, string>({
      query: (ticketId) => ({
        url: `/tickets/${ticketId}/worklogs/summary`,
        method: 'GET',
      }),
      providesTags: ['Worklogs'],
    }),

    // Update worklog
    updateWorklog: builder.mutation<UpdateWorklogResponse, { worklogId: string; data: UpdateWorklogPayload }>({
      query: ({ worklogId, data }) => ({
        url: `/worklogs/${worklogId}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: ['Worklogs', 'Tickets'],
    }),

    // Delete worklog
    deleteWorklog: builder.mutation<DeleteWorklogResponse, string>({
      query: (worklogId) => ({
        url: `/worklogs/${worklogId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Worklogs', 'Tickets'],
    }),

    // Create worklog for subtask
    createSubTaskWorklog: builder.mutation<CreateWorklogResponse, { subTaskId: string; data: CreateWorklogPayload }>({
      query: ({ subTaskId, data }) => ({
        url: `/subtasks/${subTaskId}/worklogs`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['Worklogs', 'Tickets'],
    }),

    // List worklogs for subtask
    getSubTaskWorklogs: builder.query<WorklogsResponse, { subTaskId: string; page?: number; size?: number }>({
      query: ({ subTaskId, ...params }) => ({
        url: `/subtasks/${subTaskId}/worklogs`,
        method: 'GET',
        params,
      }),
      providesTags: ['Worklogs'],
    }),
  }),
  overrideExisting: false,
});

export const {
  useCreateWorklogMutation,
  useGetWorklogsQuery,
  useGetWorklogSummaryQuery,
  useUpdateWorklogMutation,
  useDeleteWorklogMutation,
  useCreateSubTaskWorklogMutation,
  useGetSubTaskWorklogsQuery,
} = worklogsApi;