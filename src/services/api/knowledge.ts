import { baseApi } from './index';
import { KnowledgeArticle } from '../../types';

interface GetKnowledgeArticlesResponse {
  success: boolean;
  data: {
    articles: KnowledgeArticle[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

interface GetKnowledgeArticlesParams {
  category?: string;
  search?: string;
  status?: 'draft' | 'published' | 'archived';
  sortBy?: 'createdAt' | 'updatedAt' | 'title' | 'views' | 'helpful' | 'notHelpful';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

interface CreateKnowledgeArticleRequest {
  title: string;
  content: string;
  category: string;
  tags?: string[];
  status?: 'draft' | 'published' | 'archived';
  isInternal?: boolean;
  isPinned?: boolean;
}

interface UpdateKnowledgeArticleRequest {
  title?: string;
  content?: string;
  category?: string;
  tags?: string[];
  status?: 'draft' | 'published' | 'archived';
  isInternal?: boolean;
  isPinned?: boolean;
}

interface VoteRequest {
  isHelpful: boolean;
}

interface SearchKnowledgeParams {
  q: string;
  category?: string;
  limit?: number;
}

interface GetCategoriesResponse {
  success: boolean;
  data: Array<{
    category: string;
    count: number;
  }>;
}

export const knowledgeApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get all knowledge articles
    getKnowledgeArticles: builder.query<GetKnowledgeArticlesResponse, GetKnowledgeArticlesParams>({
      query: (params = {}) => {
        // If search is provided, use the search endpoint for better results
        if (params.search) {
          return {
            url: '/knowledge/search',
            method: 'GET',
            params: {
              q: params.search,
              category: params.category,
              limit: params.limit || 20,
            },
          };
        }
        
        // Otherwise use regular listing endpoint
        return {
          url: '/knowledge',
          method: 'GET',
          params,
        };
      },
      providesTags: ['KnowledgeArticle'],
      // Transform search response to match the expected format
      transformResponse: (response: any, meta, arg) => {
        if (arg.search) {
          // Transform search response to match getKnowledgeArticles format
          return {
            success: response.success,
            data: {
              articles: response.data || [],
              pagination: {
                page: 1,
                limit: arg.limit || 20,
                total: response.data?.length || 0,
                pages: 1
              }
            }
          };
        }
        return response;
      },
    }),

    // Get single knowledge article
    getKnowledgeArticle: builder.query<{ success: boolean; data: KnowledgeArticle }, string>({
      query: (id) => ({
        url: `/knowledge/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'KnowledgeArticle', id }],
    }),

    // Create knowledge article
    createKnowledgeArticle: builder.mutation<{ success: boolean; data: KnowledgeArticle }, CreateKnowledgeArticleRequest>({
      query: (data) => ({
        url: '/knowledge',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['KnowledgeArticle'],
    }),

    // Update knowledge article
    updateKnowledgeArticle: builder.mutation<{ success: boolean; data: KnowledgeArticle }, { id: string } & UpdateKnowledgeArticleRequest>({
      query: ({ id, ...data }) => ({
        url: `/knowledge/${id}`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'KnowledgeArticle', id },
        'KnowledgeArticle',
      ],
    }),

    // Delete knowledge article
    deleteKnowledgeArticle: builder.mutation<{ success: boolean }, string>({
      query: (id) => ({
        url: `/knowledge/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['KnowledgeArticle'],
    }),

    // Vote on knowledge article
    voteKnowledgeArticle: builder.mutation<{ 
      success: boolean; 
      message: string; 
      data: { 
        helpful: number; 
        notHelpful: number; 
        userVote: boolean | null;
      } 
    }, { id: string } & VoteRequest>({
      query: ({ id, ...data }) => ({
        url: `/knowledge/${id}/vote`,
        method: 'POST',
        data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'KnowledgeArticle', id },
      ],
    }),

    // Get popular articles
    getPopularKnowledgeArticles: builder.query<{ success: boolean; data: KnowledgeArticle[] }, { limit?: number }>({
      query: (params = {}) => ({
        url: '/knowledge/popular',
        method: 'GET',
        params,
      }),
      providesTags: ['KnowledgeArticle'],
    }),

    // Get categories with counts
    getKnowledgeCategories: builder.query<GetCategoriesResponse, void>({
      query: () => ({
        url: '/knowledge/categories',
        method: 'GET',
      }),
      providesTags: ['KnowledgeArticle'],
    }),

    // Search knowledge articles
    searchKnowledgeArticles: builder.query<{ success: boolean; data: KnowledgeArticle[] }, SearchKnowledgeParams>({
      query: (params) => ({
        url: '/knowledge/search',
        method: 'GET',
        params,
      }),
      providesTags: ['KnowledgeArticle'],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetKnowledgeArticlesQuery,
  useGetKnowledgeArticleQuery,
  useCreateKnowledgeArticleMutation,
  useUpdateKnowledgeArticleMutation,
  useDeleteKnowledgeArticleMutation,
  useVoteKnowledgeArticleMutation,
  useGetPopularKnowledgeArticlesQuery,
  useGetKnowledgeCategoriesQuery,
  useSearchKnowledgeArticlesQuery,
} = knowledgeApi;
