import { baseApi } from '../api';
import { ApiResponse } from '@/types/api';
import { TicketStatus, TicketPriority } from '@/lib/enums';

export interface Attachment {
  _id: string;
  filename: string;
  url: string;
  size?: number;
  mimetype?: string;
}

export interface TicketData {
  _id: string;
  tenantId: string;
  ticketKey: string;
  title: string;
  description: string;
  priority: TicketPriority;
  status: TicketStatus;
  requesterId?: string;
  requester?: {
    userId: string;
    name: string;
    email: string;
  };
  assigneeId?: string;
  assignee?: {
    userId: string;
    name: string;
    email: string;
  };
  department?: {
    id: string;
    name: string;
  };
  parentTicketId?: string;
  watchers: any[];
  tags: string[];
  slaPaused: boolean;
  respondBy?: string;
  resolveBy?: string;
  slaPolicyId?: string;
  openedAt: string;
  subTaskCount: number;
  ticketChecklistProgress: {
    done: number;
    total: number;
  };
  commentCount: number;
  worklogMinutes: number;
  estimatedTime?: number;
  createdBy: string;
  lastActionAt: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
  mergedTickets?: MergedTicketSubDetail[];
  attachments?: Attachment[];
  canDescriptionAndTitleChange?: boolean;
}

export interface TicketsResponse {
  success: boolean;
  data: TicketData[];
  pagination: {
    page: number;
    size: number;
    total: number;
    pages: number;
  };
}

export interface TicketSummary {
  id: string;
  ticketKey: string;
  title: string;
  subject: string;
  status: string;
  priority: string;
  requester: { name: string } | string;
  assignee?: { name: string } | null;
  createdAt: string;
}

export interface CreateTicketPayload {
  title: string;
  description: string;
  canDescriptionAndTitleChange?: boolean;
  priority: string;
  departmentId?: string;
  parentTicketId?: string;
  requester?: {
    userId: string;
    name: string;
    email: string;
  };
  assigneeId?: string;
  tags: string[];
  attachments?: File[];
}

export type CreateTicketResponse = ApiResponse<TicketData>;

export interface MergeTicketsPayload {
  mainTicketId: string;
  ticketIds: string[];
  comment?: string;
}

export interface UpdateMergePayload {
  mainTicketId: string;
  action: 'add' | 'remove' | 'add_comment';
  ticketIds?: string[];
  mergedTicketId?: string;
  comment?: string;
}

export interface MergedTicketSubDetail {
  id: string;
  title: string;
  requester: {
    userId: string;
    name: string;
    email: string;
  };
  assigneeId?: string;
  comments: number;
}

export interface MergedTicketData {
  mainTicket: TicketData;
  mergedTickets: MergedTicketSubDetail[];
}

export interface MergedTicketsResponse {
  success: boolean;
  data: MergedTicketData[];
  pagination?: {
    page: number;
    size: number;
    total: number;
    pages: number;
  };
}

export type MergeTicketsResponse = ApiResponse<null>;

export interface HistoryEntry {
  timestamp: string;
  message: string;
}

export interface HistoryResponse {
  success: boolean;
  data: HistoryEntry[];
  pagination: {
    page: number;
    size: number;
    total: number;
    pages: number;
  };
}

export interface RecentTicketsQuery {
  tenantId: string;
  status?: string | string[];
  priority?: string | string[];
  createdAfter?: string;
  createdBefore?: string;
  page?: number;
  size?: number;
}

export interface RecentTicketsResponse {
  success: boolean;
  data: TicketData[];
  pagination: {
    page: number;
    size: number;
    total: number;
    pages: number;
  };
  filters: {
    tenantId: string;
    dateRange: any;
    status?: string | string[];
    priority?: string | string[];
  };
}

export const ticketsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getTickets: builder.query<TicketsResponse, { statusId?: string; priority?: string; search?: string; page?: number; size?: number; assigneeId?: string[]; departmentId?: string[] } | void>({
      query: (params) => ({
        url: '/tickets',
        method: 'GET',
        params: params || {},
      }),
      transformResponse: (response: TicketsResponse) => {
        return response;
      },
      providesTags: ['Tickets'],
    }),
    getTicketById: builder.query<TicketData, string>({
      query: (ticketKey) => ({ url: `/tickets/${ticketKey}`, method: 'GET' }),
      transformResponse: (response: any) => {
        console.log('Raw API response for getTicketById:', response);
        
        // Handle the response format: { success: true, data: {...} }
        if (response.success && response.data) {
          return response.data;
        }
        
        // Fallback for direct data response
        if (response._id) {
          return response;
        }
        
        throw new Error('Invalid response format');
      },
      providesTags: (result, error, ticketKey) => [{ type: 'Tickets', id: ticketKey }],
    }),
    createTicket: builder.mutation<CreateTicketResponse, CreateTicketPayload>({
      query: (payload) => {
        console.log('RTK Query - Sending payload:', payload);
        
        // Check if attachments are present
        if (payload.attachments && payload.attachments.length > 0) {
          // Use FormData for multipart upload
          const formData = new FormData();
          formData.append('title', payload.title);
          formData.append('description', payload.description);
          formData.append('priority', payload.priority);
          formData.append('t', payload.departmentId || '');
          formData.append('tags', JSON.stringify(payload.tags));
          formData.append('tags', JSON.stringify(payload.tags));
          
          if (payload.requester) {
            formData.append('requester', JSON.stringify(payload.requester));
          }
          if (payload.assigneeId) {
            formData.append('assigneeId', payload.assigneeId);
          }
          if (payload.parentTicketId) {
            formData.append('parentTicketId', payload.parentTicketId);
          }
          
          // Add attachments
          payload.attachments.forEach((file, index) => {
            formData.append('attachments', file);
          });
          
          return {
            url: '/tickets',
            method: 'POST',
            data: formData,
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          };
        } else {
          // Use JSON for regular payload
          const { attachments, ...jsonPayload } = payload;
          return {
            url: '/tickets',
            method: 'POST',
            data: jsonPayload,
          };
        }
      },
      transformResponse: (response: CreateTicketResponse) => {
        console.log('Raw API response for createTicket:', response);
        return response;
      },
      invalidatesTags: ['Tickets', 'TicketHistory'],
    }),
    getRecentTickets: builder.query<RecentTicketsResponse, RecentTicketsQuery>({
      query: (params) => ({
        url: '/tickets/recent',
        method: 'GET',
        params,
      }),
      transformResponse: (response: RecentTicketsResponse) => {
        return response;
      },
      providesTags: ['Tickets'],
    }),
    mergeTickets: builder.mutation<MergeTicketsResponse, MergeTicketsPayload>({
      query: (payload) => ({
        url: '/tickets/merge',
        method: 'POST',
        data: payload,
      }),
      transformResponse: (response: MergeTicketsResponse) => {
        return response;
      },
      invalidatesTags: ['Tickets', 'TicketHistory'],
    }),
    updateMergeTickets: builder.mutation<MergeTicketsResponse, UpdateMergePayload>({
      query: (payload) => ({
        url: '/tickets/merge',
        method: 'PUT',
        data: payload,
      }),
      transformResponse: (response: MergeTicketsResponse) => {
        return response;
      },
      invalidatesTags: ['Tickets', 'TicketHistory'],
    }),
    getMergedTickets: builder.query<MergedTicketsResponse, { page?: number; size?: number }>({
      query: (params) => ({
        url: '/tickets/merged',
        method: 'GET',
        params: params || {},
      }),
      transformResponse: (response: MergedTicketsResponse) => {
        return response;
      },
      providesTags: ['Tickets'],
    }),
    getTicketHistory: builder.query<HistoryResponse, { ticketKey: string; page?: number; size?: number }>({
      query: ({ ticketKey, ...params }) => ({
        url: `/tickets/${ticketKey}/history`,
        method: 'GET',
        params: params || {},
      }),
      transformResponse: (response: HistoryResponse) => {
        return response;
      },
      providesTags: (result, error, { ticketKey }) => [{ type: 'TicketHistory', id: ticketKey }],
    }),
    updateTicket: builder.mutation<TicketData, { ticketKey: string; updates: any }>({
      query: ({ ticketKey, updates }) => ({
        url: `/tickets/${ticketKey}`,
        method: 'PUT',
        data: updates,
      }),
      transformResponse: (response: any) => {
        if (response.success && response.data) {
          return response.data;
        }
        if (response._id) {
          return response;
        }
        throw new Error('Invalid response format');
      },
      invalidatesTags: (result, error, { ticketKey }) => [
        { type: 'Tickets', id: ticketKey },
        'Tickets'
      ],
    }),
    getTicketsByUser: builder.query<TicketsResponse, { userId: string; page?: number; size?: number }>({
      query: ({ userId, page = 1, size = 20 }) => ({
        url: `/tickets/user/${userId}`,
        method: 'GET',
        params: { page, size },
      }),
      providesTags: ['Tickets'],
    }),
  }),
  overrideExisting: false,
});

export const { useGetTicketsQuery, useGetTicketByIdQuery, useCreateTicketMutation, useGetRecentTicketsQuery, useMergeTicketsMutation, useUpdateMergeTicketsMutation, useGetMergedTicketsQuery, useGetTicketHistoryQuery, useUpdateTicketMutation, useGetTicketsByUserQuery } = ticketsApi;
