import { baseApi } from '../api';
import { ApiResponse } from '@/types/api';

export interface Department {
  _id: string;
  tenantId: string;
  name: string;
  description?: string;
  status: 'active' | 'inactive';
  isDefault?: boolean;
  userCount?: number;
  canDelete?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface DepartmentsResponse extends ApiResponse<{
  departments: Department[];
  count: number;
  tenantId: string;
}> {}

export interface UserDepartmentsResponse extends ApiResponse<{
  user: {
    _id: string;
    name: string;
    email: string;
  };
  departments: Department[];
  count: number;
}> {}

export const departmentsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getDepartments: builder.query<DepartmentsResponse, void>({
      query: () => ({
        url: '/v1/departments',
        method: 'GET',
      }),
      providesTags: ['Departments'],
    }),
    getDepartmentUsers: builder.query<DepartmentUsersResponse, string>({
      query: (departmentId) => ({
        url: `/v1/departments/${departmentId}/users`,
        method: 'GET',
      }),
      providesTags: ['DepartmentUsers'],
    }),
    getUserDepartments: builder.query<UserDepartmentsResponse, void>({
      query: () => ({
        url: '/departments/my',
        method: 'GET',
      }),
      providesTags: ['UserDepartments'],
    }),
  }),
});

export const {
  useGetDepartmentsQuery,
  useGetDepartmentUsersQuery,
  useGetUserDepartmentsQuery,
} = departmentsApi;