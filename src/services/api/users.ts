import { baseApi } from '../api';
import { ApiResponse } from '@/types/api';

export interface User {
  _id: string;
  tenantId: string;
  name: string;
  email: string;
  emails: string[];
  phone?: string;
  role: string | {
    _id: string;
    name: string;
    permissions: string[];
  };
  roles: Array<{
    _id: string;
    name: string;
    userType: string;
    permissions: string[];
  }>;
  department?: {
    _id: string;
    name: string;
    description: string;
  };
  permissions: string[];
  status: string;
  avatar?: string;
  timezone?: string;
  language?: string;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: {
    _id: string;
    name: string;
    email: string;
  };
}

export interface ProfileResponse extends ApiResponse<{ user: User }> {}

export interface UpdateProfilePayload {
  userId?: string; // optional, for admin updating other users
  name?: string;
  email?: string;
  phone?: string;
  avatar?: string;
  timezone?: string;
  language?: string;
}

export interface UserByDepartment {
  id: string;
  name: string;
  fullName: string;
  email: string;
}

export interface UsersByDepartmentsResponse extends ApiResponse<UserByDepartment[]> {}

export interface UserTrackingResponse extends ApiResponse<{
  userId: string;
  worklogs: any[];
  tickets: any[];
}> {}

export const usersApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getProfile: builder.query<ProfileResponse, { userId?: string }>({
      query: (params) => ({
        url: '/users/profile',
        method: 'GET',
        params, // send as query params
      }),
      providesTags: ['Profile'],
    }),
    updateProfile: builder.mutation<ProfileResponse, UpdateProfilePayload | FormData>({
      query: (data) => ({
        url: '/users/profile',
        method: 'PUT',
        data,
       headers: {
              'Content-Type': 'multipart/form-data',
            },
      }),
      invalidatesTags: ['Profile'],
    }),
    getUserTracking: builder.query<UserTrackingResponse, { userId: string; startDate?: string; endDate?: string }>({
      query: ({ userId, startDate, endDate }) => ({
        url: `/users/userTracking/${userId}`,
        method: 'GET',
        params: { startDate, endDate },
      }),
      providesTags: ['Profile'],
    }),
    getUsersByDepartments: builder.query<UsersByDepartmentsResponse, void>({
      query: () => ({
        url: '/users/users-by-departments',
        method: 'GET',
      }),
      providesTags: ['Users'],
    }),
  }),
});

export const {
  useGetProfileQuery,
  useUpdateProfileMutation,
  useGetUserTrackingQuery,
  useGetUsersByDepartmentsQuery,
} = usersApi;