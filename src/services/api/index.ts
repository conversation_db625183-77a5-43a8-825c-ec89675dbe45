import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '../../lib/rtkAxiosBaseQuery';

export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';

export const baseApi = createApi({
  reducerPath: 'api',
  tagTypes: ['Tickets', 'TicketHistory', 'KnowledgeArticle', 'Comments', 'Checklists', 'Worklogs', 'Profile', 'Users', 'SubTasks'],
  baseQuery: axiosBaseQuery({ baseUrl: `${API_BASE_URL}` }),
  endpoints: () => ({}),
});
