import axiosInstance from "@/lib/axios";
import type {
  LoginRequest,
  LoginResponse,
  TenantValidationResponse,
  ActivateAccountRequest,
  ActivateAccountResponse,
  VerifyTenantRequest,
  VerifyTenantResponse,
  Tenant,
  User,
} from "@/types";

export const authService = {
  /**
   * Validate tenant by identifier (tenantId, slug, or company name)
   */
  async validateTenant(identifier: string): Promise<Tenant | null> {
    try {
      const response = await axiosInstance.get<TenantValidationResponse>(
        `/tenant/infoByIdentifier?identifier=${encodeURIComponent(identifier)}`
      );
      
      if (response.data.success && response.data.data) {
        return response.data.data;
      }
      
      return null;
    } catch (error: any) {
      // If 404, tenant not found
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  },

  /**
   * Login user with email, password and tenantId
   */
  async login(loginData: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await axiosInstance.post<LoginResponse>(
        "/tenant/login",
        loginData
      );
      console.log("Login response:", response.data);
      
      return response.data;
    } catch (error: any) {
      console.log("Login error:", error);
      throw new Error(error.response?.data?.message || "Login failed");
    }
  },

  /**
   * Activate tenant owner account
   */
  async activateAccount(activationData: ActivateAccountRequest): Promise<ActivateAccountResponse> {
    try {
      const response = await axiosInstance.post<ActivateAccountResponse>(
        "/tenant/activate",
        activationData
      );
      
      return response.data;
    } catch (error: any) {
      // Handle specific error cases
      if (error.response?.status === 400) {
        throw new Error("Invalid activation code or password");
      } else if (error.response?.status === 404) {
        throw new Error("Activation code not found or expired");
      } else if (error.response?.status === 409) {
        throw new Error("Account already activated");
      }
      
      throw new Error(error.response?.data?.message || "Account activation failed");
    }
  },

  /**
   * Verify inactive tenant with 6-digit code
   */
  async verifyTenant(verifyData: VerifyTenantRequest): Promise<Tenant> {
    try {
      const response = await axiosInstance.post<VerifyTenantResponse>(
        "/tenant/verify",
        verifyData
      );
      
      return response.data.data;
    } catch (error: any) {
      // Handle specific error cases
      if (error.response?.status === 400) {
        throw new Error("Invalid verification code");
      } else if (error.response?.status === 404) {
        throw new Error("Tenant not found or verification code expired");
      } else if (error.response?.status === 429) {
        throw new Error("Too many verification attempts. Please try again later");
      }
      
      throw new Error(error.response?.data?.message || "Tenant verification failed");
    }
  },

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string): Promise<LoginResponse> {
    try {
      const response = await axiosInstance.post<LoginResponse>(
        "/tenant/refresh",
        { refreshToken }
      );
      
      return response.data;
    } catch (error: any) {
      // Handle specific error cases
      if (error.response?.status === 401) {
        throw new Error("Invalid or expired refresh token");
      }
      
      throw new Error(error.response?.data?.message || "Token refresh failed");
    }
  },

  /**
   * Verify user OTP for account activation
   */
  async verifyUserOTP(otpData: { userId: string; otp: string }): Promise<any> {
    try {
      const response = await axiosInstance.post(
        "/auth/verify-user-otp",
        otpData
      );

      return response.data;
    } catch (error: any) {
      // Handle specific error cases
      if (error.response?.status === 400) {
        throw new Error("Invalid or expired OTP");
      } else if (error.response?.status === 404) {
        throw new Error("User not found");
      } else if (error.response?.status === 429) {
        throw new Error("Too many verification attempts. Please try again later");
      }

      throw new Error(error.response?.data?.message || "OTP verification failed");
    }
  },

  /**
   * Request password reset
   */
  async forgotPassword(email: string, tenantId: string): Promise<void> {
    await axiosInstance.post("/auth/forgot-password", { email, tenantId });
  },

  /**
   * Reset password with token
   */
  async resetPassword(token: string, password: string): Promise<void> {
    await axiosInstance.post("/auth/reset-password", { token, password });
  },

  /**
   * Check if token is expired or about to expire (within 5 minutes)
   */
  isTokenExpired(token: string | null): boolean {
    if (!token) return true;
    
    try {
      // Parse JWT payload
      const payload = JSON.parse(atob(token.split('.')[1]));
      const now = Math.floor(Date.now() / 1000);
      
      // Check if token expires within 5 minutes (300 seconds)
      return payload.exp && (payload.exp - now) < 300;
    } catch (error) {
      console.error('Error parsing token:', error);
      return true;
    }
  },

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    // Clear local storage
    localStorage.removeItem("user");
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("operatorMode");
  },

  /**
   * Resend user activation OTP
   */
  async resendUserActivationOTP(userId: string): Promise<any> {
    try {
      const response = await axiosInstance.post(
        "/v1/auth/resend-user-activation-otp",
        { userId }
      );
      return response.data;
    } catch (error: any) {
      throw error;
    }
  },

  /**
   * Get stored authentication data from localStorage
   */
  getStoredAuth(): {
    user: User | null;
    tenant: Tenant | null;
    accessToken: string | null;
    refreshToken: string | null;
  } {
    if (typeof window === "undefined") {
      return {
        user: null,
        tenant: null,
        accessToken: null,
        refreshToken: null,
      };
    }

    try {
      const user = localStorage.getItem("user");
      const tenant = localStorage.getItem("tenant");
      const accessToken = localStorage.getItem("accessToken");
      const refreshToken = localStorage.getItem("refreshToken");

      return {
        user: user ? JSON.parse(user) : null,
        tenant: tenant ? JSON.parse(tenant) : null,
        accessToken,
        refreshToken,
      };
    } catch (error) {
      console.error("Error parsing stored auth data:", error);
      return {
        user: null,
        tenant: null,
        accessToken: null,
        refreshToken: null,
      };
    }
  },

  /**
   * Store authentication data in localStorage
   */
  storeAuth(data: {
    user: User;
    tenant: Tenant;
    accessToken: string;
    refreshToken: string;
  }): void {
    if (typeof window === "undefined") return;

    localStorage.setItem("user", JSON.stringify(data.user));
    localStorage.setItem("tenant", JSON.stringify(data.tenant));
    localStorage.setItem("accessToken", data.accessToken);
    localStorage.setItem("refreshToken", data.refreshToken);
  },
};
