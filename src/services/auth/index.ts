// Export the new auth service
export * from './authService';

// Keep existing exports for backward compatibility
import { baseApi } from '../api';

export interface AuthResponse {
  token: string;
  user: any;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export const authApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    login: builder.mutation<AuthResponse, LoginRequest>({
      query: (credentials) => ({
        url: '/auth/login',
        method: 'POST',
        data: credentials,
      }),
    }),
    current: builder.query<AuthResponse, void>({
      query: () => ({
        url: '/auth/current',
        method: 'GET',
      }),
    }),
  }),
  overrideExisting: false,
});

export const { useLoginMutation, useCurrentQuery } = authApi;
