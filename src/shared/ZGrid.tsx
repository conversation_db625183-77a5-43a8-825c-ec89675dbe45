'use client'

import React, { useState, useMemo } from 'react'
import { ChevronUp, ChevronDown, Search, Filter, MoreHorizontal, ArrowUpDown, ArrowUp, ArrowDown, ChevronRight, Minus, Plus } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface ZGridColumn {
  field: string
  headerName: string
  width?: string | number
  sortable?: boolean
  filterable?: boolean
  type?: 'text' | 'number' | 'date' | 'boolean'
  cellRenderer?: (value: any, row: any, rowIndex: number) => React.ReactNode
  headerClassName?: string
  cellClassName?: string
  headerStyle?: React.CSSProperties
  cellStyle?: React.CSSProperties
  filterType?: 'text' | 'select' | 'date' | 'number'
  filterOptions?: Array<{ label: string; value: string }>
  // Nested data support
  isExpanderColumn?: boolean
  nestedField?: string // For accessing nested properties like 'address.city'
}

export interface ZGridNestedConfig {
  // Field that contains children array
  childrenField?: string
  // Custom columns for child rows (if not provided, will auto-generate from child data)
  childColumns?: ZGridColumn[]
  // Max depth to expand
  maxDepth?: number
  // Default expanded state
  defaultExpanded?: boolean
  // Custom child row styling
  childRowClassName?: string | ((row: any, level: number) => string)
  childRowStyle?: React.CSSProperties | ((row: any, level: number) => React.CSSProperties)
  // Custom expand/collapse icons
  expandIcon?: React.ReactNode
  collapseIcon?: React.ReactNode
  // Indent per level (in pixels)
  indentSize?: number
}

export interface ZGridProps {
  columns: ZGridColumn[]
  data: any[]
  pagination?: boolean
  pageSize?: number
  className?: string
  onRowClick?: (row: any, index: number) => void
  onRowDoubleClick?: (row: any, index: number) => void
  loading?: boolean
  height?: string
  showSearch?: boolean
  showFilters?: boolean
  selectable?: boolean
  selectedItems?: string[]
  onSelectionChange?: (selectedIds: string[]) => void
  emptyState?: {
    title: string
    description: string
    action?: {
      label: string
      onClick: () => void
      icon?: React.ReactNode
    }
  }
  bulkActions?: React.ReactNode
  // Styling props
  headerClassName?: string
  headerStyle?: React.CSSProperties
  rowClassName?: string | ((row: any, index: number) => string)
  rowStyle?: React.CSSProperties | ((row: any, index: number) => React.CSSProperties)
  tableClassName?: string
  searchClassName?: string
  paginationClassName?: string
  // Filter options
  filterPosition?: 'header' | 'below' | 'popup'
  sortIcons?: 'arrows' | 'chevrons'
  // Nested/Hierarchical data support
  nested?: ZGridNestedConfig
}

export const ZGrid: React.FC<ZGridProps> = ({
  columns,
  data,
  pagination = true,
  pageSize = 10,
  className = '',
  onRowClick,
  onRowDoubleClick,
  loading = false,
  height,
  showSearch = true,
  showFilters = true,
  selectable = false,
  selectedItems = [],
  onSelectionChange,
  emptyState,
  bulkActions,
  // Styling props
  headerClassName = '',
  headerStyle = {},
  rowClassName = '',
  rowStyle = {},
  tableClassName = '',
  searchClassName = '',
  paginationClassName = '',
  // Filter options
  filterPosition = 'below',
  sortIcons = 'arrows',
  // Nested support
  nested
}) => {
  const [sortConfig, setSortConfig] = useState<{ field: string; direction: 'asc' | 'desc' } | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [filters, setFilters] = useState<Record<string, string>>({})
  const [globalFilter, setGlobalFilter] = useState('')
  const [showColumnFilters, setShowColumnFilters] = useState(false)
  // Nested data state
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set())

  // Helper function to get nested value from object
  const getNestedValue = (obj: any, path: string) => {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  // Helper function to generate child columns automatically
  const generateChildColumns = (childData: any[]): ZGridColumn[] => {
    if (!childData || childData.length === 0) return []
    
    const firstChild = childData[0]
    const columns: ZGridColumn[] = []
    
    Object.keys(firstChild).forEach(key => {
      if (key !== 'id' && typeof firstChild[key] !== 'object') {
        columns.push({
          field: key,
          headerName: key.charAt(0).toUpperCase() + key.slice(1),
          sortable: true,
          filterable: true,
          width: '150px'
        })
      }
    })
    
    return columns
  }

  // Helper function to flatten nested data for display
  const flattenNestedData = (data: any[], level: number = 0, parentId: string = ''): any[] => {
    if (!nested) return data
    
    const flattened: any[] = []
    const childrenField = nested.childrenField || 'children'
    const maxDepth = nested.maxDepth || 10
    
    data.forEach((item, index) => {
      const itemId = item.id || `${parentId}-${index}`
      
      // Add main row with metadata
      flattened.push({
        ...item,
        _level: level,
        _parentId: parentId,
        _itemId: itemId,
        _hasChildren: Boolean(item[childrenField] && item[childrenField].length > 0),
        _isExpanded: expandedRows.has(itemId),
        _isChildRow: level > 0
      })
      
      // Add children if expanded and within depth limit
      if (item[childrenField] && expandedRows.has(itemId) && level < maxDepth) {
        // Use custom child columns or auto-generate
        const childColumns = nested.childColumns || generateChildColumns(item[childrenField])
        
        // Process each child
        item[childrenField].forEach((child: any, childIndex: number) => {
          const childId = child.id || `${itemId}-child-${childIndex}`
          
          // Create child row data - preserve all child fields
          const childRowData: any = {
            ...child, // Copy all child properties first
            _level: level + 1,
            _parentId: itemId,
            _itemId: childId,
            _hasChildren: Boolean(child[childrenField] && child[childrenField].length > 0),
            _isExpanded: expandedRows.has(childId),
            _isChildRow: true,
            _originalData: child // Keep reference to original child data
          }
          
          flattened.push(childRowData)
          
          // Handle nested children recursively (for products in orders example)
          if (child[childrenField] && expandedRows.has(childId) && level + 1 < maxDepth) {
            const nestedChildren = flattenNestedData([child], level + 1, childId)
            // Add only the child rows, skip the parent as we already added it
            flattened.push(...nestedChildren.slice(1))
          }
        })
      }
    })
    
    return flattened
  }

  // Toggle row expansion
  const toggleRowExpansion = (itemId: string) => {
    const newExpanded = new Set(expandedRows)
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId)
    } else {
      newExpanded.add(itemId)
    }
    setExpandedRows(newExpanded)
  }

  // Handle selection with nested support
  const handleSelectItem = (itemId: string) => {
    const newSelection = selectedItems.includes(itemId) 
      ? selectedItems.filter(id => id !== itemId)
      : [...selectedItems, itemId]
    onSelectionChange?.(newSelection)
  }

  const handleSelectAll = () => {
    const flatData = flattenNestedData(data)
    const allIds = flatData.map(item => item._itemId || item.id || item.key || item.value)
    const newSelection = selectedItems.length === allIds.length ? [] : allIds
    onSelectionChange?.(newSelection)
  }

  // Get sort icon based on sortIcons prop
  const getSortIcon = (column: ZGridColumn) => {
    if (!column.sortable) return null
    
    const isActive = sortConfig?.field === column.field
    const direction = sortConfig?.direction
    
    if (sortIcons === 'chevrons') {
      return (
        <div className="flex flex-col ml-2">
          <ChevronUp 
            className={cn(
              "w-3 h-3",
              isActive && direction === 'asc'
                ? 'text-foreground'
                : 'text-muted-foreground/50'
            )}
          />
          <ChevronDown 
            className={cn(
              "w-3 h-3 -mt-1",
              isActive && direction === 'desc'
                ? 'text-foreground'
                : 'text-muted-foreground/50'
            )}
          />
        </div>
      )
    }
    
    // Default arrows
    if (!isActive) {
      return <ArrowUpDown className="w-4 h-4 ml-2 text-muted-foreground/50" />
    }
    
    return direction === 'asc' 
      ? <ArrowUp className="w-4 h-4 ml-2 text-foreground" />
      : <ArrowDown className="w-4 h-4 ml-2 text-foreground" />
  }

  // Render column filter
  const renderColumnFilter = (column: ZGridColumn) => {
    if (!column.filterable) return null

    if (column.filterType === 'select' && column.filterOptions) {
      return (
        <select
          value={filters[column.field] || ''}
          onChange={(e) => handleFilterChange(column.field, e.target.value)}
          className="w-full px-2 py-1 text-xs border border-input rounded bg-background focus:ring-1 focus:ring-ring"
        >
          <option value="">All</option>
          {column.filterOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      )
    }

    const inputType = column.filterType === 'number' ? 'number' : 
                     column.filterType === 'date' ? 'date' : 'text'

    return (
      <input
        type={inputType}
        placeholder={`Filter...`}
        value={filters[column.field] || ''}
        onChange={(e) => handleFilterChange(column.field, e.target.value)}
        className="w-full px-2 py-1 text-xs border border-input rounded bg-background focus:ring-1 focus:ring-ring"
      />
    )
  }

  // Apply filters and search with nested support
  const filteredData = useMemo(() => {
    const flatData = nested ? flattenNestedData(data) : data
    let filtered = [...flatData]

    // Apply column filters
    Object.entries(filters).forEach(([field, value]) => {
      if (value) {
        filtered = filtered.filter(row => {
          const fieldValue = field.includes('.') ? getNestedValue(row, field) : row[field]
          return String(fieldValue).toLowerCase().includes(value.toLowerCase())
        })
      }
    })

    // Apply global search
    if (globalFilter) {
      filtered = filtered.filter(row =>
        columns.some(col => {
          const fieldValue = col.nestedField 
            ? getNestedValue(row, col.nestedField)
            : col.field.includes('.')
              ? getNestedValue(row, col.field)
              : row[col.field]
          return String(fieldValue).toLowerCase().includes(globalFilter.toLowerCase())
        })
      )
    }

    return filtered
  }, [data, filters, globalFilter, columns, nested, expandedRows])

  // Apply sorting
  const sortedData = useMemo(() => {
    if (!sortConfig) return filteredData

    return [...filteredData].sort((a, b) => {
      const aValue = a[sortConfig.field]
      const bValue = b[sortConfig.field]

      if (aValue === bValue) return 0

      const comparison = aValue > bValue ? 1 : -1
      return sortConfig.direction === 'desc' ? -comparison : comparison
    })
  }, [filteredData, sortConfig])

  // Pagination
  const paginatedData = useMemo(() => {
    if (!pagination) return sortedData

    const startIndex = (currentPage - 1) * pageSize
    return sortedData.slice(startIndex, startIndex + pageSize)
  }, [sortedData, currentPage, pageSize, pagination])

  const totalPages = Math.ceil(sortedData.length / pageSize)

  const handleSort = (field: string) => {
    const column = columns.find(col => col.field === field)
    if (!column?.sortable) return

    setSortConfig(prev => ({
      field,
      direction: prev?.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }))
  }

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({ ...prev, [field]: value }))
    setCurrentPage(1)
  }

  const renderCell = (column: ZGridColumn, row: any, rowIndex: number) => {
    // Handle expander column for nested data
    if (column.isExpanderColumn && nested) {
      const level = row._level || 0
      const hasChildren = row._hasChildren
      const isExpanded = row._isExpanded
      const indentSize = nested.indentSize || 20
      
      return (
        <div 
          className="flex items-center" 
          style={{ paddingLeft: `${level * indentSize}px` }}
        >
          {hasChildren ? (
            <button
              onClick={(e) => {
                e.stopPropagation()
                toggleRowExpansion(row._itemId)
              }}
              className="p-1 hover:bg-muted rounded mr-2 transition-colors"
            >
              {isExpanded 
                ? (nested.collapseIcon || <Minus className="w-4 h-4" />)
                : (nested.expandIcon || <Plus className="w-4 h-4" />)
              }
            </button>
          ) : (
            <div className="w-6 h-6 mr-2" /> // Spacer for alignment
          )}
          <span>
            {getCellValue(column, row)}
          </span>
        </div>
      )
    }

    // Regular cell rendering
    const value = getCellValue(column, row)
    
    if (column.cellRenderer) {
      return column.cellRenderer(value, row, rowIndex)
    }

    return value !== null && value !== undefined ? value : '-'
  }

  // Helper function to get cell value
  const getCellValue = (column: ZGridColumn, row: any) => {
    // Handle nested field paths (e.g., 'address.city')
    if (column.field.includes('.')) {
      return getNestedValue(row, column.field)
    }
    
    // Direct field access
    const value = row[column.field]
    
    // Return the value or null if undefined
    return value !== undefined ? value : null
  }

  // Get row styling with nested support
  const getRowClassName = (row: any, index: number) => {
    let className = ''
    
    if (typeof rowClassName === 'function') {
      className = rowClassName(row, index)
    } else {
      className = rowClassName || ''
    }
    
    // Add nested styling if applicable
    if (nested && row._isChildRow) {
      const level = row._level || 0
      
      if (nested.childRowClassName) {
        if (typeof nested.childRowClassName === 'function') {
          className += ` ${nested.childRowClassName(row, level)}`
        } else {
          className += ` ${nested.childRowClassName}`
        }
      } else {
        // Default nested styling
        className += ` bg-muted/30`
      }
    }
    
    return className
  }

  const getRowStyle = (row: any, index: number) => {
    let style = {}
    
    if (typeof rowStyle === 'function') {
      style = rowStyle(row, index)
    } else if (rowStyle) {
      style = rowStyle
    }
    
    // Add nested styling if applicable
    if (nested && row._isChildRow && nested.childRowStyle) {
      const level = row._level || 0
      
      if (typeof nested.childRowStyle === 'function') {
        style = { ...style, ...nested.childRowStyle(row, level) }
      } else {
        style = { ...style, ...nested.childRowStyle }
      }
    }
    
    return style
  }

  if (loading) {
    return (
      <div className={cn("border rounded-lg bg-card", className)} style={{ height }}>
        <div className="flex items-center justify-center h-full min-h-[200px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("rounded-lg border bg-card", className)}>
      {/* Header with search and bulk actions */}
      {(showSearch || bulkActions || showFilters) && (
        <div className={cn("p-4 border-b bg-muted/30", searchClassName)}>
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-4 flex-1">
              {showSearch && (
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search..."
                    value={globalFilter}
                    onChange={(e) => setGlobalFilter(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 text-sm border border-input rounded-md bg-background focus:ring-2 focus:ring-ring focus:border-ring"
                  />
                </div>
              )}
              {showFilters && filterPosition === 'header' && (
                <button
                  onClick={() => setShowColumnFilters(!showColumnFilters)}
                  className={cn(
                    "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3",
                    showColumnFilters && "bg-accent text-accent-foreground"
                  )}
                >
                  <Filter className="w-4 h-4 mr-2" />
                  Filters
                </button>
              )}
            </div>
            {bulkActions && selectedItems.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">
                  {selectedItems.length} selected
                </span>
                {bulkActions}
              </div>
            )}
          </div>
          
          {/* Column Filters in Header */}
          {showFilters && filterPosition === 'header' && showColumnFilters && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {columns.filter(col => col.filterable).map((column) => (
                <div key={`header-filter-${column.field}`} className="space-y-1">
                  <label className="text-xs font-medium text-muted-foreground">
                    {column.headerName}
                  </label>
                  {renderColumnFilter(column)}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Table Container - Enhanced Responsive */}
      <div className={cn("relative overflow-auto border rounded-md", height && `max-h-[${height}]`)}>
        <div className="min-w-full inline-block align-middle">
          <table className={cn("w-full table-auto", tableClassName)}>
            <thead className={cn("sticky top-0 z-10 border-b bg-muted/50 backdrop-blur-sm", headerClassName)} style={headerStyle}>
              <tr>
                {selectable && (
                  <th className="h-12 px-2 sm:px-4 text-left align-middle font-medium text-muted-foreground w-8 sm:w-12">
                    <input
                      type="checkbox"
                    className="rounded border border-input"
                    checked={selectedItems.length === data.length && data.length > 0}
                    onChange={handleSelectAll}
                  />
                </th>
              )}
              {columns.map((column) => (
                <th
                  key={column.field}
                  className={cn(
                    "h-12 px-2 sm:px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 text-xs sm:text-sm",
                    column.sortable && "cursor-pointer hover:text-foreground",
                    column.headerClassName
                  )}
                  style={{ 
                    width: column.width, 
                    minWidth: typeof column.width === 'string' && column.width.includes('px') 
                      ? `${Math.max(80, parseInt(column.width))}px` 
                      : '80px',
                    ...column.headerStyle 
                  }}
                  onClick={() => column.sortable && handleSort(column.field)}
                >
                  <div className="flex items-center gap-1">
                    <span className="truncate">{column.headerName}</span>
                    {getSortIcon(column)}
                  </div>
                </th>
              ))}
            </tr>
            {/* Filter Row Below Headers */}
            {showFilters && filterPosition === 'below' && (
              <tr className="border-b">
                {selectable && <th className="h-10 px-4"></th>}
                {columns.map((column) => (
                  <th key={`filter-${column.field}`} className="h-10 px-4">
                    {renderColumnFilter(column)}
                  </th>
                ))}
              </tr>
            )}
          </thead>
          <tbody className="[&_tr:last-child]:border-0">
            {paginatedData.length === 0 ? (
              <tr>
                <td colSpan={columns.length + (selectable ? 1 : 0)} className="h-24 text-center">
                  <div className="flex flex-col items-center justify-center space-y-2 text-muted-foreground">
                    <div className="text-sm font-medium">
                      {emptyState?.title || "No data found"}
                    </div>
                    <div className="text-xs">
                      {emptyState?.description || "There are no items to display."}
                    </div>
                    {emptyState?.action && (
                      <button
                        onClick={emptyState.action.onClick}
                        className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-8 px-3 mt-2"
                      >
                        {emptyState.action.icon}
                        {emptyState.action.label}
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ) : (
              paginatedData.map((row, rowIndex) => {
                const itemId = row._itemId || row.id || row.key || row.value || rowIndex
                return (
                  <tr
                    key={itemId}
                    className={cn(
                      "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",
                      (onRowClick || onRowDoubleClick) && "cursor-pointer",
                      getRowClassName(row, rowIndex)
                    )}
                    style={getRowStyle(row, rowIndex)}
                    onClick={() => onRowClick?.(row, rowIndex)}
                    onDoubleClick={() => onRowDoubleClick?.(row, rowIndex)}
                  >
                    {selectable && (
                      <td className="p-2 sm:p-4 align-middle w-8 sm:w-12">
                        <input
                          type="checkbox"
                          className="rounded border border-input"
                          checked={selectedItems.includes(itemId)}
                          onChange={() => handleSelectItem(itemId)}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </td>
                    )}
                    {columns.map((column) => (
                      <td
                        key={column.field}
                        className={cn(
                          "p-2 sm:p-4 align-middle [&:has([role=checkbox])]:pr-0 text-xs sm:text-sm",
                          column.cellClassName
                        )}
                        style={{ 
                          width: column.width,
                          minWidth: typeof column.width === 'string' && column.width.includes('px') 
                            ? `${Math.max(60, parseInt(column.width) * 0.7)}px` 
                            : '60px',
                          ...column.cellStyle 
                        }}
                      >
                        <div className="truncate max-w-full">
                          {renderCell(column, row, rowIndex)}
                        </div>
                      </td>
                    ))}
                  </tr>
                )
              })
            )}
          </tbody>
        </table>
        </div>
      </div>

      {/* Pagination - Enhanced Responsive */}
      {pagination && totalPages > 1 && (
        <div className={cn("flex flex-col sm:flex-row items-center justify-between gap-2 px-2 sm:px-4 py-3 border-t bg-muted/30", paginationClassName)}>
          <div className="text-xs sm:text-sm text-muted-foreground order-2 sm:order-1">
            <span className="hidden sm:inline">Showing </span>
            {((currentPage - 1) * pageSize) + 1}-{Math.min(currentPage * pageSize, sortedData.length)} of {sortedData.length}
            <span className="hidden sm:inline"> results</span>
          </div>
          <div className="flex space-x-1 order-1 sm:order-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="inline-flex items-center justify-center rounded-md text-xs sm:text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-8 px-2 sm:px-3"
            >
              <span className="hidden sm:inline">Previous</span>
              <span className="sm:hidden">‹</span>
            </button>
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let page: number
              if (totalPages <= 5) {
                page = i + 1
              } else if (currentPage <= 3) {
                page = i + 1
              } else if (currentPage >= totalPages - 2) {
                page = totalPages - 4 + i
              } else {
                page = currentPage - 2 + i
              }
              
              return (
                <button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={cn(
                    "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 h-8 px-3",
                    currentPage === page
                      ? "bg-primary text-primary-foreground hover:bg-primary/90"
                      : "border border-input bg-background hover:bg-accent hover:text-accent-foreground"
                  )}
                >
                  {page}
                </button>
              )
            })}
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="inline-flex items-center justify-center rounded-md text-xs sm:text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-8 px-2 sm:px-3"
            >
              <span className="hidden sm:inline">Next</span>
              <span className="sm:hidden">›</span>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default ZGrid
