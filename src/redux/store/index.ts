import { configureStore } from "@reduxjs/toolkit"
import { useDispatch, useSelector, TypedUseSelectorHook } from "react-redux"

// Reducers / APIs
import { counterReducer, authReducer, cannedResponseApi, brandingApi, departmentsApi, usersApi, rolesApi, statusesApi } from "../slices"
import { baseApi } from "../../services/api"

export const store = configureStore({
  reducer: {
    counter: counterReducer,
    auth: authReducer,
    // RTK Query reducers
    [cannedResponseApi.reducerPath]: cannedResponseApi.reducer,
    [brandingApi.reducerPath]: brandingApi.reducer,
    [departmentsApi.reducerPath]: departmentsApi.reducer,
    [usersApi.reducerPath]: usersApi.reducer,
    [rolesApi.reducerPath]: rolesApi.reducer,
    [statusesApi.reducerPath]: statusesApi.reducer,
    [baseApi.reducerPath]: baseApi.reducer,

    // add more reducers here
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      // RTK Query middlewares
      cannedResponseApi.middleware,
      brandingApi.middleware,
      departmentsApi.middleware,
      usersApi.middleware,
      rolesApi.middleware,
      statusesApi.middleware,
      baseApi.middleware,
    ),
})

// Types
export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

// Typed hooks
export const useAppDispatch: () => AppDispatch = useDispatch
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector
