import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '@/lib/rtkAxiosBaseQuery';
import { API_BASE_URL } from '@/services/api';

// Department types based on actual API response
export type Department = {
  id?: string;
  _id?: string;
  name: string;
  description?: string;
  status: 'active' | 'inactive';
  parentId?: string;
  memberCount: number;
  tenantId?: string;
  createdBy?: string | null;
  createdAt?: string;
  updatedAt?: string;
  additionalInfo?: {
    email?: string;
    phone?: string;
    manager?: string;
    location?: string;
    budget?: number;
    notes?: string;
  };
};

export type CreateDepartmentRequest = {
  name: string;
  description?: string;
  status?: 'active' | 'inactive';
  parentId?: string;
  additionalInfo?: {
    email?: string;
    phone?: string;
    manager?: string;
    location?: string;
    budget?: number;
    notes?: string;
  };
};

export type UpdateDepartmentRequest = Partial<CreateDepartmentRequest>;

export const departmentsApi = createApi({
  reducerPath: 'departmentsApi',
  tagTypes: ['Department'],
  baseQuery: axiosBaseQuery({
    baseUrl: `${API_BASE_URL}/departments`,
  }),
  endpoints: (builder) => ({
    getDepartments: builder.query<Department[], void>({
      query: () => ({ url: '', method: 'GET' }),
      providesTags: ['Department'],
      transformResponse: (response: any) => {
        console.log('Departments API raw response:', response);
        let data: any[] = [];
        if (Array.isArray(response)) {
          data = response;
        } else if (Array.isArray(response?.data?.departments)) {
          data = response.data.departments;
        } else if (Array.isArray(response?.data)) {
          data = response.data;
        } else if (Array.isArray(response?.departments)) {
          data = response.departments;
        } else if (Array.isArray(response?.data?.data)) {
          data = response.data.data;
        }

        return data.map((item: any) => ({
          id: item._id || item.id || item.departmentId,
          name: item.name,
          description: item.description,
          status: item.isActive ? 'active' : 'inactive',
          parentId: item.parentId,
          memberCount: item.memberCount || 0, // Default to 0 if not provided
          tenantId: item.tenantId,
          createdBy: item.createdBy,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
        }));
      },
    }),

    getDepartmentById: builder.query<Department, string>({
      query: (id) => ({ url: `/${id}`, method: 'GET' }),
      providesTags: (result, error, id) => [{ type: 'Department', id }],
      transformResponse: (response: any) => {
        console.log('Department by ID API raw response:', response);
        const departmentData = response?.data?.department || response?.department || response;
        
        return {
          id: departmentData._id || departmentData.id || departmentData.departmentId,
          name: departmentData.name,
          description: departmentData.description,
          status: departmentData.isActive ? 'active' : 'inactive',
          parentId: departmentData.parentId,
          memberCount: departmentData.memberCount || 0, // Default to 0 if not provided
          tenantId: departmentData.tenantId,
          createdBy: departmentData.createdBy,
          createdAt: departmentData.createdAt,
          updatedAt: departmentData.updatedAt,
          additionalInfo: departmentData.additionalInfo
        };
      },
    }),

    createDepartment: builder.mutation<Department, CreateDepartmentRequest>({
      query: (data) => ({ url: '', method: 'POST', data }),
      invalidatesTags: ['Department'],
    }),

    updateDepartment: builder.mutation<Department, { id: string; data: UpdateDepartmentRequest }>({
      query: ({ id, data }) => {
        if (!id || id === 'undefined') throw new Error('Invalid ID provided for update');
        return { url: `/${id}`, method: 'PUT', data };
      },
      invalidatesTags: (result, error, { id }) => [{ type: 'Department', id }, 'Department'],
    }),

    deleteDepartment: builder.mutation<void, string>({
      query: (id) => {
        if (!id || id === 'undefined') throw new Error('Invalid ID provided for delete');
        return { url: `/${id}`, method: 'DELETE' };
      },
      invalidatesTags: ['Department'],
    }),
  }),
});

export const {
  useGetDepartmentsQuery,
  useGetDepartmentByIdQuery,
  useCreateDepartmentMutation,
  useUpdateDepartmentMutation,
  useDeleteDepartmentMutation,
} = departmentsApi;


