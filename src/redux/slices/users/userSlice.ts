import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '@/lib/rtkAxiosBaseQuery';
import { API_BASE_URL } from '@/services/api';

// User types based on demo types
export type User = {
  id: string;
  name: string;
  email: string;
  role: string | Role;
  department: Department;
  status: 'active' | 'inactive' | 'pending_activation' | 'suspended';
  avatar?: string;
  lastLogin?: string;
  createdAt?: string;
  updatedAt?: string;
  phone?: string;
  tenantId?: string;
  userType?: any;


  additionalInfo?: {
    phone?: string;
    location?: string;
    notes?: string;
    userType?: string;
  };
};

export type Role = {
  id?: string;
  _id?: string;
  name: string;
  description?: string;
  type: 'system' | 'custom';
  permissions: {
    tickets: { read: boolean; write: boolean; delete: boolean };
    users: { read: boolean; write: boolean; delete: boolean };
    settings: { read: boolean; write: boolean; delete: boolean };
    reports: { read: boolean; write: boolean; delete: boolean };
  };
  userCount: number;
};

export type Department = {
  id?: string;
  _id?: string;
  name: string;
  description?: string;
  status: 'active' | 'inactive';
  parentId?: string;
  memberCount: number;
  additionalInfo?: {
    email?: string;
    phone?: string;
    manager?: string;
    location?: string;
    budget?: number;
    notes?: string;
  };
};

export type CreateUserRequest = {
  name: string;
  email: string;
  roleId: string;
  departmentId: string;
  status?: 'active' | 'inactive';
  phone?: string;
  location?: string;
  userType?: string;

  additionalInfo?: {
    phone?: string;
    location?: string;
    notes?: string;
  };
};

export type UpdateUserRequest = {
  name?: string;
  email?: string;
  role?: string;
  departments?: string[];
  status?: 'active' | 'inactive' | 'pending_activation' | 'suspended';
  tenantId?: string;
  additionalInfo?: {
    phone?: string;
    location?: string;
    notes?: string;
  };
  phone?: string;
  location?: string;
  notes?: string;
  userType?: string;
};

export type InviteUserRequest = {
  email: string;
  name?: string;
  phone?: string;
  location?: string;
  role?: string;
  departments?: string[];
  userType?: string;
};

export const usersApi = createApi({
  reducerPath: 'usersApi',
  tagTypes: ['User'],
  baseQuery: axiosBaseQuery({
    baseUrl: `${API_BASE_URL}/users`,
  }),
  endpoints: (builder) => ({
    getUsers: builder.query<User[], void>({
      query: () => ({ url: '', method: 'GET' }),
      providesTags: ['User'],
      transformResponse: (response: any) => {
        console.log('Users API raw response:', response);
        let data: any[] = [];
        if (Array.isArray(response)) {
          data = response;
        } else if (Array.isArray(response?.data?.users
        )) {
          data = response.data?.users;
        }
        return data?.map((item: any) => {
          const userTypeMap: Record<number, string> = {
            0: "Self Service",
            1: "Operator",
            2: "Self Service, Operator",
          };
        
          return {
            id: item._id || item.id || item.userId,
            name: item.name,
            email: item.email,
            phone: item.phone,
            role: item.role || { id: '', name: 'No Role', type: 'custom', permissions: {}, userCount: 0 },
            department: item.departments?.[0] || item.department || { id: '', name: 'No Department', status: 'active', memberCount: 0 },
            status: item.status,
            avatar: item.avatar,
            lastLogin: item.lastLogin,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
            tenantId: item.tenantId,
            userType: userTypeMap[item.userType] || "Unknown",
          };
        });
        

      },

    }),


    getUserById: builder.query<User, { id: string; tenantId?: string }>({
      query: ({ id, tenantId }) => {
        const config: any = {
          url: `/${id}`,
          method: 'GET',
        };
    
        if (tenantId) {
          config.params = { tenantId };
        }
    
        return config;
      },
    
      providesTags: (result, error, { id }) => [{ type: 'User', id }],
    
      transformResponse: (response: any) => {
        console.log('User by ID API raw response:', response);
    
        const userData = response?.data?.user || response?.user || response;
    
        return {
          id: userData._id || userData.id,
          name: userData.name || '',
          email: userData.email,
          role: userData.role,
          phone: userData.phone,
          department: userData.departments?.[0] || userData.department || { id: '', name: 'No Department', status: 'active', memberCount: 0 },
          status: userData.status,
          avatar: userData.avatar,
          lastLogin: userData.lastLogin,
          createdAt: userData.createdAt,
          updatedAt: userData.updatedAt,
          userType: userData.userType,
          tenantId: userData.tenantId,
        };
      },
    }),
    
    createUser: builder.mutation<User, CreateUserRequest>({
      query: (data) => ({ url: '', method: 'POST', data }),
      invalidatesTags: ['User'],
    }),

    updateUser: builder.mutation<User, { id: string; tenantId: string; data: UpdateUserRequest }>({
      query: ({ id, tenantId, data }) => {
        if (!id || id === 'undefined') throw new Error('Invalid ID provided for update');
        
        return { 
          url: `/${id}`, 
          method: 'PUT', 
          data: { ...data, tenantId }
        };
      },
      invalidatesTags: (result, error, { id }) => [{ type: 'User', id }, 'User'],
    }),

    deleteUser: builder.mutation<void, { id: string; tenantId: string }>({
      query: ({ id, tenantId }) => {
        if (!id || id === 'undefined') throw new Error('Invalid ID provided for delete');
        
        return { 
          url: `/${id}`, 
          method: 'DELETE',
          data: { tenantId }
        };
      },
      invalidatesTags: ['User'],
    }),

    inviteUser: builder.mutation<User, InviteUserRequest>({
      query: (data) => ({ url: '', method: 'POST', data }),
      invalidatesTags: ['User'],
    }),

    resetPassword: builder.mutation<{ success: boolean; data: { newPassword: string; message: string } }, { id: string; tenantId: string }>({
      query: ({ id, tenantId }) => ({
        url: `/${id}/reset-password`,
        method: 'POST',
        data: { tenantId }
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'User', id }],
    }),
  }),
});

export const {
  useGetUsersQuery,
  useGetUserByIdQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useInviteUserMutation,
  useResetPasswordMutation,
} = usersApi;
