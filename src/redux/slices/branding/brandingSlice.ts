import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '@/lib/rtkAxiosBaseQuery';
import type {
  BrandingItem,
  BrandingListResponse,
  CreateUpdateBrandingRequest,
  BrandingApiResponse,
  BrandingFilters
} from '@/types/branding';
import { API_BASE_URL } from '@/services/api';

export const brandingApi = createApi({
  reducerPath: 'brandingApi',
  tagTypes: ['Branding'],
  baseQuery: axiosBaseQuery({
    baseUrl: `${API_BASE_URL}`,
  }),
  endpoints: (builder) => ({
    // Get all branding for a tenant
    getBrandingList: builder.query<BrandingListResponse, BrandingFilters>({
      query: ({ tenantId, page = 1, limit = 10 }) => ({
        url: `/branding/all?tenantId=${tenantId}&page=${page}&limit=${limit}`,
        method: 'GET',
      }),
      providesTags: ['Branding'],
      transformResponse: (response: BrandingListResponse) => {
        return response;
      },
    }),

    // Get branding by ID
    getBrandingById: builder.query<BrandingItem, string>({
      query: (id) => ({
        url: `/branding/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'Branding', id }],
    }),

    // Create branding
    createBranding: builder.mutation<BrandingApiResponse, CreateUpdateBrandingRequest>({
      query: (data) => {
        const formData = new FormData();

        // Append form data
        formData.append('tenantId', data.tenantId);
        formData.append('customColors', data.customColors.toString());
        formData.append('colors', data.colors);
        formData.append('tagline', data.tagline);
        formData.append('description', data.description);
        formData.append('featureHighlights', JSON.stringify(data.featureHighlights));

        // Append image if provided
        if (data.image) {
          formData.append('image', data.image);
        }

        return {
          url: '/branding/0',
          method: 'POST',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        };
      },
      invalidatesTags: ['Branding'],
      async onQueryStarted(arg, { queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          // Update localStorage with the new branding data
          if (data?.data?.branding) {
            const branding = data.data.branding;
            const localStorageData = {
              brandingId: branding._id,
              tagline: branding.tagline,
              description: branding.description,
              url: branding.url,
              customColors: branding.customColors,
              colors: typeof branding.colors === 'string' 
                ? (branding.colors ? JSON.parse(branding.colors) : {})
                : branding.colors
            };
            localStorage.setItem('tickflo-branding', JSON.stringify(localStorageData));
          }
        } catch (error) {
          console.error('Failed to update localStorage after creating branding:', error);
        }
      },
    }),

    // Update branding
    updateBranding: builder.mutation<
      BrandingApiResponse,
      { id: string; data: CreateUpdateBrandingRequest }
    >({
      query: ({ id, data }) => {
        const formData = new FormData();

        // Append form data
        formData.append('tenantId', data.tenantId);
        formData.append('customColors', data.customColors.toString());
        formData.append('colors', data.colors);
        formData.append('tagline', data.tagline);
        formData.append('description', data.description);
        formData.append('featureHighlights', JSON.stringify(data.featureHighlights));

        // Append image if provided
        if (data.image) {
          formData.append('image', data.image);
        }

        return {
          url: `/branding/${id}`,
          method: 'POST',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        };
      },
      invalidatesTags: (result, error, { id }) => [
        { type: 'Branding', id },
        'Branding',
      ],
      async onQueryStarted(arg, { queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          // Update localStorage with the updated branding data
          if (data?.data?.branding) {
            const branding = data.data.branding;
            const localStorageData = {
              brandingId: branding._id,
              tagline: branding.tagline,
              description: branding.description,
              url: branding.url,
              customColors: branding.customColors,
              colors: typeof branding.colors === 'string' 
                ? (branding.colors ? JSON.parse(branding.colors) : {})
                : branding.colors
            };
            localStorage.setItem('tickflo-branding', JSON.stringify(localStorageData));
          }
        } catch (error) {
          console.error('Failed to update localStorage after updating branding:', error);
        }
      },
    }),

    // Delete branding
    deleteBranding: builder.mutation<BrandingApiResponse, { id: string; tenantId?: string }>({
      query: ({ id, tenantId }) => {
        const url = tenantId ? `/branding/${id}?tenantId=${tenantId}` : `/branding/${id}`;
        return {
          url,
          method: 'DELETE',
        };
      },
      invalidatesTags: ['Branding'],
    }),
  }),
});

export const {
  useGetBrandingListQuery,
  useGetBrandingByIdQuery,
  useCreateBrandingMutation,
  useUpdateBrandingMutation,
  useDeleteBrandingMutation,
} = brandingApi;
