import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '@/lib/rtkAxiosBaseQuery';
import { API_BASE_URL } from '@/services/api';

// Status type based on API response
export type Status = {
  _id: string;
  tenantId: string;
  name: string;
  color: string;
};

export const statusesApi = createApi({
  reducerPath: 'statusesApi',
  tagTypes: ['Status'],
  baseQuery: axiosBaseQuery({
    baseUrl: `${API_BASE_URL}`,
  }),
  endpoints: (builder) => ({
    getStatuses: builder.query<Status[], string>({
      query: (tenantId) => ({ url: `/statuses?tenantId=${tenantId}`, method: 'GET' }),
      providesTags: ['Status'],
      transformResponse: (response: any) => {
        console.log('Statuses API raw response:', response);
        if (response.success && Array.isArray(response.data)) {
          return response.data.map((item: any) => ({
            _id: item._id,
            tenantId: item.tenantId,
            name: item.name,
            color: item.color,
          }));
        }
        return [];
      },
    }),
  }),
});

export const { useGetStatusesQuery } = statusesApi;