import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import type { AuthState, LoginRequest, User, Tenant, ActivateAccountRequest, VerifyTenantRequest } from "@/types";
import { authService } from "@/services/auth/authService";

// Initial state
const initialState: AuthState = {
  user: null,
  tenant: null,
  tokens: {
    accessToken: null,
    refreshToken: null,
  },
  loading: false,
  error: null,
  isAuthenticated: false,
  mfaRequired: false,
};

// Async thunks
export const validateTenant = createAsyncThunk(
  "auth/validateTenant",
  async (identifier: string, { rejectWithValue }) => {
    try {
      const tenant = await authService.validateTenant(identifier);
      return tenant;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to validate tenant");
    }
  }
);

export const loginUser = createAsyncThunk(
  "auth/loginUser",
  async (loginData: LoginRequest, { rejectWithValue }) => {
    try {
      const response = await authService.login(loginData);
      
      // Check if MFA is required
      if (response.data.mfaRequired) {
        // Return the response with mfaRequired flag instead of rejecting
        return { ...response.data, mfaRequired: true };
      }

      // Check if account activation is required
      if (response.data.activationRequired) {
        // Return the response with activationRequired flag instead of rejecting
        return { ...response.data, activationRequired: true };
      }
      
      // Store auth data in localStorage
      if (response.data.tokens) {
        const currentTenant = JSON.parse(localStorage.getItem("tenant") || "null");
        authService.storeAuth({
          user: response.data.user,
          tenant: currentTenant,
          accessToken: response.data.tokens!.accessToken,
          refreshToken: response.data.tokens!.refreshToken,
        });
      }

      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.message || "Login failed");
    }
  }
);

export const activateAccount = createAsyncThunk(
  "auth/activateAccount",
  async (activationData: ActivateAccountRequest, { rejectWithValue }) => {
    try {
      const response = await authService.activateAccount(activationData);
      
      // Store auth data in localStorage
      authService.storeAuth({
        user: response.data.user,
        tenant: {} as Tenant, // Will be populated from stored tenant data
        accessToken: response.data.tokens.accessToken,
        refreshToken: response.data.tokens.refreshToken,
      });

      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.message || "Account activation failed");
    }
  }
);

export const verifyTenant = createAsyncThunk(
  "auth/verifyTenant",
  async (verifyData: VerifyTenantRequest, { rejectWithValue }) => {
    try {
      const tenant = await authService.verifyTenant(verifyData);
      
      // Store tenant data in localStorage
      localStorage.setItem("tenant", JSON.stringify(tenant));
      return tenant;
    } catch (error: any) {
      return rejectWithValue(error.message || "Tenant verification failed");
    }
  }
);

export const verifyUserOTP = createAsyncThunk(
  "auth/verifyUserOTP",
  async (otpData: { userId: string; otp: string }, { rejectWithValue }) => {
    try {
      const response = await authService.verifyUserOTP(otpData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.message || "OTP verification failed");
    }
  }
);

export const forgotPassword = createAsyncThunk(
  "auth/forgotPassword",
  async ({ email, tenantId }: { email: string; tenantId: string }, { rejectWithValue }) => {
    try {
      await authService.forgotPassword(email, tenantId);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to send reset link");
    }
  }
);

export const resetPassword = createAsyncThunk(
  "auth/resetPassword",
  async ({ token, password }: { token: string; password: string }, { rejectWithValue }) => {
    try {
      await authService.resetPassword(token, password);
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to reset password");
    }
  }
);

export const refreshAuthToken = createAsyncThunk(
  "auth/refreshToken",
  async (_, { rejectWithValue }) => {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await authService.refreshToken(refreshToken);
      
      // Ensure tokens are present in the response
      if (!response.data.tokens) {
        throw new Error('No tokens received from refresh');
      }
      
      // Store updated auth data in localStorage
      const currentTenant = JSON.parse(localStorage.getItem("tenant") || "null");
      authService.storeAuth({
        user: response.data.user,
        tenant: currentTenant,
        accessToken: response.data.tokens.accessToken,
        refreshToken: response.data.tokens.refreshToken,
      });

      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.message || "Token refresh failed");
    }
  }
);

export const logoutUser = createAsyncThunk(
  "auth/logoutUser",
  async (_, { rejectWithValue }) => {
    try {
      await authService.logout();
      return;
    } catch (error: any) {
      return rejectWithValue(error.message || "Logout failed");
    }
  }
);

export const loadStoredAuth = createAsyncThunk(
  "auth/loadStoredAuth",
  async (_, { rejectWithValue }) => {
    try {
      const storedAuth = authService.getStoredAuth();
      
      // Only return if we have both user and tokens
      if (storedAuth.user && storedAuth.accessToken) {
        return storedAuth;
      }
      
      return null;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to load stored auth");
    }
  }
);

export const resendUserActivationOTP = createAsyncThunk(
  "auth/resendUserActivationOTP",
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await authService.resendUserActivationOTP(userId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to resend OTP");
    }
  }
);

// Auth slice
const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
    },
    setTenant: (state, action: PayloadAction<Tenant>) => {
      state.tenant = action.payload;
    },
    setTokens: (state, action: PayloadAction<{ accessToken: string; refreshToken: string }>) => {
      state.tokens = action.payload;
    },
    setSSOLoginData: (state, action: PayloadAction<{ user: User; tenant: Tenant; tokens: { accessToken: string; refreshToken: string } }>) => {
      state.user = action.payload.user;
      state.tenant = action.payload.tenant;
      state.tokens = action.payload.tokens;
      state.isAuthenticated = true;
      state.loading = false;
      state.error = null;
    },
    clearAuth: (state) => {
      state.user = null;
      state.tenant = null;
      state.tokens = {
        accessToken: null,
        refreshToken: null,
      };
      state.isAuthenticated = false;
      state.error = null;
      state.loading = false;
    },
  },
  extraReducers: (builder) => {
    // Validate Tenant
    builder
      .addCase(validateTenant.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(validateTenant.fulfilled, (state, action) => {
        state.loading = false;
        state.tenant = action.payload;
        state.error = null;
      })
      .addCase(validateTenant.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.tenant = null;
      });

    // Login User
    builder
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        if (action.payload.tenant) {
          state.tenant = action.payload.tenant;
        }
        if (action.payload.tokens) {
          state.tokens = {
            accessToken: action.payload.tokens.accessToken,
            refreshToken: action.payload.tokens.refreshToken,
          };
          state.isAuthenticated = true;
        }
        state.mfaRequired = action.payload.mfaRequired || false;
        state.activationRequired = action.payload.activationRequired || false;
        // If MFA or activation is required, don't set isAuthenticated to true
        if (!action.payload.mfaRequired && !action.payload.activationRequired) {
          state.isAuthenticated = true;
        }
        state.error = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
      });

    // Activate Account
    builder
      .addCase(activateAccount.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(activateAccount.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        if (action.payload.tokens) {
          state.tokens = {
            accessToken: action.payload.tokens.accessToken,
            refreshToken: action.payload.tokens.refreshToken,
          };
          state.isAuthenticated = true;
        }
        state.error = null;
      })
      .addCase(activateAccount.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
      });

    // Verify Tenant
    builder
      .addCase(verifyTenant.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyTenant.fulfilled, (state, action) => {
        state.loading = false;
        state.tenant = action.payload;
        state.error = null;
      })
      .addCase(verifyTenant.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Verify User OTP
    builder
      .addCase(verifyUserOTP.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyUserOTP.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.activationRequired = false;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(verifyUserOTP.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Logout User
    builder
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null;
        state.tenant = null;
        state.tokens = {
          accessToken: null,
          refreshToken: null,
        };
        state.isAuthenticated = false;
        state.error = null;
        state.loading = false;
      });

    // Refresh Token
    builder
      .addCase(refreshAuthToken.pending, (state) => {
        state.error = null;
      })
      .addCase(refreshAuthToken.fulfilled, (state, action) => {
        state.user = action.payload.user;
        if (action.payload.tokens) {
          state.tokens = {
            accessToken: action.payload.tokens.accessToken,
            refreshToken: action.payload.tokens.refreshToken,
          };
          state.isAuthenticated = true;
        }
        state.error = null;
      })
      .addCase(refreshAuthToken.rejected, (state, action) => {
        state.user = null;
        state.tenant = null;
        state.tokens = {
          accessToken: null,
          refreshToken: null,
        };
        state.isAuthenticated = false;
        state.error = action.payload as string;
      });

    // Load Stored Auth
    builder
      .addCase(loadStoredAuth.fulfilled, (state, action) => {
        if (action.payload) {
          state.user = action.payload.user;
          state.tenant = action.payload.tenant;
          state.tokens = {
            accessToken: action.payload.accessToken,
            refreshToken: action.payload.refreshToken,
          };
          state.isAuthenticated = true;
        }
        state.loading = false;
      })
      .addCase(loadStoredAuth.rejected, (state) => {
        state.loading = false;
      });

    // Forgot Password
    builder
      .addCase(forgotPassword.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(forgotPassword.fulfilled, (state) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(forgotPassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Reset Password
    builder
      .addCase(resetPassword.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(resetPassword.fulfilled, (state) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(resetPassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setUser, setTenant, setTokens, setSSOLoginData, clearAuth } = authSlice.actions;
export default authSlice.reducer;
