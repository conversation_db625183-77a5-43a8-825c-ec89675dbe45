import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '@/lib/rtkAxiosBaseQuery';
import { API_BASE_URL } from '@/services/api';

export type Role = {
  id?: string;
  _id?: string;
  name: string;
  key?: string;
  description?: string;
  type: 'system' | 'custom';
  userType: 'systemAdmin' | 'member' | 'agent';
  permissions: string[];
  isActive: boolean;
  userCount?: number;
};

export type CreateRoleRequest = {
  name: string;
  description?: string;
  type?: 'system' | 'custom';
  userType?: 'systemAdmin' | 'member' | 'agent';
  permissions?: string[];
  isActive?: boolean;
};

export type UpdateRoleRequest = Partial<CreateRoleRequest>;

export const rolesApi = createApi({
  reducerPath: 'rolesApi',
  tagTypes: ['Role'],
  baseQuery: axiosBaseQuery({
    baseUrl: `${API_BASE_URL}/roles`,
  }),
  endpoints: (builder) => ({
    getRoles: builder.query<Role[], void>({
      query: () => ({ url: '', method: 'GET' }),
      providesTags: ['Role'],
      transformResponse: (response: any) => {
        return response?.data?.roles;
      },
    }),

    getRoleById: builder.query<Role, string>({
      query: (id) => ({ url: `/${id}`, method: 'GET' }),
      providesTags: (result, error, id) => [{ type: 'Role', id }],
    }),

    createRole: builder.mutation<Role, CreateRoleRequest>({
      query: (data) => ({ url: '', method: 'POST', data }),
      invalidatesTags: ['Role'],
    }),

    updateRole: builder.mutation<Role, { id: string; data: UpdateRoleRequest }>({
      query: ({ id, data }) => {
        if (!id || id === 'undefined') throw new Error('Invalid ID provided for update');
        return { url: `/${id}`, method: 'PUT', data };
      },
      invalidatesTags: (result, error, { id }) => [{ type: 'Role', id }, 'Role'],
    }),

    deleteRole: builder.mutation<void, string>({
      query: (id) => {
        if (!id || id === 'undefined') throw new Error('Invalid ID provided for delete');
        return { url: `/${id}`, method: 'DELETE' };
      },
      invalidatesTags: ['Role'],
    }),
  }),
});

export const {
  useGetRolesQuery,
  useGetRoleByIdQuery,
  useCreateRoleMutation,
  useUpdateRoleMutation,
  useDeleteRoleMutation,
} = rolesApi;
