import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '@/lib/rtkAxiosBaseQuery';
import type {
  CannedResponse,
  CreateCannedResponseRequest,
  UpdateCannedResponseRequest
} from '@/types/canned-responses';
import { API_BASE_URL } from '@/services/api';



export const cannedResponseApi = createApi({
  reducerPath: 'cannedResponseApi',
  tagTypes: ['CannedResponse'],
  baseQuery: axiosBaseQuery({
    baseUrl: `${API_BASE_URL}/canned-responses`,
  }),
  endpoints: (builder) => ({
    // Get all canned responses
    getCannedResponses: builder.query<CannedResponse[], void>({
      query: () => ({
        url: '',
        method: 'GET',
      }),
      providesTags: ['CannedResponse'],
      transformResponse: (response: any) => {

        let data = [];
        // Handle different possible response formats
        if (Array.isArray(response)) {
          data = response;
        } else if (response && Array.isArray(response.data)) {
          data = response.data;
        }

        // Normalize the data to ensure each item has an 'id' field
        data = data.map((item: any) => ({
          ...item,
          // Map common ID field variations to 'id'
          id: item.id || item._id || item.responseId || item.cannedResponseId,
        }));

        return data;
      },
    }),

    // Get canned response by ID
    getCannedResponseById: builder.query<CannedResponse, string>({
      query: (id) => ({
        url: `/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'CannedResponse', id }],
    }),

    // Create new canned response
    createCannedResponse: builder.mutation<CannedResponse, CreateCannedResponseRequest>({
      query: (data) => ({
        url: '',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['CannedResponse'],
    }),

    // Update canned response
    updateCannedResponse: builder.mutation<
      CannedResponse,
      { id: string; data: UpdateCannedResponseRequest }
    >({
      query: ({ id, data }) => {
        if (!id || id === 'undefined') {
          throw new Error('Invalid ID provided for update');
        }
        return {
          url: `/${id}`,
          method: 'PUT',
          data,
        };
      },
      invalidatesTags: (result, error, { id }) => [
        { type: 'CannedResponse', id },
        'CannedResponse',
      ],
    }),

    // Delete canned response
    deleteCannedResponse: builder.mutation<void, string>({
      query: (id) => {
        if (!id || id === 'undefined') {
          throw new Error('Invalid ID provided for delete');
        }
        return {
          url: `/${id}`,
          method: 'DELETE',
        };
      },
      invalidatesTags: ['CannedResponse'],
    }),
  }),
});

export const {
  useGetCannedResponsesQuery,
  useGetCannedResponseByIdQuery,
  useCreateCannedResponseMutation,
  useUpdateCannedResponseMutation,
  useDeleteCannedResponseMutation,
} = cannedResponseApi;
