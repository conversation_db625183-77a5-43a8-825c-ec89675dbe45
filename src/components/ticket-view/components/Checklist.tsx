"use client";

import * as React from "react";
import { Trash2, Plus, CheckSquare } from "lucide-react";
import { toast } from "sonner";
import {
  AppCard,
  AppCardContent,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  AppInput,
  Checkbox,
  AppBadge,
} from "@/components/ui-toolkit";
import { ChecklistItem } from "../types";
import {
  useGetChecklistsForTicketQuery,
  useCreateChecklistForTicketMutation,
  useAddChecklistItemMutation,
  useUpdateChecklistItemMutation,
  useRemoveChecklistItemMutation,
} from "@/services/api/checklists";

interface ChecklistProps {
  ticketId: string;
}

export default function Checklist({ ticketId }: ChecklistProps) {
  const [newItemText, setNewItemText] = React.useState('');
  const [isAddingItem, setIsAddingItem] = React.useState(false);

  // API hooks
  const { data: checklists = [], isLoading, error } = useGetChecklistsForTicketQuery(ticketId);
  const [createChecklist] = useCreateChecklistForTicketMutation();
  const [addItem] = useAddChecklistItemMutation();
  const [updateItem] = useUpdateChecklistItemMutation();
  const [removeItem] = useRemoveChecklistItemMutation();

  // Handle checkbox change
  const handleCheckboxChange = async (checklistId: string, itemId: string, checked: boolean) => {
    try {
      await updateItem({
        checklistId,
        itemId,
        data: { isDone: checked }
      }).unwrap();
      toast.success(checked ? 'Item completed' : 'Item marked incomplete');
    } catch (error) {
      console.error('Error updating checklist item:', error);
      toast.error('Failed to update item');
    }
  };

  // Add new item
  const handleAddItem = async () => {
    if (!newItemText.trim()) return;

    // Find the first checklist or create one if none exists
    let targetChecklistId = checklists[0]?._id;

    if (!targetChecklistId) {
      try {
        const result = await createChecklist({
          ticketId,
          data: {
            title: 'Checklist',
            items: []
          }
        }).unwrap();
        targetChecklistId = result._id;
      } catch (error) {
        console.error('Error creating checklist:', error);
        toast.error('Failed to create checklist');
        return;
      }
    }

    try {
      await addItem({
        checklistId: targetChecklistId,
        data: {
          text: newItemText.trim(),
          isMandatory: false
        }
      }).unwrap();
      setNewItemText('');
      setIsAddingItem(false);
      toast.success('Item added successfully');
    } catch (error) {
      console.error('Error adding checklist item:', error);
      toast.error('Failed to add item');
    }
  };

  // Remove item
  const handleRemoveItem = async (checklistId: string, itemId: string) => {
    try {
      await removeItem({
        checklistId,
        itemId
      }).unwrap();
      toast.success('Item removed');
    } catch (error) {
      console.error('Error removing checklist item:', error);
      toast.error('Failed to remove item');
    }
  };

  if (isLoading) {
    return (
      <AppCard>
        <AppCardContent className="p-6">
          <div className="text-center text-muted-foreground">Loading checklist...</div>
        </AppCardContent>
      </AppCard>
    );
  }

  if (error) {
    return (
      <AppCard>
        <AppCardContent className="p-6">
          <div className="text-center text-red-500">Error loading checklist</div>
        </AppCardContent>
      </AppCard>
    );
  }

  // Get the first checklist (Jira style - single checklist per ticket)
  const checklist = checklists[0];
  const totalItems = checklist?.items.length || 0;
  const completedItems = checklist?.items.filter(item => item.isDone).length || 0;

  return (
    <AppCard>
      <AppCardHeader>
        <AppCardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CheckSquare className="w-5 h-5" />
            <span>Checklist</span>
          </div>
          {totalItems > 0 && (
            <AppBadge variant="secondary">
              {completedItems}/{totalItems}
            </AppBadge>
          )}
        </AppCardTitle>
      </AppCardHeader>
      <AppCardContent>
        <div className="space-y-2">
          {/* Checklist items */}
          {checklist?.items.map((item) => (
            <div key={item.itemId} className="flex items-center gap-3 group">
              <Checkbox
                id={item.itemId}
                checked={item.isDone}
                onCheckedChange={(checked) => handleCheckboxChange(checklist._id, item.itemId, checked === true)}
              />
              <span
                className={`flex-1 text-sm ${
                  item.isDone ? 'line-through text-muted-foreground' : ''
                }`}
              >
                {item.text}
              </span>
              <AppButton
                variant="ghost"
                size="sm"
                onClick={() => handleRemoveItem(checklist._id, item.itemId)}
                className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0 text-muted-foreground hover:text-red-500"
              >
                <Trash2 className="w-3 h-3" />
              </AppButton>
            </div>
          ))}

          {/* Empty state */}
          {(!checklist || checklist.items.length === 0) && !isAddingItem && (
            <div className="text-center py-8">
              <CheckSquare className="w-12 h-12 mx-auto text-muted-foreground/50 mb-3" />
              <p className="text-muted-foreground mb-4">No checklist items yet</p>
              <AppButton onClick={() => setIsAddingItem(true)} variant="outline" size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Add checklist item
              </AppButton>
            </div>
          )}

          {/* Add new item input */}
          {isAddingItem && (
            <div className="flex items-center gap-2 pt-2">
              <AppInput
                placeholder="Add a checklist item..."
                value={newItemText}
                onChange={(e) => setNewItemText(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleAddItem();
                  } else if (e.key === 'Escape') {
                    setNewItemText('');
                    setIsAddingItem(false);
                  }
                }}
                className="flex-1 h-8 text-sm"
                autoFocus
              />
              <AppButton
                size="sm"
                onClick={handleAddItem}
                disabled={!newItemText.trim()}
              >
                Add
              </AppButton>
              <AppButton
                size="sm"
                variant="ghost"
                onClick={() => {
                  setNewItemText('');
                  setIsAddingItem(false);
                }}
              >
                Cancel
              </AppButton>
            </div>
          )}

          {/* Add item button (when not adding) */}
          {checklist && checklist.items.length > 0 && !isAddingItem && (
            <AppButton
              variant="ghost"
              size="sm"
              onClick={() => setIsAddingItem(true)}
              className="w-full justify-start text-muted-foreground hover:text-foreground mt-2"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add checklist item
            </AppButton>
          )}
        </div>
      </AppCardContent>
    </AppCard>
  );
}