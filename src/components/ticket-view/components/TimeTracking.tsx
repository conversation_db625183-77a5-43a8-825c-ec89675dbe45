"use client";

import * as React from "react";
import { Clock } from "lucide-react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import {
  AppButton,
  AppInput,
  AppTextarea,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui-toolkit";

interface TimeFormData {
  timeInput: string;
  description: string;
  endDateTime: Date | null;
}

interface TimeTrackingProps {
  timeLogDialogOpen: boolean;
  estimateDialogOpen: boolean;
  onTimeLogDialogClose: () => void;
  onEstimateDialogClose: () => void;
  timeForm: TimeFormData;
  onTimeFormChange: (form: TimeFormData) => void;
  newEstimate: string;
  onNewEstimateChange: (estimate: string) => void;
  onTimeLogSubmit: (e: React.FormEvent) => void;
  onEstimateSubmit: (e: React.FormEvent) => void;
  worklogSummary?: {
    totalMinutes: number;
    billableMinutes: number;
    totalHours: number;
    billableHours: number;
    agentBreakdown: Array<{
      totalMinutes: number;
      billableMinutes: number;
      entryCount: number;
      agentId: string;
      agentName: string;
      agentRole: string;
    }>;
  };
  formatTimeString?: (hours: number) => string;
  isEditing?: boolean;
}

export default function TimeTracking({
  timeLogDialogOpen,
  estimateDialogOpen,
  onTimeLogDialogClose,
  onEstimateDialogClose,
  timeForm,
  onTimeFormChange,
  newEstimate,
  onNewEstimateChange,
  onTimeLogSubmit,
  onEstimateSubmit,
  worklogSummary,
  formatTimeString,
  isEditing = false,
}: TimeTrackingProps) {
  return (
    <>
      {/* Time Log Dialog */}
      <Dialog open={timeLogDialogOpen} onOpenChange={onTimeLogDialogClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{isEditing ? 'Edit Worklog' : 'Log Time'}</DialogTitle>
            <DialogDescription>
              {isEditing ? 'Update the time entry for this ticket.' : 'Log time spent working on this ticket.'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={onTimeLogSubmit} className="space-y-4">
            <div>
              <label className="text-sm font-medium">Time Spent</label>
              <AppInput
                value={timeForm.timeInput}
                onChange={(e) => onTimeFormChange({...timeForm, timeInput: e.target.value})}
                placeholder="e.g., 2h, 30m, 1h 30m, 1.5h, 1d"
                required
              />
              <p className="text-xs text-muted-foreground mt-1">
                Formats: 2h, 30m, 1h 30m, 1.5, 90m, 1d (1 day = 8 hours)
              </p>
            </div>

            <div>
              <label className="text-sm font-medium">Description (Optional)</label>
              <AppTextarea
                value={timeForm.description}
                onChange={(e) => onTimeFormChange({...timeForm, description: e.target.value})}
                placeholder="What did you work on?"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="text-sm font-medium">End Time (Optional)</label>
                <div className="mt-1">
                  <DatePicker
                    selected={timeForm.endDateTime}
                    onChange={(date) => onTimeFormChange({...timeForm, endDateTime: date})}
                    showTimeSelect
                    timeFormat="HH:mm"
                    timeIntervals={15}
                    dateFormat="MMM dd, yyyy HH:mm"
                    placeholderText="Select end time"
                    className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                    wrapperClassName="w-full"
                  />
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  When did you finish working on this?
                </p>
              </div>
            </div>

            {/* Work Summary */}
            {/* {worklogSummary && formatTimeString && (
              <div className="bg-muted/30 rounded-lg p-3 space-y-2">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4" />
                  <span className="text-sm font-medium">Work Summary</span>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <span className="text-muted-foreground">Total Time:</span>
                    <span className="font-medium ml-1">{formatTimeString(worklogSummary.totalHours)}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Billable:</span>
                    <span className="font-medium ml-1">{formatTimeString(worklogSummary.billableHours)}</span>
                  </div>
                </div>
                {worklogSummary.agentBreakdown.length > 0 && (
                  <div className="pt-2 border-t">
                    <span className="text-xs font-medium text-muted-foreground">By Agent:</span>
                    <div className="space-y-1 mt-1">
                      {worklogSummary.agentBreakdown.map((agent) => (
                        <div key={agent.agentId} className="flex justify-between text-xs">
                          <span>{agent.agentName}:</span>
                          <span className="font-medium">{formatTimeString(agent.totalMinutes / 60)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )} */}

            <div className="flex justify-end space-x-2 pt-4">
              <AppButton variant="outline" onClick={onTimeLogDialogClose}>
                Cancel
              </AppButton>
              <AppButton type="submit">
                {isEditing ? 'Update Worklog' : 'Log Time'}
              </AppButton>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Estimate Dialog */}
      <Dialog open={estimateDialogOpen} onOpenChange={onEstimateDialogClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Set Time Estimate</DialogTitle>
            <DialogDescription>
              Set an estimated time for completing this ticket.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={onEstimateSubmit} className="space-y-4">
            <div>
              <label className="text-sm font-medium">Estimated Time</label>
              <AppInput
                value={newEstimate}
                onChange={(e) => onNewEstimateChange(e.target.value)}
                placeholder="e.g., 8h, 2d, 1h 30m"
                required
              />
              <p className="text-xs text-muted-foreground mt-1">
                Formats: 8h, 2d, 1h 30m, 4.5h, 480m
              </p>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <AppButton variant="outline" onClick={onEstimateDialogClose}>
                Cancel
              </AppButton>
              <AppButton type="submit">
                Set Estimate
              </AppButton>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
}