"use client";

import * as React from "react";
import { useSearchParams } from "next/navigation";
import { MessageSquare, Edit2, Trash2, Save, X, Reply, MoreH<PERSON>zon<PERSON>, Paperclip, Upload, Eye } from "lucide-react";
import { toast } from "sonner";

// Extend window interface for image viewer
declare global {
  interface Window {
    openImageViewer?: (images: any[], index: number) => void;
  }
}
import {
  AppCard,
  AppCardContent,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  AppBadge,
  AppTextarea,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui-toolkit";
import { type User, type CannedResponse, type Ticket } from "@/lib/demo/types";
import { type Comment } from "@/types/api";
import {
  useGetCommentsQuery,
  useCreateCommentMutation,
  useUpdateCommentMutation,
  useDeleteCommentMutation
} from "@/services/api/comments";
import { useGetCannedResponsesQuery } from "@/redux/slices/cannedResponses/cannedResponseSlice";
import type { CreateCommentRequest } from "@/types/api";
import { getRelativeTime, getFullDate } from "@/lib/utils";
import { UserProfileHover } from "./UserProfileHover";

interface CommentsProps {
  ticket: Ticket | null;
  entityType?: '0' | '1'; // 0 = ticket, 1 = subtask
  entityId?: string; // The ID of the ticket or subtask
}

export default function Comments({ ticket, entityType: propEntityType, entityId: propEntityId }: CommentsProps) {
  const searchParams = useSearchParams();
  const ticketParam = searchParams.get('ticket');
  const subTicketParam = searchParams.get('subTicket');
  // Determine if we're dealing with a subtask or main ticket
  // Use props if provided, otherwise fall back to URL parsing
  const isSubTask = !!subTicketParam;
  const urlEntityId = isSubTask ? subTicketParam : (ticketParam || ticket?.id || '');
  const urlEntityType = isSubTask ? 1 : 0; // 0 = ticket, 1 = subtask

  // Use prop values if provided, otherwise use URL-derived values
  const currentEntityId = propEntityId || urlEntityId;
  const currentEntityType = propEntityType ? parseInt(propEntityType) : urlEntityType;
  const [commentBody, setCommentBody] = React.useState("");
  const [commentType, setCommentType] = React.useState<"public" | "internal">("public");
  const [cannedResponseDialogOpen, setCannedResponseDialogOpen] = React.useState(false);
  const [editingCommentId, setEditingCommentId] = React.useState<string | null>(null);
  const [editingBody, setEditingBody] = React.useState("");
  const [replyingToCommentId, setReplyingToCommentId] = React.useState<string | null>(null);
  const [replyBody, setReplyBody] = React.useState("");
  const [replyType, setReplyType] = React.useState<"public" | "internal">("public");
  const [commentAttachments, setCommentAttachments] = React.useState<File[]>([]);
  const [editingAttachments, setEditingAttachments] = React.useState<File[]>([]);
  const [replyAttachments, setReplyAttachments] = React.useState<File[]>([]);


  // RTK Query hooks
  const { data: commentsResponse, isLoading: commentsLoading, refetch: refetchComments } = useGetCommentsQuery(
    { 
      ticketId: currentEntityId, 
      params: { 
        entityType: currentEntityType.toString(),
        includeReplies: true 
      }
    },
    { skip: !currentEntityId }
  ); 
  const { data: cannedResponsesData, isLoading: cannedResponsesLoading } = useGetCannedResponsesQuery();
   const [createComment, { isLoading: createLoading }] = useCreateCommentMutation();
  const [updateComment, { isLoading: updateLoading }] = useUpdateCommentMutation();
  const [deleteComment, { isLoading: deleteLoading }] = useDeleteCommentMutation();

  const comments = commentsResponse?.data || [];

  // Attachment handling functions
  const handleCommentFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      setCommentAttachments(prev => [...prev, ...files]);
    }
    e.target.value = '';
  };

  const handleRemoveCommentAttachment = (index: number) => {
    setCommentAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleEditFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      setEditingAttachments(prev => [...prev, ...files]);
    }
    e.target.value = '';
  };

  const handleRemoveEditAttachment = (index: number) => {
    setEditingAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleReplyFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      setReplyAttachments(prev => [...prev, ...files]);
    }
    e.target.value = '';
  };

  const handleRemoveReplyAttachment = (index: number) => {
    setReplyAttachments(prev => prev.filter((_, i) => i !== index));
  };

  // Organize comments hierarchically
  const organizeComments = (comments: Comment[]): Comment[] => {
    const commentMap = new Map<string, Comment>();
    const rootComments: Comment[] = [];

    // First pass: create comment map
    comments.forEach(comment => {
      commentMap.set(comment._id, { ...comment, replies: [] });
    });

    // Second pass: organize into hierarchy
    comments.forEach(comment => {
      if (comment.parentCommentId) {
        const parent = commentMap.get(comment.parentCommentId);
        if (parent) {
          parent.replies = parent.replies || [];
          parent.replies.push(commentMap.get(comment._id)!);
        }
      } else {
        rootComments.push(commentMap.get(comment._id)!);
      }
    });

    return rootComments;
  };

  const organizedComments = organizeComments(comments);

  const renderComment = (comment: Comment, depth = 0) => {
    const isEditing = editingCommentId === comment._id;
    const isReplying = replyingToCommentId === comment._id;
    const maxDepth = 3; // Limit nesting depth
    
    return (
      <div key={comment._id} className={`${depth > 0 ? 'ml-8 border-l-2 border-muted-foreground/40 pl-6' : ''}`}>
        <div className="group py-3 hover:bg-muted/30 transition-colors rounded-lg px-2">
          {/* Comment Header */}
          <div className="flex items-start space-x-3 mb-2">
            {comment.author ? (
              <UserProfileHover user={comment.author}>
                <Avatar className="h-8 w-8 flex-shrink-0">
                  <AvatarImage src={comment.author.avatar} />
                  <AvatarFallback className="text-xs">
                    {comment.author.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
              </UserProfileHover>
            ) : (
              <Avatar className="h-8 w-8 flex-shrink-0">
                <AvatarFallback className="text-xs">U</AvatarFallback>
              </Avatar>
            )}
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                {comment.author ? (
                  <UserProfileHover user={comment.author}>
                    <span className="font-medium text-sm text-foreground hover:text-primary transition-colors">
                      {comment.author.name}
                    </span>
                  </UserProfileHover>
                ) : (
                  <span className="font-medium text-sm text-foreground">
                    Unknown User
                  </span>
                )}
                
                <AppBadge 
                  variant={comment.visibility === "internal" ? "secondary" : "outline"} 
                  className="text-xs h-5"
                >
                  {comment.visibility}
                </AppBadge>
                
                {comment.isEdited && (
                  <span className="text-xs text-muted-foreground">(edited)</span>
                )}
              </div>
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="text-xs text-muted-foreground cursor-pointer">
                    {getRelativeTime(comment.createdAt)}
                  </span>
                </TooltipTrigger>
                <TooltipContent>
                  {getFullDate(comment.createdAt)}
                </TooltipContent>
              </Tooltip>
            </div>

            {/* Comment Actions */}
            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {depth < maxDepth && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <AppButton
                      variant="ghost"
                      size="sm"
                      className="h-7 w-7 p-0 text-muted-foreground hover:text-foreground"
                      onClick={() => handleReplyToComment(comment._id)}
                    >
                      <Reply className="h-4 w-4" />
                    </AppButton>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Reply</p>
                  </TooltipContent>
                </Tooltip>
              )}
              <Tooltip>
                <TooltipTrigger asChild>
                  <AppButton
                    variant="ghost"
                    size="sm"
                    className="h-7 w-7 p-0 text-muted-foreground hover:text-foreground"
                    onClick={() => handleEditComment(comment._id, comment.body)}
                  >
                    <Edit2 className="h-4 w-4" />
                  </AppButton>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Edit</p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <AppButton
                    variant="ghost"
                    size="sm"
                    className="h-7 w-7 p-0 text-destructive/70 hover:text-destructive"
                    onClick={() => handleDeleteComment(comment._id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </AppButton>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Delete</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>

          {/* Comment Body */}
          <div className="ml-11">
            {isEditing ? (
              <div className="space-y-3 bg-muted/30 p-3 rounded-lg border">
                <AppTextarea
                  value={editingBody}
                  onChange={(e) => setEditingBody(e.target.value)}
                  className="min-h-[80px] resize-none border-0 bg-transparent focus:ring-0"
                  placeholder="Edit your comment..."
                />

                {/* Edit Attachments */}
                <div className="flex items-center space-x-3 pt-2 border-t border-border/30">
                  <div className="relative">
                    <input
                      type="file"
                      id={`edit-file-upload-${comment._id}`}
                      className="hidden"
                      multiple
                      accept="image/*,.pdf,.doc,.docx"
                      onChange={handleEditFileSelect}
                    />
                    <AppButton
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-8 px-3 text-xs text-muted-foreground hover:text-foreground hover:bg-muted/50"
                      onClick={() => document.getElementById(`edit-file-upload-${comment._id}`)?.click()}
                    >
                      <Paperclip className="h-3 w-3 mr-1" />
                      Add Files
                    </AppButton>
                  </div>

                  {/* Attachment Count */}
                  {editingAttachments.length > 0 && (
                    <span className="text-xs text-muted-foreground">
                      {editingAttachments.length} file{editingAttachments.length > 1 ? 's' : ''} selected
                    </span>
                  )}
                </div>

                {/* Edit Attachment Preview */}
                {editingAttachments.length > 0 && (
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Paperclip className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">
                        New Attachments ({editingAttachments.length})
                      </span>
                    </div>
                    <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                      {editingAttachments.map((file, index) => (
                        <div key={index} className="relative group">
                          {file.type.startsWith('image/') ? (
                            <div className="aspect-video relative overflow-hidden rounded-md border bg-muted max-h-16">
                              <img
                                src={URL.createObjectURL(file)}
                                alt={file.name}
                                className="w-full h-full object-cover"
                              />
                            </div>
                          ) : (
                            <div className="aspect-video relative overflow-hidden rounded-md border bg-muted flex items-center justify-center max-h-16">
                              <div className="text-center">
                                <div className="w-6 h-6 bg-muted-foreground/20 rounded mx-auto mb-1 flex items-center justify-center">
                                  <span className="text-xs font-medium text-muted-foreground">
                                    {file.name.split('.').pop()?.toUpperCase()}
                                  </span>
                                </div>
                                <p className="text-xs text-muted-foreground truncate px-1">
                                  {file.name}
                                </p>
                              </div>
                            </div>
                          )}
                          <AppButton
                            type="button"
                            variant="destructive"
                            size="sm"
                            className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => handleRemoveEditAttachment(index)}
                          >
                            <X className="h-3 w-3" />
                          </AppButton>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex justify-end space-x-2">
                  <AppButton
                    variant="outline"
                    size="sm"
                    onClick={handleCancelEdit}
                    disabled={updateLoading}
                  >
                    Cancel
                  </AppButton>
                  <AppButton
                    size="sm"
                    onClick={handleSaveEdit}
                    disabled={updateLoading || !editingBody.trim()}
                  >
                    {updateLoading ? "Saving..." : "Save"}
                  </AppButton>
                </div>
              </div>
            ) : (
              <div className="whitespace-pre-wrap text-sm text-foreground leading-relaxed">
                {comment.body}
              </div>
            )}

            {/* Comment Attachments */}
            {comment.attachments && comment.attachments.length > 0 && (
              <div className="ml-11 mt-3">
                <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                  {comment.attachments.map((attachment: any, index: number) => (
                    <div
                      key={attachment._id}
                      className="relative group cursor-pointer"
                      onClick={() => {
                        // Open image viewer with comment attachments
                        // We'll need to pass the attachments to the parent component
                        if (window.openImageViewer) {
                          window.openImageViewer(comment.attachments, index);
                        }
                      }}
                    >
                      <div className="aspect-video relative overflow-hidden rounded-md border bg-muted max-h-16">
                        <img
                          src={attachment.url}
                          alt={attachment.filename}
                          className="w-full h-full object-cover transition-transform group-hover:scale-105"
                          loading="lazy"
                        />
                      </div>
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors rounded-md flex items-center justify-center">
                        <Eye className="h-3 w-3 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Reply Form */}
          {isReplying && (
            <div className="ml-11 mt-3 space-y-3 bg-muted/20 p-3 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Reply to {comment.author?.name}</span>
                <Select value={replyType} onValueChange={(value: "public" | "internal") => setReplyType(value)}>
                  <SelectTrigger className="w-24 h-7 text-xs border-0 bg-background/50">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">Public</SelectItem>
                    <SelectItem value="internal">Internal</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <AppTextarea
                value={replyBody}
                onChange={(e) => setReplyBody(e.target.value)}
                className="min-h-[80px] resize-none border-0 bg-background/50 focus:ring-0"
                placeholder="Write your reply..."
              />

              {/* Reply Attachments */}
              <div className="flex items-center space-x-3 pt-2 border-t border-border/30">
                <div className="relative">
                  <input
                    type="file"
                    id={`reply-file-upload-${comment._id}`}
                    className="hidden"
                    multiple
                    accept="image/*,.pdf,.doc,.docx"
                    onChange={handleReplyFileSelect}
                  />
                  <AppButton
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-7 px-2 text-xs text-muted-foreground hover:text-foreground hover:bg-muted/50"
                    onClick={() => document.getElementById(`reply-file-upload-${comment._id}`)?.click()}
                  >
                    <Paperclip className="h-3 w-3 mr-1" />
                    Attach
                  </AppButton>
                </div>

                {replyAttachments.length > 0 && (
                  <span className="text-xs text-muted-foreground">
                    {replyAttachments.length} file{replyAttachments.length > 1 ? 's' : ''} selected
                  </span>
                )}
              </div>

              {/* Reply Attachment Preview */}
              {replyAttachments.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Paperclip className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium text-muted-foreground">
                      Attachments ({replyAttachments.length})
                    </span>
                  </div>
                  <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                    {replyAttachments.map((file, index) => (
                      <div key={index} className="relative group">
                        {file.type.startsWith('image/') ? (
                          <div className="aspect-video relative overflow-hidden rounded-md border bg-muted max-h-16">
                            <img
                              src={URL.createObjectURL(file)}
                              alt={file.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ) : (
                          <div className="aspect-video relative overflow-hidden rounded-md border bg-muted flex items-center justify-center max-h-16">
                            <div className="text-center">
                              <div className="w-6 h-6 bg-muted-foreground/20 rounded mx-auto mb-1 flex items-center justify-center">
                                <span className="text-xs font-medium text-muted-foreground">
                                  {file.name.split('.').pop()?.toUpperCase()}
                                </span>
                              </div>
                              <p className="text-xs text-muted-foreground truncate px-1">
                                {file.name}
                              </p>
                            </div>
                          </div>
                        )}
                        <AppButton
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => handleRemoveReplyAttachment(index)}
                        >
                          <X className="h-3 w-3" />
                        </AppButton>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="flex justify-end space-x-2">
                <AppButton
                  variant="outline"
                  size="sm"
                  onClick={handleCancelReply}
                  disabled={createLoading}
                >
                  Cancel
                </AppButton>
                <AppButton
                  size="sm"
                  onClick={() => handleSubmitReply(comment._id)}
                  disabled={createLoading || (!replyBody.trim() && replyAttachments.length === 0)}
                >
                  {createLoading ? "Sending..." : "Reply"}
                </AppButton>
              </div>
            </div>
          )}
        </div>

        {/* Render Replies */}
        {comment.replies && comment.replies.length > 0 && depth < maxDepth && (
          <div className="mt-2 space-y-2">
            {comment.replies.map((reply) => renderComment(reply, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  const handleEditComment = (commentId: string, currentBody: string) => {
    setEditingCommentId(commentId);
    setEditingBody(currentBody);
    setEditingAttachments([]);
  };

  const handleSaveEdit = async () => {
    if (!editingCommentId || !editingBody.trim()) return;

    try {
      if (editingAttachments.length > 0) {
        // Use FormData for multipart upload
        const formData = new FormData();
        formData.append('body', editingBody.trim());

        editingAttachments.forEach((file) => {
          formData.append('attachments', file);
        });

        await updateComment({
          commentId: editingCommentId,
          ticketId: currentEntityId,
          data: formData as any,
        }).unwrap();
      } else {
        // Use JSON for regular payload
        await updateComment({
          commentId: editingCommentId,
          ticketId: currentEntityId,
          data: { body: editingBody.trim() },
        }).unwrap();
      }

      toast.success("Comment updated successfully");
      setEditingCommentId(null);
      setEditingBody("");
      setEditingAttachments([]);
      refetchComments();
    } catch (error: any) {
      console.error("Failed to update comment:", error);
      toast.error(error?.data?.error || "Failed to update comment");
    }
  };

  const handleCancelEdit = () => {
    setEditingCommentId(null);
    setEditingBody("");
    setEditingAttachments([]);
  };

  const handleDeleteComment = async (commentId: string) => {
    if (!confirm("Are you sure you want to delete this comment?")) return;

    try {
      await deleteComment({
        commentId,
        ticketId: currentEntityId
      }).unwrap();
      toast.success("Comment deleted successfully");
      refetchComments();
    } catch (error: any) {
      console.error("Failed to delete comment:", error);
      toast.error(error?.data?.error || "Failed to delete comment");
    }
  };

  const handleReplyToComment = (commentId: string) => {
    setReplyingToCommentId(commentId);
    setReplyBody("");
    setReplyAttachments([]);
  };

  const handleCancelReply = () => {
    setReplyingToCommentId(null);
    setReplyBody("");
    setReplyAttachments([]);
  };

  const handleSubmitReply = async (parentCommentId: string) => {
    if (!currentEntityId || !replyBody.trim()) return;

    try {
      if (replyAttachments.length > 0) {
        // Use FormData for multipart upload
        const formData = new FormData();
        formData.append('body', replyBody.trim());
        formData.append('visibility', replyType);
        formData.append('parentCommentId', parentCommentId);
        formData.append('entityType', currentEntityType.toString());
        formData.append('ticketOrSubticketId', currentEntityId);

        replyAttachments.forEach((file) => {
          formData.append('attachments', file);
        });

        await createComment({
          ticketId: currentEntityId,
          data: formData as any,
        }).unwrap();
      } else {
        // Use JSON for regular payload
        const replyData: CreateCommentRequest = {
          body: replyBody.trim(),
          visibility: replyType,
          parentCommentId,
          entityType: currentEntityType.toString(),
          ticketOrSubticketId: currentEntityId,
        };

        await createComment({
          ticketId: currentEntityId,
          data: replyData,
        }).unwrap();
      }

      toast.success("Reply added successfully");
      setReplyingToCommentId(null);
      setReplyBody("");
      setReplyAttachments([]);
      refetchComments();
    } catch (error: any) {
      console.error("Failed to add reply:", error);
      toast.error(error?.data?.error || "Failed to add reply");
    }
  };

  const handleAddComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentEntityId || !commentBody.trim()) return;

    try {
      if (commentAttachments.length > 0) {
        // Use FormData for multipart upload
        const formData = new FormData();
        formData.append('body', commentBody.trim());
        formData.append('visibility', commentType);
        formData.append('entityType', currentEntityType.toString());
        formData.append('ticketOrSubticketId', currentEntityId);

        commentAttachments.forEach((file) => {
          formData.append('attachments', file);
        });

        await createComment({
          ticketId: currentEntityId,
          data: formData as any,
        }).unwrap();
      } else {
        // Use JSON for regular payload
        const commentData: CreateCommentRequest = {
          body: commentBody.trim(),
          visibility: commentType,
          entityType: currentEntityType.toString(),
          ticketOrSubticketId: currentEntityId,
        };

        await createComment({
          ticketId: currentEntityId,
          data: commentData,
        }).unwrap();
      }

      toast.success("Comment added successfully");
      setCommentBody("");
      setCommentAttachments([]);
      refetchComments();
    } catch (error: any) {
      console.error("Failed to add comment:", error);
      toast.error(error?.data?.error || "Failed to add comment");
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2 pb-2 border-b">
        <MessageSquare className="h-5 w-5 text-muted-foreground" />
        <h3 className="text-lg font-semibold">Comments</h3>
        <span className="text-sm text-muted-foreground">({organizedComments.length})</span>
      </div>
      
      <div className="space-y-4">
        {/* Add New Comment Section */}
        <div className="bg-gradient-to-r from-muted/30 to-muted/10 rounded-xl p-4 border border-border/50">
   
          <form onSubmit={handleAddComment} className="space-y-1">
            {/* Comment Type and Templates */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-muted-foreground">Visibility:</span>
                  <Select value={commentType} onValueChange={(value: "public" | "internal") => setCommentType(value)}>
                    <SelectTrigger className="w-32 h-9 text-sm border-border/50">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="public">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 rounded-full bg-green-500"></div>
                          <span>Public</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="internal">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                          <span>Internal</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Dialog open={cannedResponseDialogOpen} onOpenChange={setCannedResponseDialogOpen}>
                  <DialogTrigger asChild>
                    <AppButton
                      type="button"
                      variant="outline"
                      size="sm"
                      className="h-9 px-4 text-sm border-border/50 hover:bg-accent/50"
                    >
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Canned Responses
                    </AppButton>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle className="flex items-center space-x-2">
                        <MessageSquare className="h-5 w-5" />
                        <span>Choose Response Template</span>
                      </DialogTitle>
                      <DialogDescription>
                        Select a pre-written response to use as a starting point for your comment.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="max-h-96 overflow-y-auto space-y-3">
                      {cannedResponsesLoading ? (
                        <div className="text-center py-12">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                          <p className="text-muted-foreground">Loading templates...</p>
                        </div>
                      ) : !cannedResponsesData || cannedResponsesData.length === 0 ? (
                        <div className="text-center py-12">
                          <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                          <p className="text-muted-foreground font-medium">No templates available</p>
                          <p className="text-sm text-muted-foreground mt-1">Create some templates to speed up your responses.</p>
                        </div>
                      ) : (
                        cannedResponsesData.map((response) => (
                          <div
                            key={response.id}
                            className="border border-border/50 rounded-lg p-4 cursor-pointer hover:bg-accent/30 hover:border-accent-foreground/30 transition-all duration-200 hover:shadow-sm"
                            onClick={() => {
                              setCommentBody(response.content);
                              setCannedResponseDialogOpen(false);
                            }}
                          >
                            <div className="flex items-center justify-between mb-3">
                              <h4 className="font-semibold text-foreground text-base">{response.title}</h4>
                              <AppBadge variant="secondary" className="text-xs px-2 py-1">
                                {response.category}
                              </AppBadge>
                            </div>
                            <p className="text-sm text-muted-foreground leading-relaxed line-clamp-2">
                              {response.content}
                            </p>
                          </div>
                        ))
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            {/* Comment Input */}
            <div className="relative">
              <div className="bg-background/80 border border-border/50 rounded-lg p-3 focus-within:border-primary/50 focus-within:ring-1 focus-within:ring-primary/20 transition-all duration-200">
                <AppTextarea
                  value={commentBody}
                  onChange={(e) => setCommentBody(e.target.value)}
                  placeholder="Share your thoughts, feedback, or updates..."
                  className="min-h-[80px] resize-none border-0 bg-transparent focus:ring-0 text-base leading-relaxed placeholder:text-muted-foreground/70"
                  required
                />
                <div className="flex items-center justify-between mt-3 pt-3 border-t border-border/30">
                  <div className="flex items-center space-x-3">
                    {/* Attachment Button */}
                    <div className="relative">
                      <input
                        type="file"
                        id="comment-file-upload"
                        className="hidden"
                        multiple
                        accept="image/*,.pdf,.doc,.docx"
                        onChange={handleCommentFileSelect}
                      />
                      <AppButton
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-8 px-3 text-xs text-muted-foreground hover:text-foreground hover:bg-muted/50"
                        onClick={() => document.getElementById('comment-file-upload')?.click()}
                      >
                        <Paperclip className="h-3 w-3 mr-1" />
                        Attach
                      </AppButton>
                    </div>

                    {/* Attachment Count */}
                    {commentAttachments.length > 0 && (
                      <span className="text-xs text-muted-foreground">
                        {commentAttachments.length} file{commentAttachments.length > 1 ? 's' : ''} selected
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <AppButton
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setCommentBody("");
                        setCommentAttachments([]);
                      }}
                      disabled={!commentBody.trim() && commentAttachments.length === 0 || createLoading}
                      className="h-8 px-3 text-xs text-muted-foreground hover:text-foreground hover:bg-muted/50"
                    >
                      Clear
                    </AppButton>
                    <AppButton
                      type="submit"
                      size="sm"
                      disabled={createLoading || (!commentBody.trim() && commentAttachments.length === 0)}
                      className="h-8 px-4 text-xs bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm"
                    >
                      {createLoading ? (
                        <div className="flex items-center space-x-2">
                          <div className="animate-spin rounded-full h-3 w-3 border border-current border-t-transparent"></div>
                          <span>Sending...</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <MessageSquare className="h-3 w-3" />
                          <span>Comment</span>
                        </div>
                      )}
                    </AppButton>
                  </div>
                </div>
              </div>
            </div>

            {/* Attachment Preview */}
            {commentAttachments.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Paperclip className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium text-muted-foreground">
                    Attachments ({commentAttachments.length})
                  </span>
                </div>
                <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                  {commentAttachments.map((file, index) => (
                    <div key={index} className="relative group">
                      {file.type.startsWith('image/') ? (
                        <div className="aspect-video relative overflow-hidden rounded-md border bg-muted max-h-16">
                          <img
                            src={URL.createObjectURL(file)}
                            alt={file.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ) : (
                        <div className="aspect-video relative overflow-hidden rounded-md border bg-muted flex items-center justify-center max-h-16">
                          <div className="text-center">
                            <div className="w-6 h-6 bg-muted-foreground/20 rounded mx-auto mb-1 flex items-center justify-center">
                              <span className="text-xs font-medium text-muted-foreground">
                                {file.name.split('.').pop()?.toUpperCase()}
                              </span>
                            </div>
                            <p className="text-xs text-muted-foreground truncate px-1">
                              {file.name}
                            </p>
                          </div>
                        </div>
                      )}
                      <AppButton
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => handleRemoveCommentAttachment(index)}
                      >
                        <X className="h-3 w-3" />
                      </AppButton>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </form>
        </div>

        {/* Comments Section */}
        {commentsLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-3"></div>
            <p className="text-muted-foreground">Loading comments...</p>
          </div>
        ) : organizedComments.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground text-lg font-medium mb-1">
              No comments yet
            </p>
            <p className="text-muted-foreground text-sm">
              Be the first to add a comment and start the conversation!
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {organizedComments.map((comment) => renderComment(comment))}
          </div>
        )}
      </div>
    </div>
  );
}