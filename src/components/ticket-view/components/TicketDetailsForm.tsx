"use client";

import * as React from "react";
import {
  AppButton,
  AppInput,
  AppTextarea,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui-toolkit";
import { type Ticket } from "@/lib/demo/types";

interface TicketDetailsFormProps {
  ticket: Ticket;
  editDialogOpen: boolean;
  onEditDialogClose: () => void;
  editSubject: string;
  editDescription: string;
  onSubjectChange: (subject: string) => void;
  onDescriptionChange: (description: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  updating: boolean;
}

export default function TicketDetailsForm({
  ticket,
  editDialogOpen,
  onEditDialogClose,
  editSubject,
  editDescription,
  onSubjectChange,
  onDescriptionChange,
  onSubmit,
  updating,
}: TicketDetailsFormProps) {
  return (
    <Dialog open={editDialogOpen} onOpenChange={onEditDialogClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Ticket</DialogTitle>
          <DialogDescription>
            Update the ticket details below.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={onSubmit} className="space-y-4">
          <div>
            <label className="text-sm font-medium">Subject</label>
            <AppInput
              value={editSubject}
              onChange={(e) => onSubjectChange(e.target.value)}
              placeholder="Ticket subject..."
              required
            />
          </div>

          <div>
            <label className="text-sm font-medium">Description</label>
            <AppTextarea
              value={editDescription}
              onChange={(e) => onDescriptionChange(e.target.value)}
              placeholder="Ticket description..."
              rows={4}
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <AppButton variant="outline" onClick={onEditDialogClose}>
              Cancel
            </AppButton>
            <AppButton type="submit" loading={updating}>
              Update Ticket
            </AppButton>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}