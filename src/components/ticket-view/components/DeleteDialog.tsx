"use client";

import { Trash2 } from "lucide-react";
import {
  AppB<PERSON>on,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui-toolkit";

interface DeleteDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  loading: boolean;
  title?: string;
  description?: string;
}

export default function DeleteDialog({
  open,
  onClose,
  onConfirm,
  loading,
  title = "Delete Item",
  description = "Are you sure you want to delete this item? This action cannot be undone.",
}: DeleteDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>
            {description}
          </DialogDescription>
        </DialogHeader>
        <div className="flex justify-end space-x-2 pt-4">
          <AppButton variant="outline" onClick={onClose}>
            Cancel
          </AppButton>
          <AppButton
            variant="destructive"
            onClick={onConfirm}
            loading={loading}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </AppButton>
        </div>
      </DialogContent>
    </Dialog>
  );
}