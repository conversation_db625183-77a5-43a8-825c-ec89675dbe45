"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, EyeOff, Link, Plus, Edit } from "lucide-react";
import { toast } from "sonner";
import {
  AppButton,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui-toolkit";
import { type Ticket } from "@/lib/demo/types";

interface TicketHeaderProps {
  ticket: Ticket;
  isWatching: boolean;
  onToggleWatch: () => void;
  onEditClick?: () => void;
  getStatusIcon: (status: Ticket["status"]) => React.ReactNode;
  getStatusStyle: (status: Ticket["status"]) => React.CSSProperties;
  getPriorityColor: (priority: Ticket["priority"]) => string;
}

export default function TicketHeader({
  ticket,
  isWatching,
  onToggleWatch,
  onEditClick,
  getStatusIcon,
  getStatusStyle,
  getPriorityColor,
}: TicketHeaderProps) {
  const router = useRouter();

  const copyTicketUrl = () => {
    console.log("ticket",ticket)
    // const url = `${window.location.origin}/tickets/${ticket.id}`;
    const url = `${window.location.origin}/tickets?ticket=${ticket?.number}`;
    navigator.clipboard.writeText(url);
    toast.success("Ticket URL copied to clipboard");
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <div>
          <div className="flex items-center space-x-3">
            <h1 className="text-2xl font-bold">{ticket.number}</h1>
            <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border" style={getStatusStyle(ticket.status)}>
              {getStatusIcon(ticket.status)}
              <span className="ml-1 capitalize">{ticket.status.replace("_", " ")}</span>
            </div>
            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(ticket.priority)}`}>
              <span className="capitalize">{ticket.priority}</span>
            </div>
          </div>
          <p className="text-muted-foreground mt-1">{ticket.subject}</p>
          <p className="text-sm text-muted-foreground mt-1">
            Requested by: <span className="font-medium">{ticket.requester.name}</span> ({ticket.requester.email})
          </p>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <AppButton
              variant="outline"
              size="sm"
              onClick={onToggleWatch}
            >
              {isWatching ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </AppButton>
          </TooltipTrigger>
          <TooltipContent>
            {isWatching ? "Stop watching" : "Watch ticket"}
          </TooltipContent>
        </Tooltip>

        {onEditClick && (
          <Tooltip>
            <TooltipTrigger asChild>
              <AppButton
                variant="outline"
                size="sm"
                onClick={onEditClick}
              >
                <Edit className="h-4 w-4" />
              </AppButton>
            </TooltipTrigger>
            <TooltipContent>
              Edit Details
            </TooltipContent>
          </Tooltip>
        )}

        <Tooltip>
          <TooltipTrigger asChild>
            <AppButton
              variant="outline"
              size="sm"
              onClick={copyTicketUrl}
            >
              <Link className="h-4 w-4" />
            </AppButton>
          </TooltipTrigger>
          <TooltipContent>
            Copy ticket URL
          </TooltipContent>
        </Tooltip>

        {!ticket.parentTicketId && (
          <Tooltip>
            <TooltipTrigger asChild>
              <AppButton
                variant="outline"
                size="sm"
                onClick={() => {
                  const params = new URLSearchParams({
                    parentId: ticket.id,
                    parentTitle: ticket.subject
                  });
                  router.push(`/create?${params.toString()}`);
                }}
              >
                <Plus className="h-4 w-4" />
              </AppButton>
            </TooltipTrigger>
            <TooltipContent>
              Create Sub Ticket
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </div>
  );
}