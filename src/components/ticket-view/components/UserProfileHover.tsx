"use client";

import * as React from "react";
import { Calendar, Mail, User } from "lucide-react";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui-toolkit";
import { getFullDate } from "@/lib/utils";
import type { CommentAuthor } from "@/types/api";

interface UserProfileHoverProps {
  user: CommentAuthor;
  children: React.ReactNode;
}

export function UserProfileHover({ user, children }: UserProfileHoverProps) {
  const [isOpen, setIsOpen] = React.useState(false);

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Mock join date - in real app this would come from user data
  const mockJoinDate = new Date(2023, 0, 15).toISOString();

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <div
          className="cursor-pointer hover:bg-muted/50 rounded-sm px-1 py-0.5 transition-colors"
          onClick={() => setIsOpen(!isOpen)}
        >
          {children}
        </div>
      </PopoverTrigger>
      <PopoverContent 
        className="w-80 p-4 shadow-lg border" 
        side="bottom" 
        align="start"
      >
        <div className="space-y-4">
          {/* User Header */}
          <div className="flex items-center space-x-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={user.avatar} />
              <AvatarFallback className="text-base font-medium">
                {getInitials(user.name)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <h4 className="text-base font-semibold text-foreground truncate">
                {user.name}
              </h4>
              <p className="text-sm text-muted-foreground">Team Member</p>
            </div>
          </div>

          {/* User Details */}
          <div className="space-y-3 border-t pt-3">
            {user.email && (
              <div className="flex items-center space-x-2 text-sm">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-foreground">{user.email}</span>
              </div>
            )}
            
            <div className="flex items-center space-x-2 text-sm">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                Joined {getFullDate(mockJoinDate)}
              </span>
            </div>
            
            <div className="flex items-center space-x-2 text-sm">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">{user.role || 'Team Member'}</span>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="border-t pt-3">
            <div className="flex space-x-2">
              <button className="flex-1 text-xs bg-primary text-primary-foreground hover:bg-primary/90 px-3 py-2 rounded-md transition-colors">
                View Profile
              </button>
              <button className="flex-1 text-xs border border-input hover:bg-accent hover:text-accent-foreground px-3 py-2 rounded-md transition-colors">
                Send Message
              </button>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}