"use client";

import * as React from "react";
import {
  <PERSON>pp<PERSON><PERSON>,
  App<PERSON><PERSON><PERSON>ontent,
  App<PERSON><PERSON><PERSON><PERSON><PERSON>,
  AppCardTitle,
} from "@/components/ui-toolkit";
import { useGetTicketHistoryQuery } from "@/services/api/tickets";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";

interface HistoryProps {
  ticketId: string;
}

function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInHours < 24) return `${diffInHours}h ago`;
  if (diffInDays < 7) return `${diffInDays}d ago`;
  return date.toLocaleDateString();
}

export default function History({ ticketId }: HistoryProps) {
  const [page, setPage] = React.useState(1);
  const [allHistory, setAllHistory] = React.useState<any[]>([]);
  const { data, isLoading, isFetching, error } = useGetTicketHistoryQuery({
    ticketKey: ticketId,
    page,
    size: 20,
  });

  React.useEffect(() => {
    if (data?.data) {
      if (page === 1) {
        setAllHistory(data.data);
      } else {
        setAllHistory(prev => [...prev, ...data.data]);
      }
    }
  }, [data, page]);

  const pagination = data?.pagination;
  const hasMore = pagination ? page * 20 < pagination.total : false;

  const loadMore = () => {
    if (hasMore && !isFetching) {
      setPage(prev => prev + 1);
    }
  };

  if (isLoading && page === 1) {
    return (
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Activity History</AppCardTitle>
        </AppCardHeader>
        <AppCardContent>
          <p className="text-muted-foreground text-center py-8">Loading history...</p>
        </AppCardContent>
      </AppCard>
    );
  }

  if (error && page === 1) {
    return (
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Activity History</AppCardTitle>
        </AppCardHeader>
        <AppCardContent>
          <p className="text-muted-foreground text-center py-8">Failed to load history.</p>
        </AppCardContent>
      </AppCard>
    );
  }

  return (
    <AppCard>
      <AppCardHeader>
        <AppCardTitle>Activity History</AppCardTitle>
      </AppCardHeader>
      <AppCardContent>
        <TooltipProvider>
          {allHistory.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">
              No activity recorded yet.
            </p>
          ) : (
            <div className="space-y-3">
              {allHistory.map((entry, index) => {
                const date = new Date(entry.timestamp);
                return (
                  <div key={`${entry.timestamp}-${index}`} className="flex items-start space-x-3">
                    <div className="flex-1 min-w-0">
                      <div className="text-sm text-muted-foreground">
                        {entry.message}
                      </div>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="text-xs text-muted-foreground cursor-pointer">
                            {formatRelativeTime(date)}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent side="top">
                          <p>{date.toLocaleString()}</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                );
              })}
              {hasMore && (
                <div className="text-center pt-4">
                  <Button
                    variant="outline"
                    onClick={loadMore}
                    disabled={isFetching}
                  >
                    {isFetching ? 'Loading...' : 'Load More'}
                  </Button>
                </div>
              )}
            </div>
          )}
        </TooltipProvider>
      </AppCardContent>
    </AppCard>
  );
}