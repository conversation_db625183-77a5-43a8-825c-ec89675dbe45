"use client";

import * as React from "react";
import { Clock, Edit, Timer, Plus, ChevronDownIcon, CheckIcon } from "lucide-react";
import moment from "moment";
import {
  AppCard,
  AppCardContent,
  AppCard<PERSON>eader,
  AppCardTitle,
  AppButton,
  AppBadge,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
  AppInput,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui-toolkit";
import { ScrollArea } from "@/components/ui/scroll-area";
import { type Ticket, type User } from "@/lib/demo/types";
import axiosInstance from "@/lib/axios";
import { useGetDepartmentsQuery } from "@/redux/slices/departments/departmentSlice";
import { useGetStatusesQuery, type Status } from "@/redux/slices/statuses";
import { useGetUsersQuery } from "@/redux/slices/users/userSlice";

interface TicketSidebarProps {
  ticket: Ticket;
  selectedAssignee: string;
  estimatedTime: number | null;
  timeEntries: Array<{
    id: string;
    user: User;
    hours: number;
    description: string;
    createdAt: Date;
  }>;
  worklogSummary?: {
    totalMinutes: number;
    billableMinutes: number;
    totalHours: number;
    billableHours: number;
    agentBreakdown: Array<{
      totalMinutes: number;
      billableMinutes: number;
      entryCount: number;
      agentId: string;
      agentName: string;
      agentRole: string;
    }>;
  };
  onStatusChange: (status: Ticket["status"]) => void;
  onPriorityChange: (priority: Ticket["priority"]) => void;
  onAssigneeChange: (assigneeId: string) => void;
  onDepartmentChange: (departmentId: string) => void;
  onTimeLogClick: () => void;
  onEstimateClick: () => void;
  onEditClick?: () => void;
  onStartDateChange: (date: Date | null) => void;
  onDueDateChange: (date: Date | null) => void;
  onEstimatedTimeChange: (time: string) => void;
  formatTimeString: (hours: number) => string;
  getTotalTimeLogged: () => number;
}

export default function TicketSidebar({
  ticket,
  selectedAssignee,
  estimatedTime,
  timeEntries,
  worklogSummary,
  onStatusChange,
  onPriorityChange,
  onAssigneeChange,
  onDepartmentChange,
  onTimeLogClick,
  onEstimateClick,
  onEditClick,
  onStartDateChange,
  onDueDateChange,
  onEstimatedTimeChange,
  formatTimeString,
  getTotalTimeLogged,
}: TicketSidebarProps) {
  const { data: departments = [], isLoading: departmentsLoading } = useGetDepartmentsQuery();
  const { data: users = [], isLoading: usersLoading } = useGetUsersQuery();

  const [open, setOpen] = React.useState(false);
  const [query, setQuery] = React.useState("");
  const [workSummaryOpen, setWorkSummaryOpen] = React.useState(false);
  const [estimatedTimeInput, setEstimatedTimeInput] = React.useState(
    estimatedTime ? formatTimeString(estimatedTime) : ''
  );

  // Update local state when estimatedTime prop changes
  React.useEffect(() => {
    setEstimatedTimeInput(estimatedTime ? formatTimeString(estimatedTime) : '');
  }, [estimatedTime, formatTimeString]);

  // Filter users based on search query
  const filteredUsers = React.useMemo(() => {
    if (!query.trim()) return users;
    
    return users.filter(user =>
      user.name.toLowerCase().includes(query.toLowerCase()) ||
      user.email.toLowerCase().includes(query.toLowerCase()) ||
      (user.department?.name && user.department.name.toLowerCase().includes(query.toLowerCase()))
    );
  }, [users, query]);

 

  const getTenantId = () => {
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const user = JSON.parse(userData);
        return user.tenantId || '';
      } catch { }
    }
    return localStorage.getItem('tenantId') || '';
  };

   const tenantId = getTenantId();
  const { data: globalStatuses = [] } = useGetStatusesQuery(tenantId, { skip: !tenantId });
console.log("globalStatuses",globalStatuses)
  // Check if due date is overdue
  const isDueDateOverdue = ticket.dueDate ? new Date(ticket.dueDate) < new Date() : false;

  return (
    <div className="space-y-6">
      <AppCard>
        <AppCardHeader>
          <AppCardTitle className="text-sm">Ticket Information</AppCardTitle>
        </AppCardHeader>
        <AppCardContent className="space-y-4">
          {/* Start Date */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Start Date</span>
            <div className="w-32">
              <input
                type="date"
                value={ticket.startDate ? (typeof ticket.startDate === 'string' ? new Date(ticket.startDate) : ticket.startDate).toISOString().split('T')[0] : ''}
                onChange={(e) => {
                  const date = e.target.value ? new Date(e.target.value) : null;
                  console.log('Start date changed:', date);
                  onStartDateChange(date);
                }}
                className="w-full px-2 py-1 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              />
            </div>
          </div>

          {/* Due Date */}
          <div className="flex items-center justify-between">
            <span className={`text-sm text-muted-foreground`}>Due Date</span>
            <div className="w-32">
              <input
                type="date"
                value={ticket.dueDate ? (typeof ticket.dueDate === 'string' ? new Date(ticket.dueDate) : ticket.dueDate).toISOString().split('T')[0] : ''}
                onChange={(e) => {
                  const date = e.target.value ? new Date(e.target.value) : null;
                  console.log('Due date changed:', date);
                  onDueDateChange(date);
                }}
                className={`w-full px-2 py-1 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                  isDueDateOverdue 
                    ? 'border-red-500 bg-red-50 text-red-700 focus:ring-red-500' 
                    : 'border-input bg-background focus:ring-ring'
                }`}
              />
            </div>
          </div>

          {/* Estimated Time */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Estimated Time</span>
            <AppInput
              type="text"
              value={estimatedTimeInput}
              onChange={(e) => setEstimatedTimeInput(e.target.value)}
              onBlur={() => onEstimatedTimeChange(estimatedTimeInput)}
              placeholder="e.g. 2h 30m"
              className="w-32 text-sm"
            />
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Department</span>
            <Select value={ticket.department?.id || 'unassigned'} onValueChange={onDepartmentChange}>
              <SelectTrigger className="w-50">
                <SelectValue placeholder="Unassigned" />
              </SelectTrigger>
              <SelectContent>

                {departments.filter(dept => dept.id).map((department) => (
                  <SelectItem key={department.id!} value={department.id!}>
                    {department.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Status</span>
            <Select value={ticket.status} onValueChange={onStatusChange}>
              <SelectTrigger className="w-50">
                <SelectValue>
                  {ticket.status && (
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-2 h-2 rounded-full border"
                        style={{ 
                          borderColor: globalStatuses.find(s => s.name === ticket.status)?.color,
                          backgroundColor: 'white'
                        }}
                      />
                      {ticket.status}
                    </div>
                  )}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {globalStatuses.map((status) => (
                  <SelectItem key={status._id} value={status.name}>
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-2 h-2 rounded-full border"
                        style={{ 
                          borderColor: status.color,
                          backgroundColor: 'white'
                        }}
                      />
                      {status.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Priority */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Priority</span>
            <Select value={ticket.priority} onValueChange={onPriorityChange}>
              <SelectTrigger className="w-50">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>

                <SelectItem value="Top Priority">Top Priority</SelectItem>
                <SelectItem value="Urgent">Urgent</SelectItem>
                <SelectItem value="Normal">Normal</SelectItem>
                <SelectItem value="Low">Low</SelectItem>
                <SelectItem value="Later">Later</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Assignee */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Assignee</span>
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <AppButton variant="outline" className="w-50 justify-between" disabled={usersLoading}>
                  {usersLoading ? (
                    "Loading..."
                  ) : selectedAssignee === "unassigned" ? (
                    "Unassigned"
                  ) : (
                    users.find((u) => u.id === selectedAssignee)?.name || "Select assignee"
                  )}
                  <ChevronDownIcon className="h-4 w-4 opacity-50" />
                </AppButton>
              </PopoverTrigger>
              <PopoverContent className="w-50 p-0">
                <div className="p-2">
                  <AppInput
                    placeholder="Search users by name, email, or department..."
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    className="h-8"
                  />
                </div>
                <ScrollArea className="h-32">
                  <div className="p-1">
                    <div
                      className="flex items-center px-2 py-1 cursor-pointer hover:bg-accent rounded"
                      onClick={() => {
                        onAssigneeChange("unassigned");
                        setOpen(false);
                        setQuery("");
                      }}
                    >
                      <CheckIcon className={`mr-2 h-4 w-4 ${selectedAssignee === "unassigned" ? "opacity-100" : "opacity-0"}`} />
                      Unassigned
                    </div>
                    {usersLoading ? (
                      <div className="px-2 py-1 text-sm text-muted-foreground">Loading users...</div>
                    ) : filteredUsers.length === 0 ? (
                      <div className="px-2 py-1 text-sm text-muted-foreground">
                        {query ? "No users found matching search" : "No users available"}
                      </div>
                    ) : (
                      filteredUsers.map((user) => (
                        <div
                          key={user.id}
                          className="flex items-center px-2 py-1 cursor-pointer hover:bg-accent rounded"
                          onClick={() => {
                            onAssigneeChange(user.id);
                            setOpen(false);
                            setQuery("");
                          }}
                        >
                          <CheckIcon className={`mr-2 h-4 w-4 ${selectedAssignee === user.id ? "opacity-100" : "opacity-0"}`} />
                          <div className="flex flex-col">
                            <span className="text-sm">{user.name}</span>
                            
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </ScrollArea>
              </PopoverContent>
            </Popover>
          </div>

          {/* Time Tracking */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Time Logged</span>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{formatTimeString(getTotalTimeLogged())}</span>
                <AppButton size="sm" variant="outline" onClick={onTimeLogClick}>
                  <Plus className="h-3 w-3" />
                </AppButton>
              </div>
            </div>
          </div>

          {/* Work Summary */}
          {/* {worklogSummary && (
            <div className="space-y-2">
              <div 
                className="flex items-center justify-between cursor-pointer hover:bg-accent/50 rounded px-1 py-1"
                onClick={() => setWorkSummaryOpen(!workSummaryOpen)}
              >
                <span className="text-sm text-muted-foreground">Work Summary</span>
                <ChevronDownIcon className={`h-4 w-4 opacity-50 transition-transform ${workSummaryOpen ? 'rotate-180' : ''}`} />
              </div>
              {workSummaryOpen && (
                <div className="space-y-1 text-xs bg-muted/30 rounded p-2">
                  <div className="flex justify-between">
                    <span>Total Time:</span>
                    <span className="font-medium">{formatTimeString(worklogSummary.totalHours)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Billable Time:</span>
                    <span className="font-medium">{formatTimeString(worklogSummary.billableHours)}</span>
                  </div>
                  <div className="pt-1 border-t">
                    <span className="text-xs font-medium text-muted-foreground">By Agent:</span>
                  </div>
                  {worklogSummary.agentBreakdown.map((agent) => (
                    <div key={agent.agentId} className="flex justify-between text-xs pl-2">
                      <span>{agent.agentName}:</span>
                      <span className="font-medium">{formatTimeString(agent.totalMinutes / 60)}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )} */}

          <Separator />

          {/* Requester */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Requester</span>
            <div className="flex items-center space-x-2">
              <Avatar className="h-5 w-5 border border-black">
                <AvatarImage src={ticket.requester.avatar} />
                <AvatarFallback>{(ticket.requester.name as string)[0]?.toUpperCase()}</AvatarFallback>
              </Avatar>
              <span className="text-sm">{ticket.requester.name}</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Opened</span>
            <span className="text-sm">{moment(ticket.createdAt).format('MMM DD, YYYY hh:mm A')}</span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Last updated</span>
            <span className="text-sm">{moment(ticket.updatedAt).format('MMM DD, YYYY hh:mm A')}</span>
          </div>



          {/* <Separator />

          <div className="space-y-2">
            <AppButton variant="outline" size="sm" className="w-full" onClick={onTimeLogClick}>
              <Clock className="h-4 w-4 mr-2" />
              Log Time
            </AppButton>
          </div> */}
        </AppCardContent>
      </AppCard>
    </div>
  );
}