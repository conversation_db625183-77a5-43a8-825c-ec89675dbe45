// Main exports for ticket-view module

// Component exports
export { default as Checklist } from './components/Checklist';
export { default as Comments } from './components/Comments';
export { default as History } from './components/History';
export { default as TicketSidebar } from './components/TicketSidebar';
export { default as TicketDetailsForm } from './components/TicketDetailsForm';
export { default as TimeTracking } from './components/TimeTracking';
export { default as TicketHeader } from './components/TicketHeader';
export { default as DeleteDialog } from './components/DeleteDialog';

// Type exports (with explicit naming to avoid conflicts)
export type {
  TicketDetailsViewProps,
  ChecklistItem,
  Checklist as ChecklistType,
  ActivityLogEntry,
  TimeEntry,
  TimeFormData,
  EditFormData,
  CommentFormData,
  TicketCache
} from './types';