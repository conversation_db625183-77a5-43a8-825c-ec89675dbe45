import { type Ticket, type Comment, type User, type CannedResponse } from "@/lib/demo/types";

export interface TicketDetailsViewProps {
  ticketId: string;
  onTicketUpdate?: (ticket: Ticket) => void;
  onTicketDelete?: (ticketId: string) => void;
}

export interface ChecklistItem {
  itemId: string;
  text: string;
  isDone: boolean;
  isMandatory: boolean;
  assigneeId?: string;
  dueDate?: string;
  note?: string;
  linkedSubTaskId?: string;
  isEditing?: boolean;
}

export interface Checklist {
  _id: string;
  tenantId: string;
  ticketId?: string;
  subTaskId?: string;
  title?: string;
  items: ChecklistItem[];
  doneCount: number;
  totalCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface ActivityLogEntry {
  id: string;
  action: string;
  user: User;
  timestamp: Date;
  details: string;
}

export interface TimeEntry {
  id: string;
  user: User;
  hours: number;
  description: string;
  createdAt: Date;
}

export interface TimeFormData {
  timeInput: string;
  description: string;
}

export interface EditFormData {
  subject: string;
  status: Ticket["status"];
  priority: Ticket["priority"];
  description: string;
}

export interface CommentFormData {
  body: string;
  type: "public" | "internal";
}

export interface TicketCache {
  ticket: Ticket;
  comments: Comment[];
  availableUsers: User[];
  cannedResponses: CannedResponse[];
  loadedAt: number;
}