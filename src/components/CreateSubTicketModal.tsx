'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Plus } from 'lucide-react'

interface CreateSubTicketModalProps {
  trigger?: React.ReactNode
  onCreateSubTicket: (subTicket: {
    title: string
    assignee?: string
    canDescriptionAndTitleChange?: boolean
  }) => void
}

export function CreateSubTicketModal({ trigger, onCreateSubTicket }: CreateSubTicketModalProps) {
  const [open, setOpen] = useState(false)
  const [title, setTitle] = useState('')
  const [assignee, setAssignee] = useState('')
  const [canDescriptionAndTitleChange, setCanDescriptionAndTitleChange] = useState(true)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!title.trim()) return

    onCreateSubTicket({
      title: title.trim(),
      assignee: assignee.trim() || undefined,
      canDescriptionAndTitleChange,
    })

    // Reset form
    setTitle('')
    setAssignee('')
    setCanDescriptionAndTitleChange(true)
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Plus className="w-4 h-4 mr-2" />
            Add Subtask
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>Create New Subtask</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="sub-title">Title *</Label>
            <Input
              id="sub-title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter subtask title"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="sub-assignee">Assignee</Label>
            <Input
              id="sub-assignee"
              value={assignee}
              onChange={(e) => setAssignee(e.target.value)}
              placeholder="Enter assignee name (optional)"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="sub-canDescriptionAndTitleChange"
              checked={canDescriptionAndTitleChange}
              onCheckedChange={(checked) => setCanDescriptionAndTitleChange(checked as boolean)}
            />
            <Label htmlFor="sub-canDescriptionAndTitleChange" className="text-sm">
              Allow editing title and description after creation
            </Label>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={!title.trim()}>
              Create Subtask
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}