'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Ticket, Plus, MoreHorizontal } from 'lucide-react'
import { cn } from '@/lib/utils'

interface SubTicket {
  id: string
  title: string
  completed: boolean
  assignee?: string
}

interface Ticket {
  id: string
  title: string
  ticketKey?: string
  status: 'To Do' | 'In Progress' | 'Done'
  priority: 'Low' | 'Medium' | 'High'
  assignee?: string
  requester?: string
  subTickets: SubTicket[]
}

interface KanbanColumn {
  id: string
  title: string
  tickets: Ticket[]
}

interface KanbanBoardProps {
  columns: KanbanColumn[]
  onCreateTicket?: (columnId: string) => void
  onTicketClick?: (ticket: Ticket) => void
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Done': return 'bg-green-100 text-green-800'
    case 'In Progress': return 'bg-blue-100 text-blue-800'
    case 'To Do': return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'High': return 'bg-red-100 text-red-800'
    case 'Medium': return 'bg-yellow-100 text-yellow-800'
    case 'Low': return 'bg-green-100 text-green-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getInitials = (name?: string) => {
  if (!name) return 'U'
  return name.split(' ').map(n => n[0]).join('').toUpperCase()
}

export function KanbanBoard({ columns, onCreateTicket, onTicketClick }: KanbanBoardProps) {
  return (
    <div className="h-full overflow-hidden">
      <div 
        className="kanban-scroll flex gap-2 px-2 pb-4 h-full overflow-x-scroll overflow-y-hidden"
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
        }}
      >
        <style dangerouslySetInnerHTML={{
          __html: `
            .kanban-scroll::-webkit-scrollbar {
              display: none !important;
            }
            .kanban-scroll {
              -ms-overflow-style: none;
              scrollbar-width: none;
            }
          `
        }} />
        {columns.map((column) => (
          <div key={column.id} className="flex-shrink-0 w-56 sm:w-64">
            <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-3 h-full flex flex-col">
              <div className="flex items-center justify-between mb-3 flex-shrink-0">
                <div className="flex items-center space-x-2">
                  <h3 className="font-semibold text-gray-900 text-xs">{column.title}</h3>
                  <Badge variant="secondary" className="text-xs bg-gray-100 text-gray-600 px-1.5 py-0.5 text-[10px]">
                    {column.tickets.length}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2 flex-1 overflow-y-auto">
                {column.tickets.map((ticket) => (
                  <Card
                    key={ticket.id}
                    className="cursor-pointer hover:shadow-md transition-all duration-200 border border-gray-200 hover:border-gray-300 group"
                    onClick={() => onTicketClick?.(ticket)}
                  >
                    <CardContent className="">
                      <div className="space-y-1">
                        {ticket.ticketKey && (
                          <div className="text-[8px] text-gray-500 font-medium uppercase tracking-wide leading-none">
                            {ticket.ticketKey}
                          </div>
                        )}
                        <div className="flex items-start justify-between">
                          <h4 className="font-medium text-xs text-gray-900 line-clamp-2 flex-1 leading-tight">
                            {ticket.title}
                          </h4>
                          <Button variant="ghost" size="sm" className="h-3 w-3 p-0 opacity-0 group-hover:opacity-100 transition-opacity ml-0.5">
                            <MoreHorizontal className="w-2.5 h-2.5" />
                          </Button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-1">
                            <Badge className={cn("text-[9px] px-0.5 py-0", getPriorityColor(ticket.priority))}>
                              {ticket.priority}
                            </Badge>
                            {ticket.assignee && (
                              <Avatar className="h-3 w-3">
                                <AvatarFallback className="text-[8px] bg-blue-100 text-blue-800">
                                  {getInitials(ticket.assignee)}
                                </AvatarFallback>
                              </Avatar>
                            )}
                          </div>
                          {ticket.requester && !ticket.assignee && (
                            <div className="text-[8px] text-gray-500 truncate max-w-[50px]">
                              {ticket.requester}
                            </div>
                          )}
                        </div>

                        {ticket.subTickets.length > 0 && (
                          <div className="flex items-center space-x-0.5 text-[8px] text-gray-500">
                            <Ticket className="w-2 h-2" />
                            <span>
                              {ticket.subTickets.filter(st => st.completed).length}/{ticket.subTickets.length}
                            </span>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {column.tickets.length === 0 && (
                  <div className="text-center py-8 text-gray-400 flex-1 flex items-center justify-center">
                    <div>
                      <Ticket className="w-8 h-8 mx-auto mb-2 opacity-30" />
                      <p className="text-xs">No tickets yet</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}