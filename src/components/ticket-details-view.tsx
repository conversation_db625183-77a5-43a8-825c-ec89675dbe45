"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { 
  Edit, 
  Trash2, 
  MessageSquare, 
  Clock, 
  UserIcon, 
  Tag, 
  AlertCircle, 
  CheckCircle2, 
  XCircle, 
  Pause, 
  Play, 
  MoreHorizontal,
  UserCheck,
  Eye,
  EyeOff,
  Link,
  Timer,
  Plus,
  X
} from "lucide-react";
import { toast } from "sonner";
import Image from "next/image";
import {
  AppCard,
  AppCardContent,
  AppCardHeader,
  AppCardTitle,
  AppButton,
  AppBadge,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Separator,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  AppInput,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  AppTextarea,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
  Checkbox,
} from "@/components/ui-toolkit";
import { Checklist, Comments, History, TicketSidebar, TicketDetailsForm, TimeTracking, TicketHeader, DeleteDialog } from "./ticket-view";
import { ticketService, userService } from "@/lib/demo/api";
import { useGetTicketByIdQuery, useUpdateTicketMutation } from "@/services/api/tickets";
import { useGetDepartmentsQuery } from "@/redux/slices/departments/departmentSlice";
import { useGetStatusesQuery } from "@/redux/slices/statuses";
import { type Ticket, type Comment, type User } from "@/lib/demo/types";
import axiosInstance from "@/lib/axios";
import { ApiResponse } from "@/types/api";
import ImageViewer from "./ImageViewer";
import { useCreateWorklogMutation, useDeleteWorklogMutation, useGetWorklogsQuery, useGetWorklogSummaryQuery, useUpdateWorklogMutation } from "@/services/api/worklogs";

interface TicketDetailsViewProps {
  ticketId: string;
  onTicketUpdate?: (ticket: Ticket) => void;
  onTicketDelete?: (ticketId: string) => void;
}

/**
 * Get a single ticket by ID or ticket key
 * @param ticketId - Either the ticket's MongoDB ObjectId or its ticketKey (e.g., ABC-2509-00001)
 * @returns Promise<Ticket | null>
 */
const getTicketById = async (ticketId: string): Promise<Ticket | null> => {
  try {
    const response = await axiosInstance.get(`/tickets/${ticketId}`);
    const responseData = response.data as { success: boolean; data?: Ticket; error?: string };
    
    if (responseData.success && responseData.data) {
      return responseData.data;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting ticket:', error);
    throw error;
  }
};

// Global cache to store loaded tickets and prevent re-loading
const ticketCache = new Map<string, {
  ticket: Ticket;
  comments: Comment[];
  availableUsers: User[];
  loadedAt: number;
}>();

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export default function TicketDetailsView({ ticketId, onTicketUpdate, onTicketDelete }: TicketDetailsViewProps) {
  const router = useRouter();
  const [ticket, setTicket] = React.useState<Ticket | null>(null);
  const [comments, setComments] = React.useState<Comment[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [editDialogOpen, setEditDialogOpen] = React.useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [assigneeDialogOpen, setAssigneeDialogOpen] = React.useState(false);
  const [cannedResponseDialogOpen, setCannedResponseDialogOpen] = React.useState(false);

  // Check if ticketId looks like a ticketKey (e.g., CYR250900002)
  const isTicketKey = /^[A-Z]+\d+$/.test(ticketId);
  
  // Use real API for ticketKey format, skip for demo IDs
  const { data: realTicketData, isLoading: isRealTicketLoading } = useGetTicketByIdQuery(
    ticketId,
    { skip: !isTicketKey }
  );

  const [updateTicket, { isLoading: isUpdatingTicket }] = useUpdateTicketMutation();
  const { data: departments = [] } = useGetDepartmentsQuery();
  const [createWorklog, { isLoading: isCreatingWorklog }] = useCreateWorklogMutation();
  const [updateWorklog] = useUpdateWorklogMutation();
  const [deleteWorklog] = useDeleteWorklogMutation();
  const { data: worklogsData, isLoading: worklogsLoading, refetch: refetchWorklogs } = useGetWorklogsQuery(
    { ticketId: (ticket as any)?._id || ticketId },
    { skip: !ticket }
  );
  const { data: worklogSummary } = useGetWorklogSummaryQuery(
    (ticket as any)?._id || ticketId,
    { skip: !ticket }
  );

  // Fetch statuses from store
  const getTenantId = () => {
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const user = JSON.parse(userData);
        return user.tenantId || '';
      } catch { }
    }
    return localStorage.getItem('tenantId') || '';
  };

  const tenantId = getTenantId();
  const { data: statuses = [] } = useGetStatusesQuery(tenantId, { skip: !ticket });

  const [updating, setUpdating] = React.useState(false);
  const [deleting, setDeleting] = React.useState(false);
  const [isWatching, setIsWatching] = React.useState(false);
  
  // Edit form state
  const [editSubject, setEditSubject] = React.useState("");
  const [editDescription, setEditDescription] = React.useState("");

  // Enhanced features state
  const [availableUsers, setAvailableUsers] = React.useState<User[]>([]);
  const [selectedAssignee, setSelectedAssignee] = React.useState<string>("");
  const [activeTab, setActiveTab] = React.useState("details");
  
  // Time tracking state
  const [timeLogDialogOpen, setTimeLogDialogOpen] = React.useState(false);
  const [timeHistoryDialogOpen, setTimeHistoryDialogOpen] = React.useState(false);
  const [timeEntries, setTimeEntries] = React.useState<Array<{
    id: string;
    user: User;
    hours: number;
    description: string;
    createdAt: Date;
  }>>([]);
  const [timeForm, setTimeForm] = React.useState({
    timeInput: '',
    description: '',
    endDateTime: null as Date | null,
  });
  const [editingWorklogId, setEditingWorklogId] = React.useState<string | null>(null);
  const [estimatedTime, setEstimatedTime] = React.useState<number | null>(null);
  const [estimateDialogOpen, setEstimateDialogOpen] = React.useState(false);
  const [newEstimate, setNewEstimate] = React.useState('');
  const [imageViewerOpen, setImageViewerOpen] = React.useState(false);
  const [currentImageIndex, setCurrentImageIndex] = React.useState(0);
  const [imageViewerImages, setImageViewerImages] = React.useState<any[]>([]);
  const [attachments, setAttachments] = React.useState<File[]>([]);
  const [uploadingAttachments, setUploadingAttachments] = React.useState(false);

  // Convert worklogs to timeEntries format
  React.useEffect(() => {
    if (worklogsData?.data && availableUsers.length > 0) {
      const convertedEntries = worklogsData.data.map(worklog => {
        const user = availableUsers.find(u => u.id === worklog.agentId) || {
          id: worklog.agentId,
          name: 'Unknown User',
          email: '',
          role: {
            id: 'role-agent',
            name: 'Agent',
            type: 'system' as const,
            userType: 'agent' as const,
            permissions: [],
            isActive: true,
          },
          department: {
            id: 'dept-1',
            name: 'Support',
            status: 'active' as const,
            memberCount: 0
          },
          status: 'active' as const,
        };
        return {
          id: worklog._id,
          user,
          hours: worklog.minutes / 60,
          description: worklog.note || 'Work logged',
          createdAt: new Date(worklog.createdAt),
        };
      });
      setTimeEntries(convertedEntries);
    }
  }, [worklogsData, availableUsers]);

  // Utility functions
  const parseTimeString = (timeStr: string): number => {
    if (!timeStr.trim()) return 0;
    
    const normalized = timeStr.toLowerCase().replace(/\s+/g, '');
    let totalHours = 0;
    
    const daysMatch = normalized.match(/(\d+(?:\.\d+)?)d/);
    if (daysMatch) {
      totalHours += parseFloat(daysMatch[1]) * 8;
    }
    
    const hoursMatch = normalized.match(/(\d+(?:\.\d+)?)h/);
    if (hoursMatch) {
      totalHours += parseFloat(hoursMatch[1]);
    }
    
    const minutesMatch = normalized.match(/(\d+(?:\.\d+)?)m/);
    if (minutesMatch) {
      totalHours += parseFloat(minutesMatch[1]) / 60;
    }
    
    if (!hoursMatch && !minutesMatch && !daysMatch) {
      const numericValue = parseFloat(normalized);
      if (!isNaN(numericValue)) {
        totalHours = numericValue;
      }
    }
    
    return Math.round(totalHours * 100) / 100;
  };

  const formatTimeString = (hours: number): string => {
    if (hours === 0) return '0m';
    
    const days = Math.floor(hours / 8);
    const remainingHours = hours % 8;
    const wholeHours = Math.floor(remainingHours);
    const minutes = Math.round((remainingHours - wholeHours) * 60);
    
    let result = '';
    
    if (days > 0) {
      result += `${days}d`;
      if (wholeHours > 0 || minutes > 0) result += ' ';
    }
    
    if (wholeHours > 0) {
      result += `${wholeHours}h`;
      if (minutes > 0) result += ' ';
    }
    
    if (minutes > 0 && days === 0) {
      result += `${minutes}m`;
    }
    
    if (result === '' && hours > 0) {
      const totalMinutes = Math.round(hours * 60);
      result = `${totalMinutes}m`;
    }
    
    return result || '0m';
  };

  const getTotalTimeLogged = () => {
    // Use worklog summary if available, otherwise calculate from timeEntries
    if (worklogSummary?.data?.totalHours) {
      return worklogSummary.data.totalHours;
    }
    return timeEntries.reduce((total, entry) => total + entry.hours, 0);
  };

  const getStatusIcon = (status: Ticket["status"]) => {
    switch (status) {
      case "new": return <AlertCircle className="h-4 w-4" />;
      case "triage": return <Clock className="h-4 w-4" />;
      case "in_progress": return <Play className="h-4 w-4" />;
      case "waiting": return <Pause className="h-4 w-4" />;
      case "resolved": return <CheckCircle2 className="h-4 w-4" />;
      case "closed": return <XCircle className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getStatusStyle = (status: Ticket["status"]): React.CSSProperties => {
    const color = getStatusColorFromStore(status);
    return {
      borderColor: color,
      color: color,
      backgroundColor: 'white'
    };
  };

  const getPriorityColor = (priority: Ticket["priority"]) => {
    switch (priority) {
      case "low": return "bg-gray-100 text-gray-800 border-gray-300";
      case "medium": return "bg-blue-100 text-blue-800 border-blue-300";
      case "high": return "bg-orange-100 text-orange-800 border-orange-300";
      case "urgent": return "bg-red-100 text-red-800 border-red-300";
      default: return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  // Helper function to get status color from store
  const getStatusColorFromStore = (statusName: string) => {
    const status = statuses.find(s => s.name === statusName);
    return status?.color || '#6b7280'; // Default gray color
  };

  // Status badge renderer
  const renderStatusBadge = (statusValue: string) => {
    const color = getStatusColorFromStore(statusValue);
    return (
      <span 
        className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors"
        style={{ 
          borderColor: color, 
          backgroundColor: 'white',
          color: color 
        }}
      >
        {statusValue.replace("_", " ")}
      </span>
    );
  };

  // Handle real API data
  React.useEffect(() => {
    if (isTicketKey && realTicketData && !isRealTicketLoading) {
      // Convert real API data to Ticket format
      const convertedTicket: Ticket = {
        id: realTicketData._id,
        number: realTicketData.ticketKey,
        subject: realTicketData.title,
        status: realTicketData.status as any,
        priority: realTicketData.priority as any,
        requester: realTicketData.requester ? {
          id: realTicketData.requester.userId,
          name: realTicketData.requester.name,
          email: realTicketData.requester.email,
          role: {
            id: 'user-role',
            name: 'User',
            type: 'system' as const,
            permissions: {
              tickets: { read: true, write: false, delete: false },
              users: { read: false, write: false, delete: false },
              settings: { read: false, write: false, delete: false },
              reports: { read: false, write: false, delete: false }
            }
          },
          status: 'active' as const,
          department: {
            id: 'dept-1',
            name: 'Support',
            status: 'active' as const,
            memberCount: 0
          }
        } : {
          id: 'unknown',
          name: 'Unknown',
          email: '',
          role: {
            id: 'user-role',
            name: 'User',
            type: 'system' as const,
            permissions: {
              tickets: { read: true, write: false, delete: false },
              users: { read: false, write: false, delete: false },
              settings: { read: false, write: false, delete: false },
              reports: { read: false, write: false, delete: false }
            }
          },
          status: 'active' as const,
          department: {
            id: 'dept-1',
            name: 'Support',
            status: 'active' as const,
            memberCount: 0
          }
        },
        assignee: realTicketData.assignee ? {
          id: realTicketData.assignee.userId,
          name: realTicketData.assignee.name,
          email: realTicketData.assignee.email,
          role: {
            id: 'user-role',
            name: 'User',
            type: 'system' as const,
            userType: 'agent' as const,
            permissions: [],
            isActive: true,
          },
          status: 'active' as const,
          department: {
            id: 'dept-1',
            name: 'Support',
            status: 'active' as const,
            memberCount: 0
          }
        } : undefined,
        department: realTicketData.department ? {
          id: realTicketData.department.id,
          name: realTicketData.department.name,
          status: 'active' as const,
          memberCount: 0
        } : {
          id: 'dept-1',
          name: 'Support',
          status: 'active' as const,
          memberCount: 0
        },
        category: undefined,
        tags: realTicketData.tags || [],
        createdAt: new Date(realTicketData.createdAt),
        updatedAt: new Date(realTicketData.updatedAt),
        description: realTicketData.description,
        resolveBy: realTicketData.resolveBy,
        estimatedTime: realTicketData.estimatedTime,
        startDate: (realTicketData as any).startDate ? new Date((realTicketData as any).startDate) : undefined,
        dueDate: (realTicketData as any).dueDate ? new Date((realTicketData as any).dueDate) : undefined,
        canDescriptionAndTitleChange: realTicketData.canDescriptionAndTitleChange,
        subTasks: (realTicketData as any).subTasks || [],
        mergedTickets: (realTicketData as any).mergedTickets || []
      };

      setTicket(convertedTicket);
      setComments([]); // No comments for now
      setLoading(false);

      // Initialize edit form
      setEditSubject(convertedTicket.subject);
      setEditDescription(convertedTicket.description || "");
      setSelectedAssignee(convertedTicket.assignee?.id || "unassigned");
      setEstimatedTime(convertedTicket.estimatedTime || null);

      // Load demo users and canned responses for the form
      loadDemoData();
      
      return;
    }
    
    // Set loading for real API calls
    if (isTicketKey && isRealTicketLoading) {
      setLoading(true);
      return;
    }
  }, [isTicketKey, realTicketData, isRealTicketLoading]);

  const loadDemoData = async () => {
    try {
      const [usersData] = await Promise.all([
        userService.getAll()
      ]);
      setAvailableUsers(usersData);
    } catch (error) {
      console.error("Failed to load demo data:", error);
    }
  };

  // Load ticket data with caching (for demo tickets)
  React.useEffect(() => {
    // Skip if this is a real ticketKey 
    if (isTicketKey) return;
    
    const loadTicketData = async () => {
      // Check cache first
      const cached = ticketCache.get(ticketId);
      if (cached && Date.now() - cached.loadedAt < CACHE_DURATION) {
        // Use cached data
        setTicket(cached.ticket);
        setComments(cached.comments);
        setAvailableUsers(cached.availableUsers);
        
        // Initialize edit form
        setEditSubject(cached.ticket.subject);
        setEditDescription(cached.ticket.description || "");
        setSelectedAssignee(cached.ticket.assignee?.id || "unassigned");
        
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const [ticketData, commentsData, usersData] = await Promise.all([
          ticketService.getById(ticketId),
          ticketService.getComments(ticketId),
          userService.getAll()
        ]);
        
        // Cache the data
        if (ticketData) {
          ticketCache.set(ticketId, {
            ticket: ticketData,
            comments: commentsData,
            availableUsers: usersData,
            loadedAt: Date.now()
          });
        }
        
        setTicket(ticketData);
        setComments(commentsData);
        setAvailableUsers(usersData);
        
        // Initialize edit form
        if (ticketData) {
          setEditSubject(ticketData.subject);
          setEditDescription(ticketData.description || "");
          setSelectedAssignee(ticketData.assignee?.id || "unassigned");
        }
      } catch (error) {
        console.error("Failed to load ticket:", error);
        toast.error("Failed to load ticket details");
      } finally {
        setLoading(false);
      }
    };

    if (ticketId) {
      loadTicketData();
    }
  }, [ticketId]);

  // Event handlers
  const handleUpdateTicket = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!ticket) return;

    setUpdating(true);
    try {
      const result = await updateTicket({
        ticketKey: (ticket as any)._id || ticket.id || ticketId,
        updates: {
          title: editSubject.trim(),
          description: editDescription.trim(),
        },
      }).unwrap();
      
      if (result) {
        setTicket(result as any);
        
        // Update cache
        const cached = ticketCache.get(ticketId);
        if (cached) {
          ticketCache.set(ticketId, { ...cached, ticket: result as any });
        }
        
        if (onTicketUpdate) {
          onTicketUpdate(result as any);
        }
        
        toast.success("Ticket updated successfully");
        setEditDialogOpen(false);
      } else {
        toast.error("Failed to update ticket");
      }
    } catch (error) {
      console.error("Failed to update ticket:", error);
      toast.error("Failed to update ticket");
    } finally {
      setUpdating(false);
    }
  };

  const handleQuickStatusUpdate = async (newStatus: Ticket["status"]) => {
    if (!ticket || newStatus === ticket.status) return;
    console.log("newStatus", newStatus);
    console.log("ticket.status", ticket.status);
    console.log("available statuses:", statuses);
    
    // Find the status object from the statuses list
    const statusObj = statuses.find(s => s.name === newStatus);
    console.log("statusObj found:", statusObj);
    
    if (!statusObj) {
      console.error("Status not found for:", newStatus);
      toast.error("Status not found");
      return;
    }
    
    let statusToUpdate = {
      id : statusObj._id,
      name : newStatus
    };
    try {
      const result = await updateTicket({
        ticketKey: (ticket as any)._id || ticket.id || ticketId,
        updates: { status: statusToUpdate as any },
      }).unwrap();
      
      if (result) {
        setTicket(result as any);
        
        // Update cache
        const cached = ticketCache.get(ticketId);
        if (cached) {
          ticketCache.set(ticketId, { ...cached, ticket: result as any });
        }
        
        if (onTicketUpdate) {
          onTicketUpdate(result as any);
        }
        
        toast.success("Status updated successfully");
      } else {
        toast.error("Failed to update status");
      }
    } catch (error) {
      console.error("Failed to update status:", error);
      toast.error("Failed to update status");
    }
  };

  const handleQuickPriorityUpdate = async (newPriority: Ticket["priority"]) => {
    if (!ticket || newPriority === ticket.priority) return;
    
    try {
      const result = await updateTicket({
        ticketKey: (ticket as any)._id || ticket.id || ticketId,
        updates: { priority: newPriority as any },
      }).unwrap();
      
      if (result) {
        setTicket(result as any);
        
        // Update cache
        const cached = ticketCache.get(ticketId);
        if (cached) {
          ticketCache.set(ticketId, { ...cached, ticket: result as any });
        }
        
        if (onTicketUpdate) {
          onTicketUpdate(result as any);
        }
        
        toast.success("Priority updated successfully");
      } else {
        toast.error("Failed to update priority");
      }
    } catch (error) {
      console.error("Failed to update priority:", error);
      toast.error("Failed to update priority");
    }
  };

  const handleDeleteTicket = async () => {
    if (!ticket) return;

    setDeleting(true);
    try {
      await ticketService.delete((ticket as any)._id || ticket.id || ticketId);
      
      // Remove from cache
      ticketCache.delete(ticketId);
      
      if (onTicketDelete) {
        onTicketDelete(ticket.id);
      }
      
      toast.success("Ticket deleted successfully");
    } catch (error) {
      console.error("Failed to delete ticket:", error);
      toast.error("Failed to delete ticket");
    } finally {
      setDeleting(false);
    }
  };

  const handleQuickAssigneeUpdate = async (newAssigneeId: string) => {
    if (!ticket) return;
    
    const currentAssigneeId = ticket.assignee?.id || "unassigned";
    
    if (newAssigneeId === currentAssigneeId) return;
    
    try {
      const result = await updateTicket({
        ticketKey: (ticket as any)._id || ticket.id || ticketId,
        updates: { assigneeId: newAssigneeId === "unassigned" ? undefined : newAssigneeId },
      }).unwrap();
      
      if (result) {
        setTicket(result as any);
        setSelectedAssignee(newAssigneeId);
        
        // Update cache
        const cached = ticketCache.get(ticketId);
        if (cached) {
          ticketCache.set(ticketId, { ...cached, ticket: result as any });
        }
        
        if (onTicketUpdate) {
          onTicketUpdate(result as any);
        }
        
        toast.success("Assignee updated successfully");
      } else {
        toast.error("Failed to update assignee");
      }
    } catch (error) {
      console.error("Failed to update assignee:", error);
      toast.error("Failed to update assignee");
    }
  };

  const handleQuickDepartmentUpdate = async (newDepartmentId: string) => {
    if (!ticket) return;

    const currentDepartmentId = ticket.department?.id || "unassigned";

    if (newDepartmentId === currentDepartmentId) return;

    try {
      let department = null;
      if (newDepartmentId && newDepartmentId !== "unassigned") {
        // Find department from the departments list
        department = departments.find(dept => dept.id === newDepartmentId) || null;
        if (!department) {
          // If department not found in the list, create a basic object with the ID
          department = { id: newDepartmentId, name: 'Unknown Department', status: 'active' as const, memberCount: 0 };
        }
      }

      const updatedTicket = await updateTicket({ ticketKey: (ticket as any)._id || ticket.id || ticketId, updates: {
        department: department || undefined
      } }).unwrap();

      if (updatedTicket) {
        setTicket(updatedTicket as any);

        // Update cache
        const cached = ticketCache.get(ticketId);
        if (cached) {
          ticketCache.set(ticketId, { ...cached, ticket: updatedTicket as any });
        }

        if (onTicketUpdate) {
          onTicketUpdate(updatedTicket as any);
        }

        toast.success("Department updated successfully");
      } else {
        toast.error("Failed to update department");
      }
    } catch (error) {
      console.error("Failed to update department:", error);
      toast.error("Failed to update department");
    }
  };

  const handleQuickStartDateUpdate = async (newStartDate: Date | null) => {
    if (!ticket) return;

    try {
      const result = await updateTicket({
        ticketKey: (ticket as any)._id || ticket.id || ticketId,
        updates: { startDate: newStartDate || undefined } as any,
      }).unwrap();
      
      if (result) {
        setTicket(result as any);

        // Update cache
        const cached = ticketCache.get(ticketId);
        if (cached) {
          ticketCache.set(ticketId, { ...cached, ticket: result as any });
        }

        if (onTicketUpdate) {
          onTicketUpdate(result as any);
        }

        toast.success("Start date updated successfully");
      } else {
        toast.error("Failed to update start date");
      }
    } catch (error) {
      console.error("Failed to update start date:", error);
      toast.error("Failed to update start date");
    }
  };

  const handleQuickDueDateUpdate = async (newDueDate: Date | null) => {
    if (!ticket) return;

    try {
      const result = await updateTicket({
        ticketKey: (ticket as any)._id || ticket.id || ticketId,
        updates: { dueDate: newDueDate || undefined } as any,
      }).unwrap();
      
      if (result) {
        setTicket(result as any);

        // Update cache
        const cached = ticketCache.get(ticketId);
        if (cached) {
          ticketCache.set(ticketId, { ...cached, ticket: result as any });
        }

        if (onTicketUpdate) {
          onTicketUpdate(result as any);
        }

        toast.success("Due date updated successfully");
      } else {
        toast.error("Failed to update due date");
      }
    } catch (error) {
      console.error("Failed to update due date:", error);
      toast.error("Failed to update due date");
    }
  };

  const handleQuickEstimatedTimeUpdate = async (timeString: string) => {
    if (!ticket) return;

    // Parse time string like "2h 30m" to hours
    const parseTimeString = (str: string): number | null => {
      const match = str.match(/(\d+)\s*h(?:\s*(\d+)\s*m?)?|(\d+)\s*m/);
      if (!match) return null;

      const hours = parseInt(match[1] || '0');
      const minutes = parseInt(match[2] || match[3] || '0');

      return hours + minutes / 60;
    };

    const parsedTime = parseTimeString(timeString);

    try {
      const result = await updateTicket({
        ticketKey: (ticket as any)._id || ticket.id || ticketId,
        updates: { estimatedTime: parsedTime || undefined },
      }).unwrap();
      
      if (result) {
        setTicket(result as any);
        setEstimatedTime(parsedTime);

        // Update cache
        const cached = ticketCache.get(ticketId);
        if (cached) {
          ticketCache.set(ticketId, { ...cached, ticket: result as any });
        }

        if (onTicketUpdate) {
          onTicketUpdate(result as any);
        }

        toast.success("Estimated time updated successfully");
      } else {
        toast.error("Failed to update estimated time");
      }
    } catch (error) {
      console.error("Failed to update estimated time:", error);
      toast.error("Failed to update estimated time");
    }
  };

  const handleAddTime = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!ticket || !timeForm.timeInput.trim()) {
      toast.error("Please enter time to log");
      return;
    }

    const minutes = Math.round(parseTimeString(timeForm.timeInput) * 60);
    if (minutes <= 0) {
      toast.error("Please enter a valid time (e.g., '2h', '30m', '1h 30m', '1.5')");
      return;
    }

    try {
      if (editingWorklogId) {
        // Update existing worklog
        await updateWorklog({
          worklogId: editingWorklogId,
          data: {
            minutes,
            note: timeForm.description.trim() || undefined,
            billable: true,
            endDateTime: timeForm.endDateTime?.toISOString(),
          },
        }).unwrap();
        toast.success(`Worklog updated successfully`);
      } else {
        // Create new worklog
        await createWorklog({
          ticketId: ticket?.id || ticketId,
          data: {
            minutes,
            note: timeForm.description.trim() || undefined,
            billable: true, // Default to billable
            endDateTime: timeForm.endDateTime?.toISOString(),
          },
        }).unwrap();
        toast.success(`Added ${formatTimeString(parseTimeString(timeForm.timeInput))} to ticket`);
      }

      setTimeLogDialogOpen(false);
      setTimeForm({ timeInput: '', description: '', endDateTime: null });
      setEditingWorklogId(null);
      refetchWorklogs(); // Refresh worklogs
    } catch (error: any) {
      console.error('Error saving worklog:', error);
      toast.error(error?.data?.error || 'Failed to save worklog');
    }
  };

  const handleSetEstimate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newEstimate.trim()) {
      toast.error("Please enter an estimate");
      return;
    }

    const estimate = parseTimeString(newEstimate);
    if (estimate <= 0) {
      toast.error("Please enter a valid time estimate (e.g., '8h', '2d', '1h 30m')");
      return;
    }

    try {
      // Make API call to update ticket with estimated time
      const response = await axiosInstance.patch<ApiResponse<any>>(`/tickets/${(ticket as any)._id || ticketId}`, {
        estimatedTime: estimate
      });

      if (response.data.success) {
        setEstimatedTime(estimate);
        toast.success(`Estimated time set to ${formatTimeString(estimate)}`);
        setEstimateDialogOpen(false);
        setNewEstimate('');
      } else {
        toast.error('Failed to update estimated time');
      }
    } catch (error: any) {
      console.error('Error updating estimated time:', error);
      toast.error(error?.response?.data?.error || 'Failed to update estimated time');
    }
  };

  const handleEditWorklog = (worklog: any) => {
    setTimeForm({
      timeInput: formatTimeString(worklog.minutes / 60),
      description: worklog.note || '',
      endDateTime: worklog.endDateTime ? new Date(worklog.endDateTime) : null,
    });
    setEditingWorklogId(worklog._id);
    setTimeLogDialogOpen(true);
  };

  const handleDeleteWorklog = async (worklogId: string) => {
    if (!confirm('Are you sure you want to delete this worklog?')) return;

    try {
      await deleteWorklog(worklogId).unwrap();
      toast.success('Worklog deleted successfully');
      refetchWorklogs();
    } catch (error: any) {
      console.error('Error deleting worklog:', error);
      toast.error(error?.data?.error || 'Failed to delete worklog');
    }
  };

  const handleToggleWatch = () => {
    setIsWatching(!isWatching);
    toast.success(isWatching ? "Stopped watching ticket" : "Now watching ticket");
  };

  const openImageViewer = (images: any[], index: number = 0) => {
    setImageViewerImages(images);
    setCurrentImageIndex(index);
    setImageViewerOpen(true);
  };

  // Make the function available globally for comments component
  React.useEffect(() => {
    (window as any).openImageViewer = openImageViewer;
    return () => {
      delete (window as any).openImageViewer;
    };
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      setAttachments(prev => [...prev, ...files]);
    }
    // Reset input
    e.target.value = '';
  };

  const handleRemoveAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleUploadAttachments = async () => {
    if (!ticket || attachments.length === 0) return;

    setUploadingAttachments(true);
    try {
      const formData = new FormData();
      attachments.forEach((file) => {
        formData.append('attachments', file);
      });

      await axiosInstance.post(`/tickets/${(ticket as any)._id || ticket.id || ticketId}/attachments`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      toast.success(`${attachments.length} attachment(s) uploaded successfully`);
      setAttachments([]);
      
      // Refresh ticket data to show new attachments
      if (isTicketKey) {
        // Refetch real ticket data
        window.location.reload();
      }
    } catch (error) {
      console.error('Failed to upload attachments:', error);
      toast.error('Failed to upload attachments');
    } finally {
      setUploadingAttachments(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (!ticket) {
    return (
      <div className="space-y-6 p-6">
        <div className="text-center">
          <p className="text-muted-foreground">Ticket not found</p>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
       
        {/* Header */}
        <TicketHeader
          ticket={ticket}
          isWatching={isWatching}
          onToggleWatch={handleToggleWatch}
          onEditClick={ticket.canDescriptionAndTitleChange ? () => setEditDialogOpen(true) : undefined}
          getStatusIcon={getStatusIcon}
          getStatusStyle={getStatusStyle}
          getPriorityColor={getPriorityColor}
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="comments">Comments ({realTicketData?.commentCount})</TabsTrigger>
                <TabsTrigger value="worklog">Work Log</TabsTrigger>
                <TabsTrigger value="history">History</TabsTrigger>
              </TabsList>
              
              <TabsContent value="details" className="space-y-4">
                {/* Ticket Details */}
                <AppCard>
                  <AppCardHeader>
                    <AppCardTitle>Description</AppCardTitle>
                  </AppCardHeader>
                  <AppCardContent>
                    <div className="whitespace-pre-wrap">
                      {ticket.description || "No description provided."}
                    </div>
                  </AppCardContent>
                  
                </AppCard>

                {/* Attachments */}
                {realTicketData?.attachments && realTicketData.attachments.length > 0 && (
                  <AppCard>
                    <AppCardHeader>
                      <AppCardTitle>Attachments ({realTicketData.attachments.length})</AppCardTitle>
                    </AppCardHeader>
                    <AppCardContent>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                        {realTicketData.attachments.map((attachment: any, index: number) => (
                          <div
                            key={attachment._id}
                            className="relative group cursor-pointer"
                            onClick={() => openImageViewer(realTicketData.attachments!, index)}
                          >
                            <div className="aspect-square relative overflow-hidden rounded-lg border bg-muted">
                              <Image
                                src={attachment.url}
                                alt={attachment.filename}
                                fill
                                className="object-cover transition-transform group-hover:scale-105"
                                loading="lazy"
                                sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                              />
                            </div>
                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors rounded-lg flex items-center justify-center">
                              <Eye className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                            </div>
                          </div>
                        ))}
                      </div>
                    </AppCardContent>
                  </AppCard>
                )}

                 {/* Merged Tickets Section */}
                {realTicketData?.mergedTickets && realTicketData.mergedTickets.length > 0 && (
                  <AppCard>
                    <AppCardHeader>
                      <AppCardTitle>Merged Tickets ({realTicketData.mergedTickets.length})</AppCardTitle>
                    </AppCardHeader>
                    <AppCardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Ticket Key</TableHead>
                            <TableHead>Title</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Priority</TableHead>
                            <TableHead>Created By</TableHead>
                            <TableHead>Comments</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {realTicketData.mergedTickets.map((mergedTicket: any) => (
                            <TableRow 
                              key={mergedTicket.id}
                              className="cursor-pointer hover:bg-muted/50"
                              onDoubleClick={() => {
                                const params = new URLSearchParams({
                                  parentId: ticketId,
                                  parentTitle: ticket.subject
                                });
                                router.push(`/tickets/${mergedTicket.ticketKey}?${params.toString()}`);
                              }}
                            >
                              <TableCell className="font-medium">{mergedTicket.ticketKey}</TableCell>
                              <TableCell className="max-w-xs truncate">{mergedTicket.title}</TableCell>
                              <TableCell>
                                {renderStatusBadge(mergedTicket.status)}
                              </TableCell>
                              <TableCell>
                                <AppBadge priority={mergedTicket.priority as any}>
                                  {mergedTicket.priority}
                                </AppBadge>
                              </TableCell>
                              <TableCell>{mergedTicket.createdBy?.name || 'Unknown'}</TableCell>
                              <TableCell>{mergedTicket.commentCount || 0}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </AppCardContent>
                  </AppCard>
                )}

                {/* Sub Tasks Section */}
                {ticket?.subTasks && ticket.subTasks.length > 0 && (
                  <AppCard>
                    <AppCardHeader>
                      <AppCardTitle>Sub Tasks ({ticket.subTasks.length})</AppCardTitle>
                    </AppCardHeader>
                    <AppCardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Title</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Priority</TableHead>
                            <TableHead>Assignee</TableHead>
                            <TableHead>Created</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {ticket.subTasks.map((subTask: any) => (
                            <TableRow
                              key={subTask.id}
                              className="cursor-pointer hover:bg-muted/50"
                            >
                              <TableCell className="font-medium">{subTask.title}</TableCell>
                              <TableCell>
                                {renderStatusBadge(subTask.status)}
                              </TableCell>
                              <TableCell>
                                <AppBadge priority={subTask.priority as any}>
                                  {subTask.priority}
                                </AppBadge>
                              </TableCell>
                              <TableCell>{subTask.assignee?.name || 'Unassigned'}</TableCell>
                              <TableCell>{new Date(subTask.createdAt).toLocaleDateString()}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </AppCardContent>
                  </AppCard>
                )}


              </TabsContent>

              <TabsContent value="comments" className="space-y-4">
                <Comments
                  ticket={ticket}
                />
                
                
              </TabsContent>

              <TabsContent value="worklog" className="space-y-4">
                <div className="space-y-6">
                

                  {/* Work Logs List */}
                  <AppCard>
                    <AppCardHeader className="flex flex-row items-center justify-between">
                      <AppCardTitle className="text-lg">Time Entries</AppCardTitle>
                      <AppButton size="sm" onClick={() => setTimeLogDialogOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Log Time
                      </AppButton>
                    </AppCardHeader>
                    <AppCardContent>
                      {worklogsLoading ? (
                        <div className="text-center py-4">Loading work logs...</div>
                      ) : worklogsData?.data && worklogsData.data.length > 0 ? (
                        <div className="space-y-3">
                          {worklogsData.data.map((worklog) => (
                            <div key={worklog._id} className="flex items-start justify-between p-3 border rounded-lg">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-1">
                                  <Clock className="h-4 w-4 text-muted-foreground" />
                                  <span className="font-medium">{formatTimeString(worklog.minutes / 60)}</span>
                                  <span className="text-sm text-muted-foreground">by {worklog.agentId}</span>
                                </div>
                                {worklog.note && (
                                  <p className="text-sm text-muted-foreground mt-1">{worklog.note}</p>
                                )}
                                {(worklog.endDateTime) && (
                                  <div className="text-xs text-muted-foreground mt-1">
                                    <span>Completed: {new Date(worklog.endDateTime).toLocaleString()}</span>
                                  </div>
                                )}
                                <div className="text-xs text-muted-foreground mt-1">
                                  {new Date(worklog.createdAt).toLocaleString()}
                                </div>
                              </div>
                              <div className="flex items-center space-x-1">
                                <AppButton 
                                  size="sm" 
                                  variant="ghost" 
                                  onClick={() => handleEditWorklog(worklog)}
                                  className="h-8 w-8 p-0"
                                >
                                  <Edit className="h-3 w-3" />
                                </AppButton>
                                <AppButton 
                                  size="sm" 
                                  variant="ghost" 
                                  onClick={() => handleDeleteWorklog(worklog._id)}
                                  className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </AppButton>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>No time entries yet</p>
                          <AppButton 
                            size="sm" 
                            className="mt-2" 
                            onClick={() => setTimeLogDialogOpen(true)}
                          >
                            Log your first time entry
                          </AppButton>
                        </div>
                      )}
                    </AppCardContent>
                  </AppCard>
                </div>
              </TabsContent>

              <TabsContent value="history" className="space-y-4">
                <History ticketId={ticketId} />
              </TabsContent>
            </Tabs>

            {/* Checklist Section */}
            {/* <Checklist ticketId={ticketId} /> */}
           
          </div>

          {/* Sidebar */}
          <TicketSidebar
            ticket={ticket}
            selectedAssignee={selectedAssignee}
            estimatedTime={estimatedTime}
            timeEntries={timeEntries}
            worklogSummary={worklogSummary?.data ? {
              ...worklogSummary.data,
              agentBreakdown: worklogSummary.data.agentBreakdown?.map(agent => {
                const user = availableUsers.find(u => u.id === agent._id);
                return {
                  totalMinutes: agent.totalMinutes,
                  billableMinutes: agent.billableMinutes,
                  entryCount: agent.entryCount,
                  agentId: agent._id,
                  agentName: user?.name || 'Unknown User',
                  agentRole: user?.role.name || 'Agent',
                };
              }) || []
            } : undefined}
            onStatusChange={handleQuickStatusUpdate}
            onPriorityChange={handleQuickPriorityUpdate}
            onAssigneeChange={handleQuickAssigneeUpdate}
            onDepartmentChange={handleQuickDepartmentUpdate}
            onTimeLogClick={() => setTimeLogDialogOpen(true)}
            onEstimateClick={() => setEstimateDialogOpen(true)}
            onStartDateChange={handleQuickStartDateUpdate}
            onDueDateChange={handleQuickDueDateUpdate}
            onEstimatedTimeChange={handleQuickEstimatedTimeUpdate}
            formatTimeString={formatTimeString}
            getTotalTimeLogged={getTotalTimeLogged}
          />
        </div>

        {/* Edit Dialog */}
        <TicketDetailsForm
          ticket={ticket}
          editDialogOpen={editDialogOpen}
          onEditDialogClose={() => setEditDialogOpen(false)}
          editSubject={editSubject}
          editDescription={editDescription}
          onSubjectChange={setEditSubject}
          onDescriptionChange={setEditDescription}
          onSubmit={handleUpdateTicket}
          updating={updating}
        />

        {/* Delete Dialog */}
        <DeleteDialog
          open={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
          onConfirm={handleDeleteTicket}
          loading={deleting}
          title="Delete Ticket"
          description="Are you sure you want to delete this ticket? This action cannot be undone."
        />

        {/* Time Tracking Dialogs */}
        <TimeTracking
          timeLogDialogOpen={timeLogDialogOpen}
          estimateDialogOpen={estimateDialogOpen}
          onTimeLogDialogClose={() => {
            setTimeLogDialogOpen(false);
            setEditingWorklogId(null);
            setTimeForm({ timeInput: '', description: '', endDateTime: null });
          }}
          onEstimateDialogClose={() => setEstimateDialogOpen(false)}
          timeForm={timeForm}
          onTimeFormChange={setTimeForm}
          newEstimate={newEstimate}
          onNewEstimateChange={setNewEstimate}
          onTimeLogSubmit={handleAddTime}
          onEstimateSubmit={handleSetEstimate}
          worklogSummary={worklogSummary?.data ? {
            ...worklogSummary.data,
            agentBreakdown: worklogSummary.data.agentBreakdown?.map(agent => {
              const user = availableUsers.find(u => u.id === agent._id);
              return {
                totalMinutes: agent.totalMinutes,
                billableMinutes: agent.billableMinutes,
                entryCount: agent.entryCount,
                agentId: agent._id,
                agentName: user?.name || 'Unknown User',
                agentRole: user?.role.name || 'Agent',
              };
            }) || []
          } : undefined}
          formatTimeString={formatTimeString}
          isEditing={!!editingWorklogId}
        />

        {/* Image Viewer Modal */}
        <ImageViewer
          images={imageViewerImages}
          currentIndex={currentImageIndex}
          isOpen={imageViewerOpen}
          onClose={() => setImageViewerOpen(false)}
          onIndexChange={setCurrentImageIndex}
        />
      </div>
    </TooltipProvider>
  );
}
