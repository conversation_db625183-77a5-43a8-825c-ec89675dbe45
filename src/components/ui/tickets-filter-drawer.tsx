"use client";

import React from 'react';
import { Filter, X } from 'lucide-react';
import { 
  Sheet, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  SheetTrigger 
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { STATUS_OPTIONS, PRIORITY_OPTIONS, PAGE_SIZE_OPTIONS } from '@/lib/enums';
import { useThemeClasses } from '@/components/theme-provider';

interface TicketsFilterDrawerProps {
  // Filter values
  status: string;
  priority: string;
  search: string;
  sortBy: string;
  sortOrder: string;
  pageSize: number;
  
  // Status options from API
  statuses?: Array<{ label: string; value: string }>;
  
  // Filter handlers
  onStatusChange: (value: string) => void;
  onPriorityChange: (value: string) => void;
  onSearchChange: (value: string) => void;
  onSortChange: (value: string) => void;
  onPageSizeChange: (value: number) => void;
  
  // Reset handler
  onResetFilters: () => void;
}

const TicketsFilterDrawer: React.FC<TicketsFilterDrawerProps> = ({
  status,
  priority,
  search,
  sortBy,
  sortOrder,
  pageSize,
  statuses = STATUS_OPTIONS,
  onStatusChange,
  onPriorityChange,
  onSearchChange,
  onSortChange,
  onPageSizeChange,
  onResetFilters,
}) => {
  const themeClasses = useThemeClasses();
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm">
          <Filter className="h-4 w-4 mr-2" />
          Filters
        </Button>
      </SheetTrigger>
      
      <SheetContent side="right" className="w-[400px] sm:w-[540px] flex flex-col">
        <SheetHeader className="pb-6 border-b">
          <SheetTitle className="text-lg font-semibold">Filter Tickets</SheetTitle>
        </SheetHeader>

        {/* scrollable body */}
        <div className="flex-1 overflow-y-auto px-4 py-6">
          {/* Search */}
          <div className="space-y-3 mb-4">
            <Label htmlFor="search" className="text-sm font-medium text-gray-700">
              Search
            </Label>
            <Input
              id="search"
              value={search}
              onChange={(e) => onSearchChange(e.target.value)}
              placeholder="Search tickets by ID, subject..."
              className={`h-10 border-gray-200 focus:${themeClasses.accentBorder} focus:ring-blue-500 rounded-md`}
            />
          </div>

          {/* Status Filter */}
          <div className="space-y-3 mb-4">
            <Label htmlFor="status" className="text-sm font-medium text-gray-700">
              Status
            </Label>
            <Select value={status} onValueChange={onStatusChange}>
              <SelectTrigger className={`h-10 border-gray-200 focus:${themeClasses.accentBorder} focus:ring-blue-500 rounded-md`}>
                <SelectValue placeholder="Select an option" />
              </SelectTrigger>
              <SelectContent className="rounded-md shadow-lg border-gray-200">
                <SelectItem value="all">All Status</SelectItem>
                {statuses.map((option) => (
                  <SelectItem 
                    key={option.value} 
                    value={option.value}
                    className={`hover:${themeClasses.hoverBg} focus:${themeClasses.lightBg}`}
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Priority Filter */}
          <div className="space-y-3 mb-4">
            <Label htmlFor="priority" className="text-sm font-medium text-gray-700">
              Priority
            </Label>
            <Select value={priority} onValueChange={onPriorityChange}>
              <SelectTrigger className={`h-10 border-gray-200 focus:${themeClasses.accentBorder} focus:ring-blue-500 rounded-md`}>
                <SelectValue placeholder="Select an option" />
              </SelectTrigger>
              <SelectContent className="rounded-md shadow-lg border-gray-200">
                <SelectItem value="all">All Priority</SelectItem>
                {PRIORITY_OPTIONS.map((option) => (
                  <SelectItem 
                    key={option.value} 
                    value={option.value}
                    className={`hover:${themeClasses.hoverBg} focus:${themeClasses.lightBg}`}
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Sort Filter */}
          <div className="space-y-3 mb-4">
            <Label htmlFor="sort" className="text-sm font-medium text-gray-700">
              Sort By
            </Label>
            <Select value={`${sortBy}-${sortOrder}`} onValueChange={onSortChange}>
              <SelectTrigger className={`h-10 border-gray-200 focus:${themeClasses.accentBorder} focus:ring-blue-500 rounded-md`}>
                <SelectValue placeholder="Select an option" />
              </SelectTrigger>
              <SelectContent className="rounded-md shadow-lg border-gray-200">
                <SelectItem value="updatedAt-desc">Last Updated (Newest First)</SelectItem>
                <SelectItem value="updatedAt-asc">Last Updated (Oldest First)</SelectItem>
                <SelectItem value="createdAt-desc">Date Created (Newest First)</SelectItem>
                <SelectItem value="createdAt-asc">Date Created (Oldest First)</SelectItem>
                <SelectItem value="priority-desc">Priority (High to Low)</SelectItem>
                <SelectItem value="priority-asc">Priority (Low to High)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Page Size (commented out) */}
          {/* ...existing code... */}
        </div>

        {/* sticky footer with slight gap from bottom */}
        <div className="px-4 pb-3 pt-2 bg-transparent">
          <div className="max-w-full mx-auto" style={{ transform: 'translateY(-6px)' }}>
            <Button 
              variant="secondary" 
              onClick={onResetFilters}
              className={`w-full h-10 border-gray-300 hover:${themeClasses.hoverBg} hover:border-gray-400 rounded-md font-medium`}
            >
              Reset Filters
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default TicketsFilterDrawer;
