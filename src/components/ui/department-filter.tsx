import * as React from "react";
import { Building, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { AppButton } from "@/components/ui-toolkit";
import { useGetUserDepartmentsQuery } from "@/services/api/departments";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";

export interface DepartmentFilterProps {
  selectedDepartments: string[];
  onDepartmentChange: (departments: string[]) => void;
  className?: string;
}

export const DepartmentFilter: React.FC<DepartmentFilterProps> = ({
  selectedDepartments,
  onDepartmentChange,
  className,
}) => {
  const { data: userDepartmentsResponse, isLoading } = useGetUserDepartmentsQuery();
  const departments = userDepartmentsResponse?.data?.departments || [];

  const handleDepartmentToggle = (departmentId: string) => {
    const newSelected = selectedDepartments.includes(departmentId)
      ? selectedDepartments.filter(id => id !== departmentId)
      : [...selectedDepartments, departmentId];
    onDepartmentChange(newSelected);
  };

  const getSelectedDepartmentsDisplay = () => {
    if (selectedDepartments.length === 0) return "All Departments";
    if (selectedDepartments.length === 1) {
      const dept = departments.find(d => d._id === selectedDepartments[0]);
      return dept ? dept.name : "1 selected";
    }
    return `${selectedDepartments.length} selected`;
  };

  return (
    <div className={cn("flex items-center", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <AppButton variant="outline" size="sm" className="h-8">
            <Building className="h-4 w-4 mr-2" />
            {getSelectedDepartmentsDisplay()}
            <ChevronDown className="h-4 w-4 ml-2" />
          </AppButton>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-0" align="start">
          <div className="p-2">
            <div className="text-sm font-medium mb-2">Filter by Department</div>
            {isLoading ? (
              <div className="text-sm text-muted-foreground">Loading...</div>
            ) : departments.length === 0 ? (
              <div className="text-sm text-muted-foreground">No departments found</div>
            ) : (
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {departments.map((department) => (
                  <div key={department._id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`dept-${department._id}`}
                      checked={selectedDepartments.includes(department._id)}
                      onCheckedChange={() => handleDepartmentToggle(department._id)}
                    />
                    <label
                      htmlFor={`dept-${department._id}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                    >
                      {department.name}
                    </label>
                  </div>
                ))}
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};