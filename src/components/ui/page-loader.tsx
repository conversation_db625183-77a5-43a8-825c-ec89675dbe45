"use client";

import * as React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>h<PERSON>oa<PERSON> } from "react-spinners";
import { Building, Shield, Zap } from "lucide-react";
import { useThemeClasses, useThemeAccent } from "@/components/theme-provider";

interface PageLoaderProps {
  title?: string;
  subtitle?: string;
  variant?: "default" | "tenant" | "auth" | "minimal" | "hash" | "theme";
  size?: "sm" | "md" | "lg";
}

export function PageLoader({ 
  title = "", 
  subtitle = "",
  variant = "default",
  size = "md"
}: PageLoaderProps) {
  const themeClasses = useThemeClasses();
  const { accent } = useThemeAccent();

  const getIcon = () => {
    switch (variant) {
      case "tenant":
        return <Building className="h-8 w-8 text-white" />;
      case "auth":
        return <Shield className="h-8 w-8 text-white" />;
      default:
        return <Zap className="h-8 w-8 text-white" />;
    }
  };

  const getBackgroundGradient = () => {
    switch (variant) {
      case "tenant":
        return "from-blue-50 via-indigo-50 to-purple-50";
      case "auth":
        return "from-emerald-50 via-teal-50 to-cyan-50";
      case "minimal":
        return "from-gray-50 to-gray-100";
      case "hash":
      case "theme":
        return "from-white/20 to-white/10";
      default:
        return "from-slate-50 via-gray-50 to-slate-100";
    }
  };

  const getIconBackground = () => {
    switch (variant) {
      case "tenant":
        return "from-blue-500 to-indigo-600";
      case "auth":
        return "from-emerald-500 to-teal-600";
      case "minimal":
        return "from-gray-400 to-gray-600";
      default:
        return "from-slate-500 to-slate-700";
    }
  };

  const getLoaderColor = () => {
    // Handle theme variant using current accent
    if (variant === "theme") {
      const accentColors: Record<string, string> = {
        blue: "#3b82f6",
        lightBlue: "#0ea5e9",
        darkBlue: "#1e40af",
        cyan: "#06b6d4",
        teal: "#14b8a6",
        turquoise: "#2dd4bf",
        green: "#22c55e",
        emerald: "#10b981",
        lime: "#84cc16",
        yellow: "#eab308",
        amber: "#f59e0b",
        orange: "#f97316",
        deepOrange: "#ea580c",
        red: "#ef4444",
        rose: "#f43f5e",
        pink: "#ec4899",
        fuchsia: "#d946ef",
        purple: "#a855f7",
        deepPurple: "#9333ea",
        indigo: "#6366f1",
        violet: "#8b5cf6",
        magenta: "#c026d3",
        brown: "#a3a3a3",
        gray: "#6b7280",
        coolGray: "#6b7280",
        warmGray: "#78716c",
        slate: "#64748b",
        stone: "#78716c"
      };
      return accentColors[accent] || "#6366f1"; // default to indigo
    }

    // Otherwise use default colors based on variant
    switch (variant) {
      case "tenant":
        return "#3b82f6"; // blue-500
      case "auth":
        return "#10b981"; // emerald-500
      case "minimal":
        return "#6b7280"; // gray-500
      case "hash":
        return "#6366f1"; // indigo-500
      default:
        return "#64748b"; // slate-500
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return {
          container: "min-h-screen",
          icon: "h-12 w-12",
          iconInner: "h-6 w-6",
          title: "text-lg",
          subtitle: "text-sm",
          loader: 35
        };
      case "lg":
        return {
          container: "min-h-screen",
          icon: "h-20 w-20",
          iconInner: "h-10 w-10",
          title: "text-3xl",
          subtitle: "text-lg",
          loader: 60
        };
      default: // md
        return {
          container: "min-h-screen",
          icon: "h-16 w-16",
          iconInner: "h-8 w-8",
          title: "text-xl",
          subtitle: "text-base",
          loader: 45
        };
    }
  };

  const sizeClasses = getSizeClasses();

  if (variant === "hash" || variant === "theme") {
    return (
      <div className={`${sizeClasses.container} flex items-center justify-center`}>
        <div className="flex items-center justify-center space-x-4">
          <HashLoader 
            color={getLoaderColor()} 
            size={sizeClasses.loader}
          />
          <div className="text-left">
            {title && (
              <div className={`${sizeClasses.title} font-semibold text-gray-700 mb-2`}>
                {title}
              </div>
            )}
            {subtitle && (
              <div className={`${sizeClasses.subtitle} text-gray-500`}>
                {subtitle}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (variant === "minimal") {
    return (
      <div className={`${sizeClasses.container} bg-gradient-to-br ${getBackgroundGradient()} flex items-center justify-center`}>
        <div className="text-center">
          <div className="mb-6">
            <ScaleLoader 
              color={getLoaderColor()} 
              height={35}
              width={4}
              radius={2}
              margin={2}
            />
          </div>
          <div className={`${sizeClasses.title} font-semibold text-gray-700 mb-2`}>
            {title}
          </div>
          <div className={`${sizeClasses.subtitle} text-gray-500`}>
            {subtitle}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`${sizeClasses.container} bg-gradient-to-br ${getBackgroundGradient()} flex items-center justify-center relative overflow-hidden`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 2px 2px, currentColor 1px, transparent 0)`,
          backgroundSize: '32px 32px'
        }}></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400 rounded-full opacity-20 animate-bounce"></div>
      <div className="absolute top-1/3 right-1/4 w-3 h-3 bg-purple-400 rounded-full opacity-30 animate-bounce" style={{ animationDelay: '0.5s' }}></div>
      <div className="absolute bottom-1/3 left-1/3 w-2 h-2 bg-indigo-400 rounded-full opacity-25 animate-bounce" style={{ animationDelay: '1s' }}></div>

      <div className="text-center z-10 relative">
        {/* Animated Icon Container */}
        <div className="relative mb-8">


          {/* Orbital Loader */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="relative">
              <ClipLoader 
                color={getLoaderColor()} 
                size={sizeClasses.loader + 20}
                cssOverride={{
                  borderWidth: '3px',
                }}
              />
            </div>
          </div>
        </div>

       
      </div>

      <style jsx>{`
        @keyframes loading-progress {
          0% { width: 20%; opacity: 0.5; }
          50% { width: 80%; opacity: 1; }
          100% { width: 20%; opacity: 0.5; }
        }
      `}</style>
    </div>
  );
}
