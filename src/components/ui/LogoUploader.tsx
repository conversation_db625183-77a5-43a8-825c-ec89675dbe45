"use client";
import React, { useState } from "react";
import { Upload } from "lucide-react";
import { Label } from "@/components/ui/label";
import { AppButton } from "../ui-toolkit";
import Image from "next/image";

interface LogoUploaderProps {
    label?: string;
    onChange?: (file: File | null) => void;
}

const LogoUploader: React.FC<LogoUploaderProps> = ({ label = "Organization Logo", onChange }) => {
    const [logo, setLogo] = useState<string | null>(null);

    const handleLogoUpload = () => {
        const input = document.createElement("input");
        input.type = "file";
        input.accept = "image/png, image/jpeg";
        input.onchange = (e: any) => {
            const file = e.target.files[0];
            if (file) {
                if (file.size > 2 * 1024 * 1024) {
                    alert("File too large! Max 2MB allowed.");
                    return;
                }
                const url = URL.createObjectURL(file);
                setLogo(url);
                if (onChange) onChange(file); 
            }
        };
        input.click();
    };

    return (
        <div className="space-y-2">
            <Label>{label}</Label>
            <div className="flex items-center space-x-4">
                <div className="w-16 h-16 rounded-lg border-2 border-dashed border-muted-foreground/25 flex items-center justify-center bg-muted overflow-hidden">
                    {logo ? (
                        <Image
                            src={logo}
                            alt="Logo Preview"
                            width={64}
                            height={64}
                            className="w-full h-full object-cover rounded-lg"
                        />
                    ) : (
                        <span className="text-xs text-muted-foreground">Logo</span>
                    )}
                </div>
                <AppButton
                    variant="outline"
                    onClick={handleLogoUpload}
                    icon={<Upload className="h-4 w-4" />}
                >
                    Upload Logo
                </AppButton>
            </div>
            <p className="text-xs text-muted-foreground">
                Recommended: PNG or JPG, max 2MB, square aspect ratio
            </p>
        </div>
    );
};

export default LogoUploader;
