import * as React from "react";
import { User, Users, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { AppButton } from "@/components/ui-toolkit";
import { useGetUsersByDepartmentsQuery } from "@/services/api/users";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";

export interface UserFilterProps {
  selectedUsers: string[];
  onUserChange: (users: string[]) => void;
  className?: string;
}

export const UserFilter: React.FC<UserFilterProps> = ({
  selectedUsers,
  onUserChange,
  className,
}) => {
  const { data: usersResponse, isLoading } = useGetUsersByDepartmentsQuery();
  const users = usersResponse?.data || [];

  const handleUserToggle = (userId: string) => {
    const newSelected = selectedUsers.includes(userId)
      ? selectedUsers.filter(id => id !== userId)
      : [...selectedUsers, userId];
    onUserChange(newSelected);
  };

  const getSelectedUsersDisplay = () => {
    if (selectedUsers.length === 0) return "All Assignees";
    if (selectedUsers.length === 1) {
      const user = users.find(u => u.id === selectedUsers[0]);
      return user ? user.name : "1 selected";
    }
    return `${selectedUsers.length} selected`;
  };

  const visibleUsers = users.slice(0, 5);
  const remainingCount = Math.max(0, users.length - 5);

  return (
    <div className={cn("flex items-center", className)}>
      {/* Show first 4-5 users as overlapping avatars */}
      <div className="flex -space-x-1">
        {visibleUsers.map((user, index) => (
          <div
            key={user.id}
            className={cn(
              "relative cursor-pointer rounded-full border-2 transition-all hover:z-10 shadow-sm",
              selectedUsers.includes(user.id)
                ? "border-blue-500 z-10"
                : "border-gray-300 hover:border-gray-400"
            )}
            style={{ zIndex: selectedUsers.includes(user.id) ? 20 : 10 - index }}
            onClick={() => handleUserToggle(user.id)}
            title={user.fullName}
          >
            <Avatar className="h-8 w-8">
              <AvatarImage src="" alt={user.name} />
              <AvatarFallback className="text-xs bg-gray-200 text-gray-700">
                {user.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
          </div>
        ))}
      </div>

      {/* Dropdown for remaining users */}
      <Popover>
        <PopoverTrigger asChild>
          <AppButton variant="outline" size="sm" className="h-8 px-2 ml-2">
            <Users className="h-4 w-4 mr-1" />
            {getSelectedUsersDisplay()}
            <ChevronDown className="h-4 w-4 ml-1" />
          </AppButton>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-2" align="start">
          <div className="space-y-2">
            {users.map((user) => (
              <div
                key={user.id}
                className="flex items-center space-x-2 p-2 hover:bg-muted rounded cursor-pointer"
                onClick={() => handleUserToggle(user.id)}
              >
                <Checkbox
                  checked={selectedUsers.includes(user.id)}
                  onChange={() => handleUserToggle(user.id)}
                />
                <Avatar className="h-6 w-6">
                  <AvatarImage src="" alt={user.name} />
                  <AvatarFallback className="text-xs">
                    {user.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium truncate">{user.name}</div>
                  <div className="text-xs text-muted-foreground truncate">{user.email}</div>
                </div>
              </div>
            ))}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};