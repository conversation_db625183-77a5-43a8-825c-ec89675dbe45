import * as React from "react";
import { Plus, Loader2, X } from "lucide-react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useUpdateKnowledgeArticleMutation, useGetKnowledgeCategoriesQuery } from "@/services/api/knowledge";
import { KnowledgeArticle } from "@/types";
import { toast } from "sonner";

interface EditKnowledgeArticleDialogProps {
  article: KnowledgeArticle | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditKnowledgeArticleDialog({ 
  article, 
  open, 
  onOpenChange 
}: EditKnowledgeArticleDialogProps) {
  const [title, setTitle] = React.useState("");
  const [content, setContent] = React.useState("");
  const [category, setCategory] = React.useState("");
  const [tags, setTags] = React.useState<string[]>([]);
  const [currentTag, setCurrentTag] = React.useState("");
  const [status, setStatus] = React.useState<"draft" | "published">("published");
  const [isInternal, setIsInternal] = React.useState(false);
  const [isPinned, setIsPinned] = React.useState(false);

  const [updateArticle, { isLoading }] = useUpdateKnowledgeArticleMutation();
  const { data: categoriesResponse } = useGetKnowledgeCategoriesQuery();

  // Get existing categories from API for suggestions
  const existingCategories = categoriesResponse?.data?.map(cat => cat.category) || [];

  // Initialize form when article changes
  React.useEffect(() => {
    if (article && open) {
      setTitle(article.title);
      setContent(article.content);
      setCategory(article.category);
      setTags([...article.tags]);
      setStatus(article.status === "archived" ? "draft" : article.status);
      setIsInternal(article.isInternal);
      setIsPinned(article.isPinned);
    }
  }, [article, open]);

  const handleAddTag = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && currentTag.trim()) {
      e.preventDefault();
      if (!tags.includes(currentTag.trim()) && tags.length < 10) {
        setTags([...tags, currentTag.trim()]);
        setCurrentTag("");
      }
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!article) return;

    if (!title.trim() || !content.trim() || !category) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      const result = await updateArticle({
        id: article.id,
        title: title.trim(),
        content: content.trim(),
        category,
        tags,
        status,
        isInternal,
        isPinned,
      });

      if ('error' in result) {
        throw result.error;
      }

      toast.success("Knowledge article updated successfully!");
      onOpenChange(false);
    } catch (error: any) {
      console.error("Failed to update article:", error);
      toast.error(error?.data?.message || error?.message || "Failed to update article");
    }
  };

  if (!article) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Knowledge Article</DialogTitle>
          <DialogDescription>
            Update the article information and content.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              placeholder="Enter article title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Category *</Label>
            <div className="relative">
              <Input
                id="category"
                placeholder="Enter or select a category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                required
                list="category-suggestions"
              />
              <datalist id="category-suggestions">
                {existingCategories.map((cat: string) => (
                  <option key={cat} value={cat} />
                ))}
              </datalist>
            </div>
            {existingCategories.length > 0 && (
              <div className="text-sm text-muted-foreground">
                Existing categories: {existingCategories.join(", ")}
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="content">Content *</Label>
            <Textarea
              id="content"
              placeholder="Enter article content (supports markdown)"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              rows={8}
              className="min-h-[200px]"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="tags">Tags</Label>
            <Input
              id="tags"
              placeholder="Type a tag and press Enter (max 10 tags)"
              value={currentTag}
              onChange={(e) => setCurrentTag(e.target.value)}
              onKeyDown={handleAddTag}
              disabled={tags.length >= 10}
            />
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 hover:text-destructive"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={status} onValueChange={(value: "draft" | "published") => setStatus(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">Options</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isInternal"
                    checked={isInternal}
                    onChange={(e) => setIsInternal(e.target.checked)}
                    className="rounded"
                  />
                  <Label htmlFor="isInternal" className="text-sm">
                    Internal only
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isPinned"
                    checked={isPinned}
                    onChange={(e) => setIsPinned(e.target.checked)}
                    className="rounded"
                  />
                  <Label htmlFor="isPinned" className="text-sm">
                    Pin article
                  </Label>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Update Article
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
