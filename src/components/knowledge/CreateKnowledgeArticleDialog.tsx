import * as React from "react";
import { Plus, Loader2 } from "lucide-react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { useCreateKnowledgeArticleMutation, useGetKnowledgeCategoriesQuery } from "@/services/api/knowledge";
import { toast } from "sonner";

interface CreateKnowledgeArticleDialogProps {
  children: React.ReactNode;
}

export function CreateKnowledgeArticleDialog({ children }: CreateKnowledgeArticleDialogProps) {
  const [open, setOpen] = React.useState(false);
  const [title, setTitle] = React.useState("");
  const [content, setContent] = React.useState("");
  const [category, setCategory] = React.useState("");
  const [tags, setTags] = React.useState<string[]>([]);
  const [currentTag, setCurrentTag] = React.useState("");
  const [status, setStatus] = React.useState<"draft" | "published">("published");
  const [isInternal, setIsInternal] = React.useState(false);

  const [createArticle, { isLoading }] = useCreateKnowledgeArticleMutation();
  const { data: categoriesResponse } = useGetKnowledgeCategoriesQuery();

  // Get existing categories from API for suggestions
  const existingCategories = categoriesResponse?.data?.map(cat => cat.category) || [];

  // Reset form when dialog closes
  React.useEffect(() => {
    if (!open) {
      setTitle("");
      setContent("");
      setCategory("");
      setTags([]);
      setCurrentTag("");
      setStatus("published");
      setIsInternal(false);
    }
  }, [open]);

  const handleAddTag = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && currentTag.trim()) {
      e.preventDefault();
      if (!tags.includes(currentTag.trim()) && tags.length < 10) {
        setTags([...tags, currentTag.trim()]);
        setCurrentTag("");
      }
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim() || !content.trim() || !category) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      const result = await createArticle({
        title: title.trim(),
        content: content.trim(),
        category,
        tags,
        status,
        isInternal,
      });

      if ('error' in result) {
        throw result.error;
      }

      toast.success("Knowledge article created successfully!");
      
      // Close dialog - the form reset will happen via useEffect
      setOpen(false);
    } catch (error: any) {
      console.error("Failed to create article:", error);
      toast.error(error?.data?.message || error?.message || "Failed to create article");
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Knowledge Article</DialogTitle>
          <DialogDescription>
            Create a new article for the knowledge base to help users find solutions.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              placeholder="Enter article title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Category *</Label>
            <div className="relative">
              <Input
                id="category"
                placeholder="Enter or select a category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                required
                list="category-suggestions"
              />
              <datalist id="category-suggestions">
                {existingCategories.map((cat: string) => (
                  <option key={cat} value={cat} />
                ))}
              </datalist>
            </div>
            {existingCategories.length > 0 && (
              <div className="text-sm text-muted-foreground">
                Existing categories: {existingCategories.join(", ")}
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="content">Content *</Label>
            <Textarea
              id="content"
              placeholder="Enter article content (supports markdown)"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              rows={8}
              className="min-h-[200px] max-w-[450px]"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="tags">Tags</Label>
            <Input
              id="tags"
              placeholder="Type a tag and press Enter (max 10 tags)"
              value={currentTag}
              onChange={(e) => setCurrentTag(e.target.value)}
              onKeyDown={handleAddTag}
              disabled={tags.length >= 10}
            />
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 hover:text-destructive"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={status} onValueChange={(value: "draft" | "published") => setStatus(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">Visibility</Label>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isInternal"
                  checked={isInternal}
                  onChange={(e) => setIsInternal(e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="isInternal" className="text-sm">
                  Internal only
                </Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Article
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
