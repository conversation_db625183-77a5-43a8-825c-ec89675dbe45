import * as React from "react";
import { Eye, ThumbsUp, ThumbsDown, Edit, Trash2, Calendar, User, Tag } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  useGetKnowledgeArticleQuery,
  useVoteKnowledgeArticleMutation,
  useDeleteKnowledgeArticleMutation
} from "@/services/api/knowledge";
import { KnowledgeArticle } from "@/types";
import { toast } from "sonner";

interface ViewKnowledgeArticleDialogProps {
  article: KnowledgeArticle | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit?: (article: KnowledgeArticle) => void;
}

export function ViewKnowledgeArticleDialog({
  article,
  open,
  onOpenChange,
  onEdit
}: ViewKnowledgeArticleDialogProps) {
  const [userVote, setUserVote] = React.useState<boolean | null>(null);

  const {
    data: articleResponse,
    isLoading: articleLoading,
  } = useGetKnowledgeArticleQuery(article?.id || "", {
    skip: !article?.id || !open
  });

  const [voteArticle, { isLoading: voteLoading }] = useVoteKnowledgeArticleMutation();
  const [deleteArticle, { isLoading: deleteLoading }] = useDeleteKnowledgeArticleMutation();

  const currentArticle = articleResponse?.data || article;

  const handleVote = async (isHelpful: boolean) => {
    if (!currentArticle) return;

    try {
      const result = await voteArticle({
        id: currentArticle.id,
        isHelpful
      });

      if ('error' in result) {
        throw result.error;
      }

      setUserVote(isHelpful);
      toast.success(`Vote recorded as ${isHelpful ? 'helpful' : 'not helpful'}`);
    } catch (error: any) {
      console.error("Failed to vote:", error);
      toast.error(error?.data?.message || error?.message || "Failed to record vote");
    }
  };

  const handleDelete = async () => {
    if (!currentArticle) return;

    if (!confirm("Are you sure you want to delete this article? This action cannot be undone.")) {
      return;
    }

    try {
      const result = await deleteArticle(currentArticle.id);

      if ('error' in result) {
        throw result.error;
      }

      toast.success("Article deleted successfully");
      onOpenChange(false);
    } catch (error: any) {
      console.error("Failed to delete article:", error);
      toast.error(error?.data?.message || error?.message || "Failed to delete article");
    }
  };

  const handleEdit = () => {
    if (currentArticle && onEdit) {
      onEdit(currentArticle);
      onOpenChange(false);
    }
  };

  if (!currentArticle) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <DialogTitle className="text-xl font-bold mb-2 leading-tight">
                {currentArticle.title}
              </DialogTitle>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  <span>{currentArticle.author.name}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(currentArticle.createdAt).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="h-4 w-4" />
                  <span>{currentArticle.views} views</span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">{currentArticle.category}</Badge>
              {currentArticle.isPinned && (
                <Badge variant="outline">Pinned</Badge>
              )}
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-[60vh] pr-4">
            <div className="space-y-4">
              {/* Content */}
              <div className="prose max-w-none">
                <div className="whitespace-pre-wrap text-sm leading-relaxed">
                  {currentArticle.content}
                </div>
              </div>

              {/* Tags */}
              {currentArticle.tags.length > 0 && (
                <div>
                  <Separator className="mb-3" />
                  <div className="flex items-center gap-2 flex-wrap">
                    <Tag className="h-4 w-4 text-muted-foreground" />
                    {currentArticle.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Vote Section */}
              <div>
                <Separator className="mb-4" />
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Was this article helpful?
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant={userVote === true ? "default" : "outline"}
                        onClick={() => handleVote(true)}
                        disabled={voteLoading}
                        className="h-8"
                      >
                        <ThumbsUp className="h-4 w-4 mr-1" />
                        {currentArticle.helpful}
                      </Button>
                      <Button
                        size="sm"
                        variant={userVote === false ? "default" : "outline"}
                        onClick={() => handleVote(false)}
                        disabled={voteLoading}
                        className="h-8"
                      >
                        <ThumbsDown className="h-4 w-4 mr-1" />
                        {currentArticle.notHelpful}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="text-xs text-muted-foreground">
            Last updated: {new Date(currentArticle.updatedAt).toLocaleDateString()}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleEdit}
              disabled={deleteLoading}
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDelete}
              disabled={deleteLoading}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
