"use client";

import * as React from "react";
import { ChevronLeft, ChevronRight, X, Download } from "lucide-react";
import Image from "next/image";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { AppButton } from "@/components/ui-toolkit";

interface ImageViewerProps {
  images: Array<{
    _id: string;
    url: string;
    filename: string;
    size: number;
  }>;
  currentIndex: number;
  isOpen: boolean;
  onClose: () => void;
  onIndexChange: (index: number) => void;
}

export default function ImageViewer({
  images,
  currentIndex,
  isOpen,
  onClose,
  onIndexChange,
}: ImageViewerProps) {
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
    }
  }, [currentIndex, isOpen]);

  const handlePrevious = () => {
    const newIndex = currentIndex > 0 ? currentIndex - 1 : images.length - 1;
    onIndexChange(newIndex);
  };

  const handleNext = () => {
    const newIndex = currentIndex < images.length - 1 ? currentIndex + 1 : 0;
    onIndexChange(newIndex);
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case "ArrowLeft":
        handlePrevious();
        break;
      case "ArrowRight":
        handleNext();
        break;
      case "Escape":
        onClose();
        break;
    }
  };

  React.useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [currentIndex, isOpen]);

  if (!images.length) return null;

  const currentImage = images[currentIndex];

  const handleDownload = () => {
    const link = document.createElement("a");
    link.href = currentImage.url;
    link.download = currentImage.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl w-full h-[85vh] p-0 bg-black/95 border-0">
        <div className="sr-only">
          <DialogTitle>Image Viewer</DialogTitle>
        </div>
        <div className="relative w-full h-full flex items-center justify-center">
          {/* Close Button */}
          <AppButton
            variant="ghost"
            size="sm"
            className="absolute top-4 right-4 z-50 text-white hover:bg-white/20"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </AppButton>

          {/* Download Button */}
          <AppButton
            variant="ghost"
            size="sm"
            className="absolute top-4 right-16 z-50 text-white hover:bg-white/20"
            onClick={handleDownload}
          >
            <Download className="h-4 w-4" />
          </AppButton>

          {/* Navigation Buttons */}
          {images.length > 1 && (
            <>
              <AppButton
                variant="ghost"
                size="sm"
                className="absolute left-4 top-1/2 -translate-y-1/2 z-50 text-white hover:bg-white/20 h-12 w-12 rounded-full"
                onClick={handlePrevious}
              >
                <ChevronLeft className="h-6 w-6" />
              </AppButton>

              <AppButton
                variant="ghost"
                size="sm"
                className="absolute right-4 top-1/2 -translate-y-1/2 z-50 text-white hover:bg-white/20 h-12 w-12 rounded-full"
                onClick={handleNext}
              >
                <ChevronRight className="h-6 w-6" />
              </AppButton>
            </>
          )}

          {/* Image */}
          <div className="relative w-full h-full flex items-center justify-center p-4">
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
              </div>
            )}
            <Image
              src={currentImage.url}
              alt={currentImage.filename}
              fill
              className="object-contain"
              onLoad={() => setIsLoading(false)}
              onError={() => setIsLoading(false)}
              priority
            />
          </div>

          {/* Image Counter */}
          {images.length > 1 && (
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
              {currentIndex + 1} / {images.length}
            </div>
          )}

          {/* Image Info */}
          <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-2 rounded-lg text-sm">
            <p className="text-xs opacity-75">{(currentImage.size / 1024).toFixed(1)} KB</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}