import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const appToggleVariants = cva(
  "relative inline-flex items-center rounded-full bg-gradient-to-r p-0.5 transition-all duration-300 ease-out cursor-pointer",
  {
    variants: {
      variant: {
        default: "",
        destructive: "",
      },
      size: {
        sm: "h-7 w-36 text-xs",
        md: "h-8 w-44 text-xs",
        lg: "h-9 w-52 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
);

export interface AppToggleProps
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, "onChange">,
    VariantProps<typeof appToggleVariants> {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  leftLabel?: string;
  rightLabel?: string;
  isLeftAreaEnabled?: boolean;
  isRightAreaEnabled?: boolean;
}

const AppToggle = React.forwardRef<HTMLButtonElement, AppToggleProps>(
  ({
    className,
    variant,
    size,
    checked,
    onCheckedChange,
    leftLabel = "",
    rightLabel = "",
    isLeftAreaEnabled ,
    isRightAreaEnabled,
    disabled,
    ...props
  }, ref) => {
    const effectiveChecked = isLeftAreaEnabled && isRightAreaEnabled ? checked : isRightAreaEnabled ? true : false;

    const handleClick = () => {
      if (!disabled && isLeftAreaEnabled && isRightAreaEnabled) {
        onCheckedChange(!checked);
      }
    };

    return (
      <div className="flex items-center">
        <button
          type="button"
          className={cn(
            appToggleVariants({ variant, size, className }),
            effectiveChecked
              ? "bg-accent"
              : "bg-accent",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          ref={ref}
          onClick={handleClick}
          disabled={disabled}
          {...props}
        >
          {/* Left side button */}
          {isLeftAreaEnabled && (
            <div
              className={cn(
                "flex-1 flex items-center justify-center rounded-full transition-all duration-300 font-medium h-full",
                size === "sm" && "text-xs",
                size === "md" && "text-xs",
                size === "lg" && "text-sm",
                !effectiveChecked
                  ? "bg-background text-foreground shadow-sm border border-border"
                  : "bg-transparent text-accent-foreground"
              )}
            >
              {leftLabel}
            </div>
          )}

          {/* Right side button */}
          {isRightAreaEnabled && (
            <div
              className={cn(
                "flex-1 flex items-center justify-center rounded-full transition-all duration-300 font-medium  h-full",
                size === "sm" && "text-xs",
                size === "md" && "text-xs", 
                size === "lg" && "text-sm",
                effectiveChecked
                  ? "bg-background text-foreground shadow-sm border border-border"
                  : "bg-transparent text-accent-foreground"
              )}
            >
              {rightLabel}
            </div>
          )}
        </button>
      </div>
    );
  }
);

AppToggle.displayName = "AppToggle";

export { AppToggle };