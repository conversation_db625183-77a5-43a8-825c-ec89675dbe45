"use client";

import * as React from "react";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
} from "recharts";
import { cn } from "@/lib/utils";
import { useThemeAccent, type AccentTheme } from "@/components/theme-provider";

type ChartType = "line" | "area" | "bar" | "pie";

export interface AppChartProps extends React.HTMLAttributes<HTMLDivElement> {
  type: ChartType;
  data: Record<string, unknown>[];
  dataKey?: string;
  nameKey?: string;
  height?: number;
  showGrid?: boolean;
  showTooltip?: boolean;
  showXAxis?: boolean;
  showYAxis?: boolean;
}

const AppChart = React.forwardRef<HTMLDivElement, AppChartProps>(
  ({
    className,
    type,
    data,
    dataKey = "value",
    nameKey = "name",
    height = 300,
    showGrid = true,
    showTooltip = true,
    showXAxis = true,
    showYAxis = true,
    ...props
  }, ref) => {
    const { accent } = useThemeAccent();
    
    const getAccentColor = () => {
      const accentColors: Record<AccentTheme, string> = {
        blue: "#3b82f6",
        lightBlue: "#0ea5e9",
        darkBlue: "#1e40af",
        cyan: "#06b6d4",
        teal: "#14b8a6",
        turquoise: "#22d3ee",
        green: "#10b981",
        emerald: "#10b981",
        lime: "#84cc16",
        yellow: "#eab308",
        amber: "#f59e0b",
        orange: "#f97316",
        deepOrange: "#ea580c",
        red: "#ef4444",
        rose: "#f43f5e",
        pink: "#ec4899",
        fuchsia: "#d946ef",
        purple: "#8b5cf6",
        deepPurple: "#7c3aed",
        indigo: "#6366f1",
        violet: "#8b5cf6",
        magenta: "#d946ef",
        brown: "#a3663c",
        gray: "#6b7280",
        coolGray: "#64748b",
        warmGray: "#78716c",
        slate: "#64748b",
        stone: "#78716c",
      };
      return accentColors[accent] || "#3b82f6"; // fallback to blue
    };

    const renderChart = () => {
      const accentColor = getAccentColor();
      
      switch (type) {
        case "line":
          return (
            <LineChart data={data}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />}
              {showXAxis && <XAxis dataKey={nameKey} className="text-muted-foreground" />}
              {showYAxis && <YAxis className="text-muted-foreground" />}
              {showTooltip && (
                <Tooltip
                  contentStyle={{
                    backgroundColor: "hsl(var(--popover))",
                    border: "1px solid hsl(var(--border))",
                    borderRadius: "6px",
                  }}
                />
              )}
              <Line
                type="monotone"
                dataKey={dataKey}
                stroke={accentColor}
                strokeWidth={3}
                dot={{ fill: accentColor, strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: accentColor, strokeWidth: 2, fill: "#fff" }}
              />
            </LineChart>
          );
          
        case "area":
          return (
            <AreaChart data={data}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />}
              {showXAxis && <XAxis dataKey={nameKey} className="text-muted-foreground" />}
              {showYAxis && <YAxis className="text-muted-foreground" />}
              {showTooltip && (
                <Tooltip
                  contentStyle={{
                    backgroundColor: "hsl(var(--popover))",
                    border: "1px solid hsl(var(--border))",
                    borderRadius: "6px",
                  }}
                />
              )}
              <Area
                type="monotone"
                dataKey={dataKey}
                stroke={accentColor}
                fill={`${accentColor}20`}
              />
            </AreaChart>
          );
          
        case "bar":
          return (
            <BarChart data={data}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />}
              {showXAxis && <XAxis dataKey={nameKey} className="text-muted-foreground" />}
              {showYAxis && <YAxis className="text-muted-foreground" />}
              {showTooltip && (
                <Tooltip
                  contentStyle={{
                    backgroundColor: "hsl(var(--popover))",
                    border: "1px solid hsl(var(--border))",
                    borderRadius: "6px",
                  }}
                />
              )}
              <Bar dataKey={dataKey} fill={accentColor} />
            </BarChart>
          );
          
        case "pie":
          return (
            <PieChart>
              <Pie
                data={data}
                dataKey={dataKey}
                nameKey={nameKey}
                cx="50%"
                cy="50%"
                outerRadius={80}
                innerRadius={40}
                label
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={`${accentColor}${Math.floor(80 + (index * 20) % 100).toString(16)}`} />
                ))}
              </Pie>
              {showTooltip && <Tooltip />}
            </PieChart>
          );
          
        default:
          return null;
      }
    };

    return (
      <div
        ref={ref}
        className={cn("w-full", className)}
        {...props}
      >
        <ResponsiveContainer width="100%" height={height}>
          {renderChart() || <div />}
        </ResponsiveContainer>
      </div>
    );
  }
);
AppChart.displayName = "AppChart";

export { AppChart };
