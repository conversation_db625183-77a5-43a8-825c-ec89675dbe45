import * as React from "react";
import { X, Search } from "lucide-react";
import { cn } from "@/lib/utils";
import { AppInput } from "./app-input";

export interface FilterChip {
  id: string;
  label: string;
  value: string;
  removable?: boolean;
}

export interface FilterBarProps extends React.HTMLAttributes<HTMLDivElement> {
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  searchPlaceholder?: string;
  chips?: FilterChip[];
  onRemoveChip?: (chipId: string) => void;
  actions?: React.ReactNode;
  leftActions?: React.ReactNode;
}

const FilterBar = React.forwardRef<HTMLDivElement, FilterBarProps>(
  ({
    className,
    searchValue = "",
    onSearchChange,
    searchPlaceholder = "Search...",
    chips = [],
    onRemoveChip,
    actions,
    leftActions,
    ...props
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col space-y-4 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4",
          className
        )}
        {...props}
      >
        {leftActions && <div className="flex items-center space-x-2">{leftActions}</div>}
        
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <AppInput
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => onSearchChange?.(e.target.value)}
              className="pl-9"
            />
          </div>
        </div>
        
        {chips.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {chips.map((chip) => (
              <div
                key={chip.id}
                className="flex items-center rounded-full bg-secondary text-secondary-foreground px-3 py-1 text-xs font-medium"
              >
                <span className="mr-1 text-muted-foreground">{chip.label}:</span>
                <span>{chip.value}</span>
                {chip.removable !== false && onRemoveChip && (
                  <button
                    onClick={() => onRemoveChip(chip.id)}
                    className="ml-2 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}
              </div>
            ))}
          </div>
        )}
        
        {actions && <div className="flex items-center space-x-2">{actions}</div>}
      </div>
    );
  }
);
FilterBar.displayName = "FilterBar";

export { FilterBar };
