import * as React from "react";
import { cn } from "@/lib/utils";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";

export interface AppStatProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  value: string | number;
  delta?: {
    value: number;
    type: "positive" | "negative" | "neutral";
    label?: string;
  };
  description?: string;
  icon?: React.ReactNode;
}

const AppStat = React.forwardRef<HTMLDivElement, AppStatProps>(
  ({ className, title, value, delta, description, icon, ...props }, ref) => {
    const getDeltaIcon = () => {
      switch (delta?.type) {
        case "positive":
          return <TrendingUp className="h-4 w-4 text-green-500" />;
        case "negative":
          return <TrendingDown className="h-4 w-4 text-red-500" />;
        default:
          return <Minus className="h-4 w-4 text-gray-500" />;
      }
    };

    const getDeltaColor = () => {
      switch (delta?.type) {
        case "positive":
          return "text-green-600 dark:text-green-400";
        case "negative":
          return "text-red-600 dark:text-red-400";
        default:
          return "text-muted-foreground";
      }
    };

    return (
      <div
        ref={ref}
        className={cn(
          "rounded-lg border bg-card text-card-foreground shadow-sm p-6",
          className
        )}
        {...props}
      >
        <div className="flex items-center justify-between space-y-0 pb-2">
          <div className="text-sm font-medium text-muted-foreground">{title}</div>
          {icon && <div className="text-muted-foreground">{icon}</div>}
        </div>
        <div className="space-y-2">
          <div className="text-2xl font-bold">{value}</div>
          {delta && (
            <div className="flex items-center space-x-1 text-sm">
              {getDeltaIcon()}
              <span className={getDeltaColor()}>
                {delta.value > 0 ? "+" : ""}{delta.value}%
                {delta.label && (
                  <span className="text-muted-foreground ml-1">{delta.label}</span>
                )}
              </span>
            </div>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
      </div>
    );
  }
);
AppStat.displayName = "AppStat";

export { AppStat };
