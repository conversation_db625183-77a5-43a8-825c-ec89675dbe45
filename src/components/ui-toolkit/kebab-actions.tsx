import * as React from "react";
import { MoreVertical } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AppButton } from "./app-button";

export interface KebabActionItem {
  label: string;
  onClick: (e?: React.MouseEvent<Element, MouseEvent>) => void;
  icon?: React.ReactNode;
  variant?: "default" | "destructive";
  separator?: boolean;
}

export interface KebabActionsProps {
  items: KebabActionItem[];
  triggerClassName?: string;
}

const KebabActions = React.forwardRef<HTMLButtonElement, KebabActionsProps>(
  ({ items, triggerClassName }, ref) => {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <AppButton 
            ref={ref}
            variant="ghost" 
            size="icon"
            className={triggerClassName}
          >
            <MoreVertical className="h-4 w-4" />
          </AppButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {items.map((item, index) => (
            <React.Fragment key={index}>
              {item.separator && index > 0 && <DropdownMenuSeparator />}
              <DropdownMenuItem
                onClick={(e) => item.onClick(e)}
                className={item.variant === "destructive" ? "text-destructive" : ""}
              >
                {item.icon && <span className="mr-2">{item.icon}</span>}
                {item.label}
              </DropdownMenuItem>
            </React.Fragment>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }
);
KebabActions.displayName = "KebabActions";

export { KebabActions };
