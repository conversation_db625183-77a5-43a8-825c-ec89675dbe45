import * as React from "react";
import { cn } from "@/lib/utils";
import { AppButton } from "./app-button";

export interface AppEmptyProps extends React.HTMLAttributes<HTMLDivElement> {
  icon?: React.ReactNode;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
  };
}

const AppEmpty = React.forwardRef<HTMLDivElement, AppEmptyProps>(
  ({ className, icon, title, description, action, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col items-center justify-center text-center py-16 px-6",
          className
        )}
        {...props}
      >
        {icon && (
          <div className="mb-4 text-muted-foreground [&_svg]:h-12 [&_svg]:w-12">
            {icon}
          </div>
        )}
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        {description && (
          <p className="text-sm text-muted-foreground mb-6 max-w-md">
            {description}
          </p>
        )}
        {action && (
          <AppButton 
            onClick={action.onClick}
            icon={action.icon}
          >
            {action.label}
          </AppButton>
        )}
      </div>
    );
  }
);
AppEmpty.displayName = "AppEmpty";

export { AppEmpty };
