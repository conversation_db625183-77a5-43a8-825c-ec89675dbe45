'use client'
"use client";

import React, { createContext, useContext, useEffect, useState } from "react";

export type AccentTheme = 
  | "blue" 
  | "lightBlue" 
  | "darkBlue" 
  | "cyan" 
  | "teal" 
  | "turquoise" 
  | "green" 
  | "emerald" 
  | "lime" 
  | "yellow" 
  | "amber" 
  | "orange" 
  | "deepOrange" 
  | "red" 
  | "rose" 
  | "pink" 
  | "fuchsia" 
  | "purple" 
  | "deepPurple" 
  | "indigo" 
  | "violet" 
  | "magenta" 
  | "brown" 
  | "gray" 
  | "coolGray" 
  | "warmGray" 
  | "slate" 
  | "stone";

interface ThemeContextType {
  accent: AccentTheme;
  setAccent: (accent: AccentTheme) => void;
  isDark: boolean;
  setIsDark: (dark: boolean) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useThemeAccent = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useThemeAccent must be used within a ThemeProvider");
  }
  return context;
};

// Custom hook to get theme classes
export const useThemeClasses = () => {
  const { accent } = useThemeAccent();
  return getThemeClasses(accent);
};

const accentThemes: Record<AccentTheme, { accent: string; "accent-foreground": string }> = {
  blue: {
    accent: "210 40% 52%",
    "accent-foreground": "210 40% 98%"
  },
  lightBlue: {
    accent: "200 70% 55%",
    "accent-foreground": "200 70% 98%"
  },
  darkBlue: {
    accent: "220 50% 35%",
    "accent-foreground": "220 50% 98%"
  },
  cyan: {
    accent: "188 78% 41%",
    "accent-foreground": "188 78% 98%"
  },
  teal: {
    accent: "172 66% 50%",
    "accent-foreground": "172 66% 98%"
  },
  turquoise: {
    accent: "178 68% 45%",
    "accent-foreground": "178 68% 98%"
  },
  green: {
    accent: "142 45% 42%",
    "accent-foreground": "142 45% 98%"
  },
  emerald: {
    accent: "158 64% 52%",
    "accent-foreground": "158 64% 98%"
  },
  lime: {
    accent: "84 81% 44%",
    "accent-foreground": "84 81% 15%"
  },
  yellow: {
    accent: "54 91% 55%",
    "accent-foreground": "54 91% 15%"
  },
  amber: {
    accent: "48 70% 45%",
    "accent-foreground": "48 70% 15%"
  },
  orange: {
    accent: "31 90% 55%",
    "accent-foreground": "31 90% 15%"
  },
  deepOrange: {
    accent: "17 88% 50%",
    "accent-foreground": "17 88% 98%"
  },
  red: {
    accent: "0 72% 51%",
    "accent-foreground": "0 72% 98%"
  },
  rose: {
    accent: "351 83% 61%",
    "accent-foreground": "351 83% 98%"
  },
  pink: {
    accent: "322 66% 68%",
    "accent-foreground": "322 66% 15%"
  },
  fuchsia: {
    accent: "292 84% 61%",
    "accent-foreground": "292 84% 98%"
  },
  purple: {
    accent: "271 50% 48%",
    "accent-foreground": "271 50% 98%"
  },
  deepPurple: {
    accent: "262 52% 47%",
    "accent-foreground": "262 52% 98%"
  },
  indigo: {
    accent: "239 84% 67%",
    "accent-foreground": "239 84% 15%"
  },
  violet: {
    accent: "258 90% 66%",
    "accent-foreground": "258 90% 15%"
  },
  magenta: {
    accent: "300 76% 72%",
    "accent-foreground": "300 76% 15%"
  },
  brown: {
    accent: "19 69% 50%",
    "accent-foreground": "19 69% 98%"
  },
  gray: {
    accent: "215 25% 45%",
    "accent-foreground": "215 25% 98%"
  },
  coolGray: {
    accent: "212 22% 47%",
    "accent-foreground": "212 22% 98%"
  },
  warmGray: {
    accent: "43 13% 47%",
    "accent-foreground": "43 13% 98%"
  },
  slate: {
    accent: "215 20% 45%",
    "accent-foreground": "215 20% 98%"
  },
  stone: {
    accent: "37 11% 47%",
    "accent-foreground": "37 11% 98%"
  }
};

// Theme utility classes for different accent colors
export const getThemeClasses = (accent: AccentTheme) => {
  switch (accent) {
    case "blue":
      return {
        background: "from-blue-50 via-slate-50 to-blue-100",
        leftSide: "from-blue-700 via-blue-800 to-blue-900",
        iconBg: "bg-blue-600",
        accent: "bg-blue-600",
        accentHover: "hover:bg-blue-700",
        accentText: "text-blue-600",
        accentBorder: "border-blue-600",
        hoverBg: "hover:bg-blue-50",
        lightBg: "bg-blue-50"
      };
    case "lightBlue":
      return {
        background: "from-sky-50 via-slate-50 to-sky-100",
        leftSide: "from-sky-700 via-sky-800 to-sky-900",
        iconBg: "bg-sky-600",
        accent: "bg-sky-600",
        accentHover: "hover:bg-sky-700",
        accentText: "text-sky-600",
        accentBorder: "border-sky-600",
        hoverBg: "hover:bg-sky-50",
        lightBg: "bg-sky-50"
      };
    case "darkBlue":
      return {
        background: "from-blue-50 via-slate-50 to-blue-100",
        leftSide: "from-blue-800 via-blue-900 to-blue-950",
        iconBg: "bg-blue-800",
        accent: "bg-blue-800",
        accentHover: "hover:bg-blue-900",
        accentText: "text-blue-800",
        accentBorder: "border-blue-800",
        hoverBg: "hover:bg-blue-50",
        lightBg: "bg-blue-50"
      };
    case "cyan":
      return {
        background: "from-cyan-50 via-slate-50 to-cyan-100",
        leftSide: "from-cyan-700 via-cyan-800 to-cyan-900",
        iconBg: "bg-cyan-600",
        accent: "bg-cyan-600",
        accentHover: "hover:bg-cyan-700",
        accentText: "text-cyan-600",
        accentBorder: "border-cyan-600",
        hoverBg: "hover:bg-cyan-50",
        lightBg: "bg-cyan-50"
      };
    case "teal":
      return {
        background: "from-teal-50 via-slate-50 to-teal-100",
        leftSide: "from-teal-700 via-teal-800 to-teal-900",
        iconBg: "bg-teal-600",
        accent: "bg-teal-600",
        accentHover: "hover:bg-teal-700",
        accentText: "text-teal-600",
        accentBorder: "border-teal-600",
        hoverBg: "hover:bg-teal-50",
        lightBg: "bg-teal-50"
      };
    case "turquoise":
      return {
        background: "from-teal-50 via-cyan-50 to-teal-100",
        leftSide: "from-teal-700 via-cyan-800 to-teal-900",
        iconBg: "bg-teal-600",
        accent: "bg-teal-600",
        accentHover: "hover:bg-teal-700",
        accentText: "text-teal-600",
        accentBorder: "border-teal-600",
        hoverBg: "hover:bg-teal-50",
        lightBg: "bg-teal-50"
      };
    case "green":
      return {
        background: "from-green-50 via-slate-50 to-green-100",
        leftSide: "from-green-700 via-green-800 to-green-900",
        iconBg: "bg-green-600",
        accent: "bg-green-600",
        accentHover: "hover:bg-green-700",
        accentText: "text-green-600",
        accentBorder: "border-green-600",
        hoverBg: "hover:bg-green-50",
        lightBg: "bg-green-50"
      };
    case "emerald":
      return {
        background: "from-emerald-50 via-slate-50 to-emerald-100",
        leftSide: "from-emerald-700 via-emerald-800 to-emerald-900",
        iconBg: "bg-emerald-600",
        accent: "bg-emerald-600",
        accentHover: "hover:bg-emerald-700",
        accentText: "text-emerald-600",
        accentBorder: "border-emerald-600",
        hoverBg: "hover:bg-emerald-50",
        lightBg: "bg-emerald-50"
      };
    case "lime":
      return {
        background: "from-lime-50 via-slate-50 to-lime-100",
        leftSide: "from-lime-700 via-lime-800 to-lime-900",
        iconBg: "bg-lime-600",
        accent: "bg-lime-600",
        accentHover: "hover:bg-lime-700",
        accentText: "text-lime-600",
        accentBorder: "border-lime-600",
        hoverBg: "hover:bg-lime-50",
        lightBg: "bg-lime-50"
      };
    case "yellow":
      return {
        background: "from-yellow-50 via-slate-50 to-yellow-100",
        leftSide: "from-yellow-700 via-yellow-800 to-yellow-900",
        iconBg: "bg-yellow-600",
        accent: "bg-yellow-600",
        accentHover: "hover:bg-yellow-700",
        accentText: "text-yellow-600",
        accentBorder: "border-yellow-600",
        hoverBg: "hover:bg-yellow-50",
        lightBg: "bg-yellow-50"
      };
    case "amber":
      return {
        background: "from-amber-50 via-slate-50 to-amber-100",
        leftSide: "from-amber-700 via-amber-800 to-amber-900",
        iconBg: "bg-amber-600",
        accent: "bg-amber-600",
        accentHover: "hover:bg-amber-700",
        accentText: "text-amber-600",
        accentBorder: "border-amber-600",
        hoverBg: "hover:bg-amber-50",
        lightBg: "bg-amber-50"
      };
    case "orange":
      return {
        background: "from-orange-50 via-slate-50 to-orange-100",
        leftSide: "from-orange-700 via-orange-800 to-orange-900",
        iconBg: "bg-orange-600",
        accent: "bg-orange-600",
        accentHover: "hover:bg-orange-700",
        accentText: "text-orange-600",
        accentBorder: "border-orange-600",
        hoverBg: "hover:bg-orange-50",
        lightBg: "bg-orange-50"
      };
    case "deepOrange":
      return {
        background: "from-red-50 via-orange-50 to-red-100",
        leftSide: "from-red-800 via-orange-800 to-red-900",
        iconBg: "bg-red-700",
        accent: "bg-red-700",
        accentHover: "hover:bg-red-800",
        accentText: "text-red-700",
        accentBorder: "border-red-700",
        hoverBg: "hover:bg-red-50",
        lightBg: "bg-red-50"
      };
    case "red":
      return {
        background: "from-red-50 via-slate-50 to-red-100",
        leftSide: "from-red-700 via-red-800 to-red-900",
        iconBg: "bg-red-600",
        accent: "bg-red-600",
        accentHover: "hover:bg-red-700",
        accentText: "text-red-600",
        accentBorder: "border-red-600",
        hoverBg: "hover:bg-red-50",
        lightBg: "bg-red-50"
      };
    case "rose":
      return {
        background: "from-rose-50 via-slate-50 to-rose-100",
        leftSide: "from-rose-700 via-rose-800 to-rose-900",
        iconBg: "bg-rose-600",
        accent: "bg-rose-600",
        accentHover: "hover:bg-rose-700",
        accentText: "text-rose-600",
        accentBorder: "border-rose-600",
        hoverBg: "hover:bg-rose-50",
        lightBg: "bg-rose-50"
      };
    case "pink":
      return {
        background: "from-pink-50 via-slate-50 to-pink-100",
        leftSide: "from-pink-700 via-pink-800 to-pink-900",
        iconBg: "bg-pink-600",
        accent: "bg-pink-600",
        accentHover: "hover:bg-pink-700",
        accentText: "text-pink-600",
        accentBorder: "border-pink-600",
        hoverBg: "hover:bg-pink-50",
        lightBg: "bg-pink-50"
      };
    case "fuchsia":
      return {
        background: "from-fuchsia-50 via-slate-50 to-fuchsia-100",
        leftSide: "from-fuchsia-700 via-fuchsia-800 to-fuchsia-900",
        iconBg: "bg-fuchsia-600",
        accent: "bg-fuchsia-600",
        accentHover: "hover:bg-fuchsia-700",
        accentText: "text-fuchsia-600",
        accentBorder: "border-fuchsia-600",
        hoverBg: "hover:bg-fuchsia-50",
        lightBg: "bg-fuchsia-50"
      };
    case "purple":
      return {
        background: "from-purple-50 via-slate-50 to-purple-100",
        leftSide: "from-purple-700 via-purple-800 to-purple-900",
        iconBg: "bg-purple-600",
        accent: "bg-purple-600",
        accentHover: "hover:bg-purple-700",
        accentText: "text-purple-600",
        accentBorder: "border-purple-600",
        hoverBg: "hover:bg-purple-50",
        lightBg: "bg-purple-50"
      };
    case "deepPurple":
      return {
        background: "from-violet-50 via-purple-50 to-violet-100",
        leftSide: "from-violet-800 via-purple-800 to-violet-900",
        iconBg: "bg-violet-700",
        accent: "bg-violet-700",
        accentHover: "hover:bg-violet-800",
        accentText: "text-violet-700",
        accentBorder: "border-violet-700",
        hoverBg: "hover:bg-violet-50",
        lightBg: "bg-violet-50"
      };
    case "indigo":
      return {
        background: "from-indigo-50 via-slate-50 to-indigo-100",
        leftSide: "from-indigo-700 via-indigo-800 to-indigo-900",
        iconBg: "bg-indigo-600",
        accent: "bg-indigo-600",
        accentHover: "hover:bg-indigo-700",
        accentText: "text-indigo-600",
        accentBorder: "border-indigo-600",
        hoverBg: "hover:bg-indigo-50",
        lightBg: "bg-indigo-50"
      };
    case "violet":
      return {
        background: "from-violet-50 via-slate-50 to-violet-100",
        leftSide: "from-violet-700 via-violet-800 to-violet-900",
        iconBg: "bg-violet-600",
        accent: "bg-violet-600",
        accentHover: "hover:bg-violet-700",
        accentText: "text-violet-600",
        accentBorder: "border-violet-600",
        hoverBg: "hover:bg-violet-50",
        lightBg: "bg-violet-50"
      };
    case "magenta":
      return {
        background: "from-pink-50 via-fuchsia-50 to-pink-100",
        leftSide: "from-pink-700 via-fuchsia-800 to-pink-900",
        iconBg: "bg-pink-600",
        accent: "bg-pink-600",
        accentHover: "hover:bg-pink-700",
        accentText: "text-pink-600",
        accentBorder: "border-pink-600",
        hoverBg: "hover:bg-pink-50",
        lightBg: "bg-pink-50"
      };
    case "brown":
      return {
        background: "from-stone-50 via-amber-50 to-stone-100",
        leftSide: "from-stone-700 via-amber-800 to-stone-900",
        iconBg: "bg-stone-600",
        accent: "bg-stone-600",
        accentHover: "hover:bg-stone-700",
        accentText: "text-stone-600",
        accentBorder: "border-stone-600",
        hoverBg: "hover:bg-stone-50",
        lightBg: "bg-stone-50"
      };
    case "gray":
      return {
        background: "from-gray-50 via-slate-50 to-gray-100",
        leftSide: "from-gray-700 via-gray-800 to-gray-900",
        iconBg: "bg-gray-600",
        accent: "bg-gray-600",
        accentHover: "hover:bg-gray-700",
        accentText: "text-gray-600",
        accentBorder: "border-gray-600",
        hoverBg: "hover:bg-gray-50",
        lightBg: "bg-gray-50"
      };
    case "coolGray":
      return {
        background: "from-gray-50 via-slate-50 to-gray-100",
        leftSide: "from-gray-700 via-gray-800 to-gray-900",
        iconBg: "bg-gray-600",
        accent: "bg-gray-600",
        accentHover: "hover:bg-gray-700",
        accentText: "text-gray-600",
        accentBorder: "border-gray-600",
        hoverBg: "hover:bg-gray-50",
        lightBg: "bg-gray-50"
      };
    case "warmGray":
      return {
        background: "from-neutral-50 via-stone-50 to-neutral-100",
        leftSide: "from-neutral-700 via-neutral-800 to-neutral-900",
        iconBg: "bg-neutral-600",
        accent: "bg-neutral-600",
        accentHover: "hover:bg-neutral-700",
        accentText: "text-neutral-600",
        accentBorder: "border-neutral-600",
        hoverBg: "hover:bg-neutral-50",
        lightBg: "bg-neutral-50"
      };
    case "slate":
      return {
        background: "from-slate-50 via-gray-50 to-slate-100",
        leftSide: "from-slate-700 via-slate-800 to-slate-900",
        iconBg: "bg-slate-600",
        accent: "bg-slate-600",
        accentHover: "hover:bg-slate-700",
        accentText: "text-slate-600",
        accentBorder: "border-slate-600",
        hoverBg: "hover:bg-slate-50",
        lightBg: "bg-slate-50"
      };
    case "stone":
      return {
        background: "from-stone-50 via-neutral-50 to-stone-100",
        leftSide: "from-stone-700 via-stone-800 to-stone-900",
        iconBg: "bg-stone-600",
        accent: "bg-stone-600",
        accentHover: "hover:bg-stone-700",
        accentText: "text-stone-600",
        accentBorder: "border-stone-600",
        hoverBg: "hover:bg-stone-50",
        lightBg: "bg-stone-50"
      };
    default:
      return {
        background: "from-slate-50 via-gray-50 to-slate-100",
        leftSide: "from-slate-700 via-slate-800 to-slate-900",
        iconBg: "bg-slate-600",
        accent: "bg-slate-600",
        accentHover: "hover:bg-slate-700",
        accentText: "text-slate-600",
        accentBorder: "border-slate-600",
        hoverBg: "hover:bg-slate-50",
        lightBg: "bg-slate-50"
      };
  }
};

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [accent, setAccent] = useState<AccentTheme>("blue");
  const [isDark, setIsDark] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Load saved accent theme from localStorage
    const savedAccent = localStorage.getItem("tickflo-accent-theme") as AccentTheme;
    if (savedAccent && accentThemes[savedAccent]) {
      setAccent(savedAccent);
    }

    // Load saved dark mode preference
    const savedDark = localStorage.getItem("tickflo-dark-mode");
    if (savedDark !== null) {
      setIsDark(savedDark === "true");
    } else {
      // Default to light mode
      setIsDark(false);
    }

    // Mark as loaded after setting initial values
    setIsLoaded(true);
  }, []);

  useEffect(() => {
    // Apply accent theme
    const root = document.documentElement;
    const theme = accentThemes[accent];
    
    root.style.setProperty("--accent", `hsl(${theme.accent})`);
    root.style.setProperty("--accent-foreground", `hsl(${theme["accent-foreground"]})`);
    root.style.setProperty("--ring", `hsl(${theme.accent})`);
    
    localStorage.setItem("tickflo-accent-theme", accent);
  }, [accent]);

  useEffect(() => {
    // Apply dark mode
    const root = document.documentElement;
    if (isDark) {
      root.classList.add("dark");
    } else {
      root.classList.remove("dark");
    }
    localStorage.setItem("tickflo-dark-mode", isDark.toString());
  }, [isDark]);

  // Prevent rendering until theme is loaded to avoid flash
  if (!isLoaded) {
    return null;
  }

  return (
    <ThemeContext.Provider value={{ accent, setAccent, isDark, setIsDark }}>
      {children}
    </ThemeContext.Provider>
  );
}
