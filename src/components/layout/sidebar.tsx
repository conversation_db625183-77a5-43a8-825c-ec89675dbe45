'use client'
import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Home,
  Ticket,
  Users,
  Building,
  Shield,
  BookOpen,
  Activity,
  BarChart3,
  TrendingUp,
  MessageSquare,
  Mail,
  Settings,
  Download,
  ChevronRight,
  ChevronLeft,
  Grid3X3,
  FolderKanban,
  Plus,
  ChevronDown,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { AppButton } from "@/components/ui-toolkit";
import { useThemeClasses } from "@/components/theme-provider";
import { useAuth } from "@/hooks/useAuth";

interface SidebarItem {
  title: string;
  href: string;
  icon: React.ElementType;
  badge?: string;
  subItems?: SidebarItem[];
}

interface SidebarSection {
  title: string;
  items: SidebarItem[];
}

const sidebarSections: SidebarSection[] = [
  {
    title: "Core",
    items: [
      { title: "Dashboard", href: "/dashboard", icon: Home },
      // { title: "Projects", href: "/projects", icon: Folder<PERSON>anban },
      { title: "Tickets", href: "/tickets", icon: Ticket, badge: "12", subItems: [
        { title: "Merged Tickets", href: "/merge-tickets", icon: Ticket }
      ] },
      { title: "Canned Responses", href: "/canned-responses", icon: MessageSquare },
      // { title: "ZGrid Examples", href: "/zgrid-examples", icon: Grid3X3 },
      { title: "Email-to-Ticket", href: "/email-to-ticket", icon: Mail },
    ],
  },
  {
    title: "People",
    items: [
      { title: "Users", href: "/users", icon: Users },
      { title: "Departments", href: "/departments", icon: Building },
      { title: "Roles & Permissions", href: "/roles", icon: Shield },
    ],
  },
  {
    title: "Knowledge & Tracking",
    items: [
      { title: "Knowledge Base", href: "/knowledge", icon: BookOpen },
      { title: "Audit Log", href: "/audit", icon: Activity },
      { title: "Reports", href: "/reports", icon: BarChart3 },
      // { title: "Charts", href: "/charts", icon: TrendingUp },
    ],
  },
  {
    title: "Configuration",
    items: [
      // { title: "Tenants", href: "/tenants", icon: Building },
      { title: "Settings", href: "/settings", icon: Settings ,subItems : [
{ title: "Export/Import", href: "/settings/export", icon: Download }
      ]},
    ],
  },
];

interface SidebarProps {
  collapsed: boolean;
  onToggle: () => void;
}

export function Sidebar({ collapsed, onToggle }: SidebarProps) {
  const pathname = usePathname();
  const themeClasses = useThemeClasses();
  const { tenant } = useAuth();
  const [expandedItems, setExpandedItems] = React.useState<Set<string>>(new Set());

  const toggleExpanded = (title: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(title)) {
        newSet.delete(title);
      } else {
        newSet.add(title);
      }
      return newSet;
    });
  };

  const isActiveRoute = (href: string) => {
    if (href === "/") {
      return pathname === "/";
    }
    return pathname.startsWith(href);
  };

  return (
    <div
      className={cn(
        "flex h-full flex-col border-r bg-sidebar transition-all duration-300",
        collapsed ? "w-16" : "w-56"
      )}
    >
      <div className="flex h-14 items-center justify-between px-4">
        {!collapsed && (
          <div className="flex items-center space-x-2">
            <div className={`flex h-6 w-6 items-center justify-center rounded ${themeClasses.accent} text-white`}>
              {tenant?.name?.charAt(0).toUpperCase() || "T"}
            </div>
            <span className="text-base font-semibold">{tenant?.name || "Tickflo"}</span>
          </div>
        )}
        <AppButton
          variant="ghost"
          size="icon"
          onClick={onToggle}
          className="h-6 w-6"
        >
          {collapsed ? (
            <ChevronRight className="h-3.5 w-3.5" />
          ) : (
            <ChevronLeft className="h-3.5 w-3.5" />
          )}
        </AppButton>
      </div>

      <div className="flex-1 overflow-y-auto px-2 py-1.5">
        <div className="space-y-6">
          {sidebarSections.map((section, sectionIndex) => (
            <div key={sectionIndex}>
              {!collapsed && (
                <div className="px-2 py-1.5">
                  <h2 className="mb-2 text-xs font-semibold uppercase tracking-wide text-muted-foreground">
                    {section.title}
                  </h2>
                </div>
              )}
              <div className="space-y-1">
                {section.items.map((item, itemIndex) => {
                  const isActive = isActiveRoute(item.href);
                  const Icon = item.icon;
                  const hasSubItems = item.subItems && item.subItems.length > 0;
                  const isExpanded = expandedItems.has(item.title);

                  return (
                    <div key={itemIndex}>
                      <div className="group flex items-center">
                        {hasSubItems ? (
                          <>
                            <Link
                              href={item.href}
                              className={cn(
                                "flex items-center rounded-lg px-2 py-1.5 text-xs font-medium transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground flex-1",
                                isActive
                                  ? `${themeClasses.accent} text-white`
                                  : "text-sidebar-foreground"
                              )}
                            >
                              <Icon className="h-3.5 w-3.5 shrink-0" />
                              {!collapsed && (
                                <>
                                  <span className="ml-3">{item.title}</span>
                                  {item.badge && (
                                    <span className={`ml-auto rounded-full ${themeClasses.accent} px-2 py-0.5 text-xs text-white`}>
                                      {item.badge}
                                    </span>
                                  )}
                                </>
                              )}
                            </Link>
                            {!collapsed && (
                              <AppButton
                                variant="ghost"
                                size="icon"
                                onClick={() => toggleExpanded(item.title)}
                                className="h-6 w-6 ml-1"
                              >
                                <ChevronDown className={cn("h-3.5 w-3.5 transition-transform", isExpanded ? "rotate-180" : "")} />
                              </AppButton>
                            )}
                          </>
                        ) : (
                          <Link
                            href={item.href}
                            className={cn(
                              "flex items-center rounded-lg px-2 py-1.5 text-xs font-medium transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground flex-1",
                              isActive
                                ? `${themeClasses.accent} text-white`
                                : "text-sidebar-foreground"
                            )}
                          >
                            <Icon className="h-3.5 w-3.5 shrink-0" />
                            {!collapsed && (
                              <>
                                <span className="ml-3">{item.title}</span>
                                {item.badge && (
                                  <span className={`ml-auto rounded-full ${themeClasses.accent} px-2 py-0.5 text-xs text-white`}>
                                    {item.badge}
                                  </span>
                                )}
                              </>
                            )}
                          </Link>
                        )}
                        {!collapsed && item.title === "Projects" && (
                          <AppButton
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 ml-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => {
                              // TODO: Open create project modal
                              console.log("Create new project");
                            }}
                          >
                            <Plus className="h-3.5 w-3.5" />
                          </AppButton>
                        )}
                      </div>
                      {!collapsed && isExpanded && hasSubItems && (
                        <div className="ml-4 mt-1 space-y-1">
                          {item.subItems!.map((subItem, subIndex) => {
                            const isSubActive = isActiveRoute(subItem.href);
                            const SubIcon = subItem.icon;
                            return (
                              <Link
                                key={subIndex}
                                href={subItem.href}
                                className={cn(
                                  "flex items-center rounded-lg px-2 py-1.5 text-xs font-medium transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                                  isSubActive
                                    ? `${themeClasses.accent} text-white`
                                    : "text-sidebar-foreground"
                                )}
                              >
                                <SubIcon className="h-3.5 w-3.5 shrink-0" />
                                <span className="ml-3">{subItem.title}</span>
                              </Link>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
