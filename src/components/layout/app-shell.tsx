'use client'
import * as React from "react";
import { usePathname } from "next/navigation";
import { Sidebar } from "./sidebar";
import { Navbar } from "./navbar";

interface AppShellProps {
  children: React.ReactNode;
}

// Routes where sidebar should be hidden
const SIDEBAR_HIDDEN_ROUTES: string[] = [
  // Add specific routes here where you want to hide the sidebar
  // Example: "/login", "/register"
];

// Routes where navbar should be hidden
const NAVBAR_HIDDEN_ROUTES = [
  "/tickets",
  "/create",
  // Add more routes here as needed
  // "/login",
  // "/auth/register",
];

export function AppShell({ children }: AppShellProps) {
  const pathname = usePathname();
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);

  // Check if current route should hide sidebar
  const shouldHideSidebar = SIDEBAR_HIDDEN_ROUTES.some(route => 
    pathname === route || pathname.startsWith(route + "/")
  );

  // Check if current route should hide navbar
  const shouldHideNavbar = NAVBAR_HIDDEN_ROUTES.some(route => 
    pathname === route || pathname.startsWith(route + "/")
  );

  return (
    <div className="flex h-screen">
      {!shouldHideSidebar && (
        <Sidebar
          collapsed={sidebarCollapsed}
          onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        />
      )}
      <div className="flex flex-1 flex-col overflow-hidden">
        {!shouldHideNavbar && <Navbar />}
        <main className="flex-1 overflow-auto p-4">
          {children}
        </main>
      </div>
    </div>
  );
}
