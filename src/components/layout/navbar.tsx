"use client";

import * as React from "react";
import { useRouter, usePathname } from "next/navigation";
import { Search, Plus, Bell, X } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import {
  AppButton,
  AppInput,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  Avatar,
  AvatarFallback,
  AvatarImage,
  AppBadge,
  AppToggle,
  PageLoader,
} from "@/components/ui-toolkit";
import { logoutUser } from "@/redux/slices/auth";
import { useAppDispatch } from "@/redux/store";
import axios from "@/lib/axios";
interface NavbarProps {
  currentTenant?: string;
}



export function Navbar({ currentTenant = "Cyranix" }: NavbarProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [searchQuery, setSearchQuery] = React.useState("");
  const { logout, user } = useAuth();
  const [debouncedQuery, setDebouncedQuery] = React.useState("");
  const [results, setResults] = React.useState<any[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isOpen, setIsOpen] = React.useState(false);
  const [isSwitching, setIsSwitching] = React.useState(false);
  const [isOperatorMode, setIsOperatorMode] = React.useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('operatorMode');
      return saved ? JSON.parse(saved) : !pathname.startsWith('/self-service');
    }
    return true;
  });

  React.useEffect(() => {
    // localStorage.setItem('operatorMode', JSON.stringify(isOperatorMode));
    if (isOperatorMode) {
      if (pathname.startsWith('/self-service')) {
        setIsSwitching(true);
        setTimeout(() => {
          router.push('/dashboard');
          setTimeout(() => setIsSwitching(false), 500);
        }, 1000);
      }
    } else {
      if (!pathname.startsWith('/self-service')) {
        setIsSwitching(true);
        setTimeout(() => {
          router.push('/self-service');
          setTimeout(() => setIsSwitching(false), 500);
        }, 1000);
      }
    }
  }, [isOperatorMode, pathname, router]);

  React.useEffect(() => {
    if (user?.userType === "2") {
      localStorage.setItem('operatorMode', JSON.stringify(isOperatorMode));
    }
  }, [isOperatorMode, user?.userType]);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchQuery]);

  React.useEffect(() => {
    if (debouncedQuery.trim() === "") {
      setResults([]);
      setIsOpen(false);
      return;
    }
    setIsLoading(true);
    axios.get(`/search/unified?q=${encodeURIComponent(debouncedQuery)}&limit=20&offset=0`)
      .then((response) => {
        const data = response.data as any;
        if (data.success) {
          setResults(data.data.results);
          setIsOpen(true);
        }
      })
      .catch((err: any) => {
        console.error(err);
        setResults([]);
      })
      .then(() => setIsLoading(false));
  }, [debouncedQuery]);

  const handleResultClick = (result: any) => {
    if (result.type === 'user') {
      router.push('/users');
    } else if (result.type === 'ticket') {
      router.push(`/tickets?ticket=${result.data.ticketKey}`);
    }
    setIsOpen(false);
  };

  const handleSignOut = () => {
    logout(); // Redux state reset
    router.push("/auth/login"); // Route to login
  };

  return (
    <>
      <header className="flex h-14 items-center justify-between border-b bg-background px-4">
        <div className="flex items-center space-x-4">
          {
            user?.userType == "0" ? (
              <></>
            ) : user?.userType == "1" ? (<></>) :
              (
                <AppToggle
                  checked={isOperatorMode}
                  onCheckedChange={setIsOperatorMode}
                  leftLabel="Self Service"
                  rightLabel="Operator"
                  isLeftAreaEnabled={true}
                  isRightAreaEnabled={true}
                />
              )
          }

        </div>

        <div className="flex flex-1 items-center justify-center px-4">
          <div className="relative w-full max-w-xl">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <AppInput
              placeholder="Search tickets, users, articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 pr-9"
            />
            {searchQuery && (
              <X className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground cursor-pointer" onClick={() => setSearchQuery("")} />
            )}
            {isOpen && (
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 bg-background border rounded-md shadow-lg z-50 w-full max-h-80 overflow-y-auto animate-in fade-in-0 slide-in-from-top-2">
                {isLoading ? (
                  <div className="p-4 text-center">Loading...</div>
                ) : results.length === 0 ? (
                  <div className="p-4 text-center text-muted-foreground">No results found</div>
                ) : (
                  results.map((result, index) => (
                    <div key={index} className="p-4 border-b last:border-b-0 hover:bg-accent cursor-pointer" onClick={() => handleResultClick(result)}>
                      <div className="flex items-center gap-2 mb-2">
                        <AppBadge variant="secondary" className="capitalize">{result.type}</AppBadge>
                      </div>
                      {result.type === 'user' ? (
                        <div>
                          <div className="font-medium">{result.data.displayName}</div>
                          <div className="text-sm text-muted-foreground">{result.data.email}</div>
                          <div className="text-xs text-muted-foreground">Role: {result.data.role} | Status: {result.data.status}</div>
                        </div>
                      ) : result.type === 'ticket' ? (
                        <div>
                          <div className="font-medium">{result.data.ticketKey}: {result.data.title}</div>
                          <div className="text-sm text-muted-foreground">{result.data.description}</div>
                          <div className="text-xs text-muted-foreground">Priority: {result.data.priority} | Status: {result.data.status}</div>
                        </div>
                      ) : (
                        <div>Unknown type</div>
                      )}
                    </div>
                  ))
                )}
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <AppButton variant="ghost" size="icon">
                <Plus className="h-4 w-4" />
              </AppButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Quick Create</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => router.push('/create')}>Create New</DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push('/users')}>Invite User</DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push('/departments')}>New Department</DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push('/knowledge')}>Knowledge Article</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <AppButton variant="ghost" size="icon" className="relative">
                <Bell className="h-4 w-4" />
                <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-accent text-xs"></span>
              </AppButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <DropdownMenuLabel>Notifications</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <div className="max-h-80 overflow-y-auto">
                <DropdownMenuItem className="flex flex-col items-start p-4">
                  <div className="flex w-full items-center justify-between">
                    <span className="font-medium">New ticket assigned</span>
                    <span className="text-xs text-muted-foreground">2m ago</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    TKT-001: Cannot access email account
                  </p>
                </DropdownMenuItem>
                <DropdownMenuItem className="flex flex-col items-start p-4">
                  <div className="flex w-full items-center justify-between">
                    <span className="font-medium">SLA breach warning</span>
                    <span className="text-xs text-muted-foreground">5m ago</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    TKT-003 will breach SLA in 30 minutes
                  </p>
                </DropdownMenuItem>
                <DropdownMenuItem className="flex flex-col items-start p-4">
                  <div className="flex w-full items-center justify-between">
                    <span className="font-medium">Ticket updated</span>
                    <span className="text-xs text-muted-foreground">10m ago</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    TKT-002: Customer replied to ticket
                  </p>
                </DropdownMenuItem>
                <DropdownMenuItem className="flex flex-col items-start p-4">
                  <div className="flex w-full items-center justify-between">
                    <span className="font-medium">New department created</span>
                    <span className="text-xs text-muted-foreground">1h ago</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    IT Support department has been created
                  </p>
                </DropdownMenuItem>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-center p-3 text-primary hover:bg-accent cursor-pointer"
                onClick={() => router.push('/notifications')}
              >
                Show More
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <AppButton variant="ghost" className="flex items-center space-x-2">
                <Avatar className="h-8 w-8">
                  {/* <AvatarImage src="/placeholder-avatar.jpg" /> */}
                  <AvatarFallback>{getInitials(user?.displayName || "User")}</AvatarFallback>
                </Avatar>
                <span className="hidden sm:inline-block">{user?.displayName || "User"}</span>
              </AppButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {/* <DropdownMenuLabel>My Account</DropdownMenuLabel> */}
              <DropdownMenuItem disabled className="text-sm text-muted-foreground">
                Role: {user?.role || "N/A"}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => router.push('/profile')}>Profile</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut}>Sign Out</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>
      {isSwitching && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
          <PageLoader
            variant="hash"
            size="lg"
            title={isOperatorMode ? "Switching to Operator Portal..." : "Switching to Self Service Portal..."}
            subtitle="Please wait while we prepare your experience"
          />
        </div>
      )}
    </>
  );
}
