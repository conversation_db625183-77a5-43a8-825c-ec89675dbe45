"use client";

import React, { useState } from 'react';
import { App<PERSON><PERSON><PERSON>, AppCard, App<PERSON>ard<PERSON>ontent, AppCardHeader, AppCardTitle } from '@/components/ui-toolkit';
import { manualRefreshToken, checkTokenExpiry, logTokenInfo } from '@/utils/tokenRefresh';
import { toast } from 'sonner';

export default function TokenRefreshTest() {
  const [loading, setLoading] = useState(false);
  const [tokenInfo, setTokenInfo] = useState<any>(null);

  const handleRefreshToken = async () => {
    setLoading(true);
    try {
      await manualRefreshToken();
      toast.success('Token refreshed successfully!');
      updateTokenInfo();
    } catch (error: any) {
      toast.error(`Token refresh failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCheckToken = () => {
    const isExpired = checkTokenExpiry();
    updateTokenInfo();
    
    if (isExpired) {
      toast.warning('Token is expired or expires soon');
    } else {
      toast.success('Token is still valid');
    }
  };

  const updateTokenInfo = () => {
    const token = localStorage.getItem('accessToken');
    if (!token) {
      setTokenInfo(null);
      return;
    }

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const now = Math.floor(Date.now() / 1000);
      const expiresIn = payload.exp - now;
      const expiresAt = new Date(payload.exp * 1000).toLocaleString();
      
      setTokenInfo({
        userId: payload.userId,
        email: payload.email,
        role: payload.role,
        tenantId: payload.tenantId,
        expiresAt,
        expiresInSeconds: expiresIn,
        expiresInMinutes: Math.floor(expiresIn / 60),
        isExpired: expiresIn <= 0,
        expiresWithin5Min: expiresIn < 300
      });
    } catch (error) {
      setTokenInfo(null);
    }
  };

  React.useEffect(() => {
    updateTokenInfo();
    logTokenInfo();
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <AppCard>
        <AppCardHeader>
          <AppCardTitle>Token Refresh Test</AppCardTitle>
        </AppCardHeader>
        <AppCardContent className="space-y-6">
          {/* Action Buttons */}
          <div className="flex gap-4">
            <AppButton 
              onClick={handleRefreshToken}
              disabled={loading}
            >
              {loading ? 'Refreshing...' : 'Manual Refresh Token'}
            </AppButton>
            <AppButton 
              variant="outline"
              onClick={handleCheckToken}
            >
              Check Token Status
            </AppButton>
            <AppButton 
              variant="outline"
              onClick={updateTokenInfo}
            >
              Update Info
            </AppButton>
          </div>

          {/* Token Information */}
          {tokenInfo ? (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Current Token Information</h3>
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>User ID:</strong> {tokenInfo.userId}
                  </div>
                  <div>
                    <strong>Email:</strong> {tokenInfo.email}
                  </div>
                  <div>
                    <strong>Role:</strong> {tokenInfo.role}
                  </div>
                  <div>
                    <strong>Tenant ID:</strong> {tokenInfo.tenantId}
                  </div>
                  <div className="md:col-span-2">
                    <strong>Expires At:</strong> {tokenInfo.expiresAt}
                  </div>
                  <div>
                    <strong>Expires In:</strong> {tokenInfo.expiresInMinutes} minutes ({tokenInfo.expiresInSeconds} seconds)
                  </div>
                  <div className={`font-semibold ${tokenInfo.isExpired ? 'text-red-600' : tokenInfo.expiresWithin5Min ? 'text-yellow-600' : 'text-green-600'}`}>
                    <strong>Status:</strong> {
                      tokenInfo.isExpired 
                        ? 'EXPIRED' 
                        : tokenInfo.expiresWithin5Min 
                          ? 'EXPIRES SOON' 
                          : 'VALID'
                    }
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No access token found
            </div>
          )}

          {/* Usage Instructions */}
          <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h4 className="font-semibold mb-2">Testing Instructions:</h4>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Login to get an access token (expires in 15 minutes)</li>
              <li>Wait for token to expire or use browser console: <code>window.logToken()</code></li>
              <li>Make an API call that requires authentication - it should auto-refresh</li>
              <li>Or manually test refresh with the button above</li>
              <li>Check browser console for refresh logs and token info</li>
            </ol>
          </div>

          {/* Console Utilities */}
          <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <h4 className="font-semibold mb-2">Browser Console Utilities:</h4>
            <ul className="space-y-1 text-sm">
              <li><code>window.refreshToken()</code> - Manually refresh token</li>
              <li><code>window.checkToken()</code> - Check if token is expired</li>
              <li><code>window.logToken()</code> - Log detailed token information</li>
            </ul>
          </div>
        </AppCardContent>
      </AppCard>
    </div>
  );
}