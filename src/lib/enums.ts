export const TICKET_STATUSES = [
  'recent',
  'open',
  'in_progress',
  'blocked',
  'on_hold',
  'resolved',
  'closed',
  'archived',
] as const

export type TicketStatus = (typeof TICKET_STATUSES)[number]
export const TICKET_PRIORITIES = ['Normal','Top Priority', 'Urgent', 'Low', 'Later'] as const
export type TicketPriority = (typeof TICKET_PRIORITIES)[number]

export const TYPES = [
  { value: 0, label: 'story' },
  { value: 1, label: 'ticket' },
  { value: 2, label: 'Bug/Issue' },
  { value: 3, label: 'Create' }
] as const

export type Type = (typeof TYPES)[number]

export const PAGE_SIZE_OPTIONS = [10, 20, 50, 100] as const
export type PageSize = (typeof PAGE_SIZE_OPTIONS)[number]

export const STATUS_OPTIONS = TICKET_STATUSES.map((s) => ({ label: s.replace(/_/g, ' ').toUpperCase(), value: s }))
export const PRIORITY_OPTIONS = TICKET_PRIORITIES.map((p) => ({ label: p, value: p }))
