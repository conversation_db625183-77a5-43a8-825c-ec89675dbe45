
import type { BaseQueryFn } from '@reduxjs/toolkit/query';
import axios from 'axios';


export const axiosBaseQuery =
  ({ baseUrl = '', prefixUrl = '' }: { baseUrl?: string; prefixUrl?: string } = {}): BaseQueryFn<{
    url: string;
    method: string;
    data?: any;
    params?: any;
    headers?: any;
  }, unknown, unknown> =>
  async ({ url, method, data, params, headers }) => {
    try {
      // Only access localStorage in browser environment
      let token = null;
      if (typeof window !== 'undefined') {
        token = localStorage.getItem('accessToken');
      }
      
      console.log('Axios Base Query - Making request:', {
        url: `${prefixUrl}${baseUrl}${url}`,
        method,
        data,
        params,
        headers: {
          ...headers,
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
      });
      
      const result = await axios({
        url: `${prefixUrl}${baseUrl}${url}`,
        method,
        data,
        params,
        headers: {
          ...headers,
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
      });
      
      return { data: result.data };
    } catch (axiosError: any) {
      console.error('Axios Base Query - Error:', axiosError);
      return {
        error: {
          status: axiosError.response?.status,
          data: axiosError.response?.data || axiosError.message,
          message: axiosError.message,
        },
      };
    }
  };
