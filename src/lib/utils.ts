import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { formatDistanceToNow, format } from "date-fns"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getRelativeTime(date: string | Date) {
  const dateObj = new Date(date);
  return formatDistanceToNow(dateObj, { addSuffix: true });
}

export function getFullDate(date: string | Date) {
  const dateObj = new Date(date);
  return format(dateObj, "d MMMM, yyyy h:mm a");
}
