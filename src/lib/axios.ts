
import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';

const axiosInstance = axios.create({
  baseURL: API_URL,
});

// Token refresh state management
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (error?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });
  
  failedQueue = [];
};

// Helper to check if token is expired or about to expire
const isTokenExpired = (token: string | null): boolean => {
  if (!token) return true;
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const now = Math.floor(Date.now() / 1000);
    // Check if token expires within 5 minutes (300 seconds)
    return payload.exp && (payload.exp - now) < 300;
  } catch (error) {
    return true;
  }
};

// Helper to refresh token
const refreshAuthToken = async (): Promise<string | null> => {
  const refreshToken = localStorage.getItem('refreshToken');
  
  if (!refreshToken) {
    throw new Error('No refresh token available');
  }

  try {
    const response = await axios.post(`${API_URL}/tenant/refresh`, {
      refreshToken
    });

    const data = response.data as any;
    if (data.success) {
      const { accessToken, refreshToken: newRefreshToken } = data.data.tokens;
      const { user } = data.data;

      // Store new tokens
      localStorage.setItem('accessToken', accessToken);
      localStorage.setItem('refreshToken', newRefreshToken);
      localStorage.setItem('user', JSON.stringify(user));

      return accessToken;
    }

    throw new Error('Token refresh failed');
  } catch (error: any) {
    // Clear stored data if refresh fails
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    localStorage.removeItem('tenant');
    throw error;
  }
};

// Request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    // Ensure headers object exists
    config.headers = config.headers || {};
    
    // Only add token in browser environment
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('accessToken');
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
      }
    }
    
    // Only set Content-Type for non-FormData
    if (config.data && !(config.data instanceof FormData)) {
      config.headers['Content-Type'] = 'application/json';
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Helper for unauthenticated handling
function handleUnauthenticated() {
  // Only handle in browser environment
  if (typeof window !== 'undefined') {
    const currentPath = window.location.pathname;
    // Only redirect if we're not already on the login page
    if (currentPath !== '/auth/login') {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      localStorage.removeItem('tenant');
      // Use a more reliable way to redirect
      window.location.replace('/auth/login');
    }
  }
}

// Response interceptor with token refresh logic
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error: any) => {
    const originalRequest = error.config;
    
    // Skip auth redirect if flag is set
    if (originalRequest.skipAuthRedirect) {
      return Promise.reject(error);
    }
    
    // Handle 401 errors with token refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      // Skip refresh for refresh endpoint itself and login endpoint
      if (originalRequest.url?.includes('/tenant/refresh') || originalRequest.url?.includes('/tenant/login')) {
        handleUnauthenticated();
        return Promise.reject(error);
      }

      if (isRefreshing) {
        // Wait for ongoing refresh
        return new Promise((resolve, reject) => {
          failedQueue.push({ 
            resolve: (token: string) => {
              originalRequest.headers['Authorization'] = `Bearer ${token}`;
              resolve(axiosInstance(originalRequest));
            }, 
            reject 
          });
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const newToken = await refreshAuthToken();
        
        if (newToken) {
          originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
          processQueue(null, newToken);
          return axiosInstance(originalRequest);
        }
      } catch (refreshError) {
        processQueue(refreshError, null);
        handleUnauthenticated();
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }
    
    // Handle other authentication errors
    if (error.response?.status === 403) {
      handleUnauthenticated();
    }
    
    // GraphQL error handling with type guard
    const data = error.response?.data as { errors?: Array<{ code?: string }> };
    if (Array.isArray(data?.errors) && data.errors.length > 0) {
      const graphqlError = data.errors[0];
      if (graphqlError.code === 'UNAUTHENTICATED') {
        handleUnauthenticated();
      }
    }
    
    return Promise.reject(error);
  }
);

export default axiosInstance;
