export interface Ticket {
  id: string;
  number: string;
  ticketKey?: string;
  subject: string;
  status: "new" | "triage" | "in_progress" | "waiting" | "resolved" | "closed" | "recent";
  priority: "low" | "medium" | "high" | "urgent" | "Top Priority" | "Urgent" | "Normal" | "Low" | "Later";
  assignee?: User;
  requester: any;
  department: Department;
  category?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  slaTarget?: Date;
  description?: string;
  estimatedTime?: number;
  resolveBy?: string;
  startDate?: Date;
  dueDate?: Date;
  canDescriptionAndTitleChange?: boolean;
  subTaskCount?: number;
  subTasks?: SubTask[];
  mergedTickets?: MergedTicket[];
  parentTicketId?: string;
}

export interface Comment {
  id: string;
  ticketId: string;
  author: User;
  type: "public" | "internal";
  body: string;
  attachments: string[];
  createdAt: Date;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: Role;
  department: Department;
  status: "active" | "inactive";
  avatar?: string;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  type: "system" | "custom";
  userType: "systemAdmin" | "member" | "agent";
  permissions: string[];
  isActive: boolean;
  userCount?: number;
}

export interface Department {
  id: string;
  name: string;
  description?: string;
  status: "active" | "inactive";
  parentId?: string;
  manager?: User;
  memberCount: number;
}

export interface Tenant {
  id: string;
  name: string;
  slug: string;
  status: "active" | "inactive" | "suspended";
  entitlements: {
    tickets: boolean;
    knowledge: boolean;
    reports: boolean;
  };
}

export interface CannedResponse {
  id: string;
  title: string;
  content: string;
  tags: string[];
  category?: string;
}

export interface KnowledgeArticle {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  author: User;
  createdAt: Date;
  updatedAt: Date;
  helpful: number;
  notHelpful: number;
}

export interface AuditLog {
  id: string;
  actor: User;
  action: string;
  entity: string;
  entityId: string;
  details: Record<string, unknown>;
  createdAt: Date;
}

export interface SubTask {
  id: string;
  title: string;
  description?: string;
  status: string;
  priority: string;
  assignee?: {
    name: string;
  };
  canDescriptionAndTitleChange?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface MergedTicket {
  id: string;
  ticketKey: string;
  title: string;
  commentCount: number;
  priority: string;
  status: string;
  createdBy: {
    id: string;
    name: string;
    role: string;
  };
}
