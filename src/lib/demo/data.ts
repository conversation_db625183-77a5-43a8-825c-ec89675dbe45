import { 
  Tenant, 
  User, 
  Role, 
  Department, 
  Ticket, 
  Comment, 
  CannedResponse, 
  KnowledgeArticle, 
  AuditLog 
} from "./types";

// Demo Tenants
export const demoTenants: Tenant[] = [
  {
    id: "1",
    name: "<PERSON>ranix",
    slug: "cyranix",
    status: "active",
    entitlements: {
      tickets: true,
      knowledge: true,
      reports: true,
    },
  },
  {
    id: "2",
    name: "Acme Labs",
    slug: "acme-labs",
    status: "active",
    entitlements: {
      tickets: true,
      knowledge: false,
      reports: true,
    },
  },
];

// Demo Roles
export const demoRoles: Role[] = [
  {
    id: "1",
    name: "Administrator",
    description: "Full system access with all permissions",
    type: "system",
    userType: "systemAdmin",
    permissions: [
      "tickets.view", "tickets.create", "tickets.edit", "tickets.delete", "tickets.assign",
      "users.view", "users.create", "users.edit", "users.delete",
      "departments.view", "departments.create", "departments.edit", "departments.delete",
      "roles.view", "roles.create", "roles.edit", "roles.delete",
      "settings.view", "settings.edit",
      "reports.view", "reports.create",
      "knowledge.view", "knowledge.create", "knowledge.edit", "knowledge.delete",
      "canned_responses.view", "canned_responses.create", "canned_responses.edit", "canned_responses.delete"
    ],
    isActive: true,
    userCount: 2,
  },
  {
    id: "2",
    name: "Agent",
    description: "Can manage tickets and view reports",
    type: "system",
    userType: "agent",
    permissions: [
      "tickets.view", "tickets.create", "tickets.edit", "tickets.assign",
      "users.view",
      "reports.view",
      "knowledge.view", "knowledge.create", "knowledge.edit",
      "canned_responses.view", "canned_responses.create", "canned_responses.edit"
    ],
    isActive: true,
    userCount: 8,
  },
  {
    id: "3",
    name: "End User",
    description: "Can create and view own tickets",
    type: "system",
    userType: "member",
    permissions: [
      "tickets.view", "tickets.create"
    ],
    isActive: true,
    userCount: 25,
  },
];

// Demo Departments
export const demoDepartments: Department[] = [
  {
    id: "1",
    name: "IT Support",
    description: "Technology support and infrastructure",
    status: "active",
    memberCount: 12,
  },
  {
    id: "2",
    name: "Helpdesk",
    description: "First-line user support",
    status: "active",
    parentId: "1",
    memberCount: 6,
  },
  {
    id: "3",
    name: "Infrastructure",
    description: "Server and network management",
    status: "active",
    parentId: "1",
    memberCount: 4,
  },
  {
    id: "4",
    name: "Human Resources",
    description: "Employee relations and support",
    status: "active",
    memberCount: 8,
  },
  {
    id: "5",
    name: "Finance",
    description: "Financial operations and reporting",
    status: "active",
    memberCount: 5,
  },
];

// Demo Users
export const demoUsers: User[] = [
  {
    id: "1",
    name: "John Doe",
    email: "<EMAIL>",
    role: demoRoles[0],
    department: demoDepartments[0],
    status: "active",
  },
  {
    id: "2",
    name: "Jane Smith",
    email: "<EMAIL>",
    role: demoRoles[1],
    department: demoDepartments[1],
    status: "active",
  },
  {
    id: "3",
    name: "Mike Johnson",
    email: "<EMAIL>",
    role: demoRoles[1],
    department: demoDepartments[2],
    status: "active",
  },
  {
    id: "4",
    name: "Sarah Wilson",
    email: "<EMAIL>",
    role: demoRoles[2],
    department: demoDepartments[3],
    status: "active",
  },
  {
    id: "5",
    name: "David Brown",
    email: "<EMAIL>",
    role: demoRoles[2],
    department: demoDepartments[4],
    status: "inactive",
  },
];

// Demo Tickets
export const demoTickets: Ticket[] = [
  {
    id: "1",
    number: "TKT-001",
    subject: "Cannot access email account",
    status: "new",
    priority: "high",
    requester: demoUsers[3],
    assignee: demoUsers[1],
    department: demoDepartments[1],
    tags: ["email", "access"],
    createdAt: new Date("2024-01-15T09:00:00Z"),
    updatedAt: new Date("2024-01-15T09:00:00Z"),
    slaTarget: new Date("2024-01-17T09:00:00Z"),
    description: "User reports being unable to access their email account since this morning.",
  },
  {
    id: "2",
    number: "TKT-002",
    subject: "Software installation request",
    status: "in_progress",
    priority: "medium",
    requester: demoUsers[4],
    assignee: demoUsers[2],
    department: demoDepartments[2],
    tags: ["software", "installation"],
    createdAt: new Date("2024-01-14T14:30:00Z"),
    updatedAt: new Date("2024-01-15T10:15:00Z"),
    slaTarget: new Date("2024-01-18T14:30:00Z"),
    description: "Request to install Adobe Creative Suite on workstation.",
  },
  {
    id: "3",
    number: "TKT-003",
    subject: "Printer not working",
    status: "waiting",
    priority: "low",
    requester: demoUsers[3],
    assignee: demoUsers[1],
    department: demoDepartments[1],
    tags: ["printer", "hardware"],
    createdAt: new Date("2024-01-13T11:20:00Z"),
    updatedAt: new Date("2024-01-14T16:45:00Z"),
    slaTarget: new Date("2024-01-20T11:20:00Z"),
    description: "Office printer is showing error messages and not printing.",
  },
  {
    id: "4",
    number: "TKT-004",
    subject: "Password reset",
    status: "resolved",
    priority: "medium",
    requester: demoUsers[4],
    assignee: demoUsers[1],
    department: demoDepartments[1],
    tags: ["password", "account"],
    createdAt: new Date("2024-01-12T08:15:00Z"),
    updatedAt: new Date("2024-01-12T09:30:00Z"),
    description: "User forgot their password and needs a reset.",
  },
  {
    id: "5",
    number: "TKT-005",
    subject: "Network connectivity issues",
    status: "closed",
    priority: "urgent",
    requester: demoUsers[3],
    assignee: demoUsers[2],
    department: demoDepartments[2],
    tags: ["network", "connectivity"],
    createdAt: new Date("2024-01-10T13:45:00Z"),
    updatedAt: new Date("2024-01-11T10:20:00Z"),
    slaTarget: new Date("2024-01-10T17:45:00Z"),
    description: "Multiple users reporting intermittent network connectivity issues.",
  },
];

// Demo Comments
export const demoComments: Comment[] = [
  {
    id: "1",
    ticketId: "1",
    author: demoUsers[1],
    type: "internal",
    body: "Checking user's email server configuration.",
    attachments: [],
    createdAt: new Date("2024-01-15T09:15:00Z"),
  },
  {
    id: "2",
    ticketId: "2",
    author: demoUsers[2],
    type: "public",
    body: "I've started the installation process. It will take about 30 minutes to complete.",
    attachments: [],
    createdAt: new Date("2024-01-15T10:15:00Z"),
  },
  {
    id: "3",
    ticketId: "3",
    author: demoUsers[1],
    type: "public",
    body: "We're waiting for a replacement toner cartridge to arrive. Expected delivery is tomorrow.",
    attachments: [],
    createdAt: new Date("2024-01-14T16:45:00Z"),
  },
];

// Demo Canned Responses
export const demoCannedResponses: CannedResponse[] = [
  {
    id: "1",
    title: "Password Reset Instructions",
    content: "Hi {{name}},\n\nI've reset your password. Please use the following temporary password to log in: {{temp_password}}\n\nPlease change your password after logging in.\n\nBest regards,\nIT Support",
    tags: ["password", "reset"],
    category: "Account Support",
  },
  {
    id: "2",
    title: "Ticket Acknowledgment",
    content: "Hi {{name}},\n\nThank you for contacting IT Support. We have received your ticket #{{ticket_number}} and will review it shortly.\n\nExpected response time: {{sla_time}}\n\nBest regards,\nIT Support Team",
    tags: ["acknowledgment"],
    category: "General",
  },
  {
    id: "3",
    title: "Software Installation Complete",
    content: "Hi {{name}},\n\nThe software installation you requested has been completed successfully. You can now find the application in your Start menu.\n\nIf you encounter any issues, please don't hesitate to contact us.\n\nBest regards,\nIT Support",
    tags: ["software", "installation"],
    category: "Software Support",
  },
  {
    id: "4",
    title: "Ticket Resolution",
    content: "Hi {{name}},\n\nWe're pleased to inform you that your ticket #{{ticket_number}} has been resolved.\n\nIf you're satisfied with the resolution, please close this ticket. If you need further assistance, feel free to reply.\n\nBest regards,\nIT Support",
    tags: ["resolution"],
    category: "General",
  },
  {
    id: "5",
    title: "Hardware Replacement",
    content: "Hi {{name}},\n\nWe've ordered a replacement for your {{hardware_item}}. It should arrive within {{delivery_time}} business days.\n\nWe'll schedule the installation once it arrives.\n\nBest regards,\nIT Support",
    tags: ["hardware", "replacement"],
    category: "Hardware Support",
  },
];

// Demo Knowledge Articles
export const demoKnowledgeArticles: KnowledgeArticle[] = [
  {
    id: "1",
    title: "How to Reset Your Password",
    content: "Follow these steps to reset your password:\n1. Go to the login page\n2. Click 'Forgot Password'\n3. Enter your email address\n4. Check your email for reset instructions",
    category: "Account Management",
    tags: ["password", "reset", "account"],
    author: demoUsers[0],
    createdAt: new Date("2024-01-01T00:00:00Z"),
    updatedAt: new Date("2024-01-10T12:00:00Z"),
    helpful: 25,
    notHelpful: 3,
  },
  {
    id: "2",
    title: "Setting Up Email on Mobile Devices",
    content: "Configure your work email on your mobile device:\n1. Open Email app\n2. Add Account > Exchange\n3. Enter your credentials\n4. Configure sync settings",
    category: "Email Support",
    tags: ["email", "mobile", "setup"],
    author: demoUsers[1],
    createdAt: new Date("2024-01-05T00:00:00Z"),
    updatedAt: new Date("2024-01-05T00:00:00Z"),
    helpful: 18,
    notHelpful: 2,
  },
];

// Demo Audit Logs
export const demoAuditLogs: AuditLog[] = [
  {
    id: "1",
    actor: demoUsers[0],
    action: "created_user",
    entity: "user",
    entityId: "5",
    details: { userName: "David Brown", userEmail: "<EMAIL>" },
    createdAt: new Date("2024-01-15T10:30:00Z"),
  },
  {
    id: "2",
    actor: demoUsers[1],
    action: "updated_ticket",
    entity: "ticket",
    entityId: "1",
    details: { field: "status", oldValue: "new", newValue: "in_progress" },
    createdAt: new Date("2024-01-15T09:15:00Z"),
  },
  {
    id: "3",
    actor: demoUsers[2],
    action: "assigned_ticket",
    entity: "ticket",
    entityId: "2",
    details: { assignedTo: "Mike Johnson" },
    createdAt: new Date("2024-01-14T14:35:00Z"),
  },
];

// Chart data
export const chartData = {
  ticketVolume: [
    { name: "Jan", value: 45 },
    { name: "Feb", value: 38 },
    { name: "Mar", value: 52 },
    { name: "Apr", value: 61 },
    { name: "May", value: 47 },
    { name: "Jun", value: 33 },
  ],
  slaCompliance: [
    { name: "On Time", value: 78 },
    { name: "Overdue", value: 22 },
  ],
  agentWorkload: [
    { name: "Jane Smith", value: 12 },
    { name: "Mike Johnson", value: 8 },
    { name: "John Doe", value: 5 },
  ],
};
