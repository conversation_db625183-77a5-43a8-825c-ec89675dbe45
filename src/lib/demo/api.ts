import axiosInstance from "../axios";
import {
  demoTickets,
  demoUsers,
  demoTenants,
  demoRoles,
  demoDepartments,
  demoComments,
  demoCannedResponses,
  demoKnowledgeArticles,
  demoAuditLogs,
  chartData,
} from "./data";
import type {
  Ticket,
  User,
  Tenant,
  Role,
  Department,
  Comment,
  CannedResponse,
  KnowledgeArticle,
  AuditLog,
} from "./types";

// Simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Mock API responses with loading states
export const ticketService = {
  async getAll(): Promise<Ticket[]> {
    await delay(800);
    return [...demoTickets];
  },

  async getById(id: string): Promise<Ticket | null> {
    await delay(500);
    return demoTickets.find((ticket) => ticket.id === id) || null;
  },

  async create(ticket: Partial<Ticket>): Promise<Ticket> {
    await delay(1000);
    const newTicket: Ticket = {
      id: Math.random().toString(36).substr(2, 9),
      number: `TKT-${String(demoTickets.length + 1).padStart(3, "0")}`,
      subject: ticket.subject || "New Ticket",
      status: "new",
      priority: ticket.priority || "medium",
      requester: ticket.requester || demoUsers[0],
      department: ticket.department || demoDepartments[0],
      tags: ticket.tags || [],
      createdAt: new Date(),
      updatedAt: new Date(),
      description: ticket.description,
    };
    demoTickets.push(newTicket);
    return newTicket;
  },

  async update(id: string, updates: Partial<Ticket>): Promise<Ticket | null> {
    await delay(600);
    const index = demoTickets.findIndex((ticket) => ticket.id === id);
    if (index === -1) return null;

    demoTickets[index] = { ...demoTickets[index], ...updates, updatedAt: new Date() };
    return demoTickets[index];
  },

  async delete(id: string): Promise<boolean> {
    await delay(500);
    const index = demoTickets.findIndex((ticket) => ticket.id === id);
    if (index === -1) return false;

    demoTickets.splice(index, 1);
    return true;
  },

  async getComments(ticketId: string): Promise<Comment[]> {
    await delay(400);
    return demoComments.filter((comment) => comment.ticketId === ticketId);
  },

  async addComment(ticketId: string, commentData: { body: string; type: "public" | "internal" }): Promise<Comment> {
    await delay(500);
    const newComment: Comment = {
      id: Math.random().toString(36).substr(2, 9),
      ticketId,
      author: demoUsers[0], // Current user
      type: commentData.type,
      body: commentData.body,
      attachments: [],
      createdAt: new Date(),
    };
    demoComments.push(newComment);
    return newComment;
  },
};

export const userService = {
  async getAll(): Promise<User[]> {
    await delay(600);
    return [...demoUsers];
  },

  async getById(id: string): Promise<User | null> {
    await delay(300);
    return demoUsers.find((user) => user.id === id) || null;
  },

  async create(user: Partial<User>): Promise<User> {
    await delay(800);
    const newUser: User = {
      id: Math.random().toString(36).substr(2, 9),
      name: user.name || "New User",
      email: user.email || "<EMAIL>",
      role: user.role || demoRoles[2],
      department: user.department || demoDepartments[0],
      status: "active",
    };
    demoUsers.push(newUser);
    return newUser;
  },

  async update(id: string, updates: Partial<User>): Promise<User | null> {
    await delay(500);
    const index = demoUsers.findIndex((user) => user.id === id);
    if (index === -1) return null;

    demoUsers[index] = { ...demoUsers[index], ...updates };
    return demoUsers[index];
  },

  async invite(email: string, role: Role, department: Department): Promise<User> {
    await delay(1200);
    return this.create({
      name: email.split("@")[0].replace(".", " "),
      email,
      role,
      department,
    });
  },

  async delete(id: string): Promise<boolean> {
    await delay(500);
    const index = demoUsers.findIndex((user) => user.id === id);
    if (index === -1) return false;

    demoUsers.splice(index, 1);
    return true;
  },
};

export const tenantService = {
  async getAll(): Promise<Tenant[]> {
    await delay(500);
    return [...demoTenants];
  },

  async create(tenant: Partial<Tenant>): Promise<Tenant> {
    await delay(900);
    const newTenant: Tenant = {
      id: Math.random().toString(36).substr(2, 9),
      name: tenant.name || "New Tenant",
      slug: tenant.slug || tenant.name?.toLowerCase().replace(/\s+/g, "-") || "new-tenant",
      status: "active",
      entitlements: {
        tickets: true,
        knowledge: false,
        reports: false,
        ...tenant.entitlements,
      },
    };
    demoTenants.push(newTenant);
    return newTenant;
  },
};

export const roleService = {
  async getAll(): Promise<Role[]> {
    await delay(400);
    return [...demoRoles];
  },

  async create(role: Partial<Role>): Promise<Role> {
    await delay(700);
    const newRole: Role = {
      id: Math.random().toString(36).substr(2, 9),
      name: role.name || "New Role",
      description: role.description,
      type: role.type || "custom",
      userType: role.userType || "agent",
      permissions: role.permissions || [],
      isActive: role.isActive !== undefined ? role.isActive : true,
      userCount: 0,
    };
    demoRoles.push(newRole);
    return newRole;
  },
};

export const departmentService = {
  async getAll(): Promise<Department[]> {
    await delay(300);
    return [...demoDepartments];
  },

  async getById(id: string): Promise<Department | null> {
    await delay(300);
    return demoDepartments.find((dept) => dept.id === id) || null;
  },

  async create(department: Partial<Department>): Promise<Department> {
    await delay(600);
    const newDepartment: Department = {
      id: Math.random().toString(36).substr(2, 9),
      name: department.name || "New Department",
      description: department.description,
      status: department.status || "active",
      parentId: department.parentId,
      memberCount: 0,
    };
    demoDepartments.push(newDepartment);
    return newDepartment;
  },

  async update(id: string, updates: Partial<Department>): Promise<Department | null> {
    await delay(500);
    const index = demoDepartments.findIndex((dept) => dept.id === id);
    if (index === -1) return null;

    demoDepartments[index] = { ...demoDepartments[index], ...updates };
    return demoDepartments[index];
  },

  async delete(id: string): Promise<boolean> {
    await delay(400);
    const index = demoDepartments.findIndex((dept) => dept.id === id);
    if (index === -1) return false;

    demoDepartments.splice(index, 1);
    return true;
  },
};

export const cannedResponseService = {
  async getAll(): Promise<CannedResponse[]> {
    await delay(500);
    return [...demoCannedResponses];
  },

  async create(response: Partial<CannedResponse>): Promise<CannedResponse> {
    await delay(800);
    const newResponse: CannedResponse = {
      id: Math.random().toString(36).substr(2, 9),
      title: response.title || "New Response",
      content: response.content || "",
      tags: response.tags || [],
      category: response.category,
    };
    demoCannedResponses.push(newResponse);
    return newResponse;
  },
};

export const knowledgeService = {
  async getAll(): Promise<KnowledgeArticle[]> {
    await delay(600);
    return [...demoKnowledgeArticles];
  },

  async getById(id: string): Promise<KnowledgeArticle | null> {
    await delay(400);
    return demoKnowledgeArticles.find((article) => article.id === id) || null;
  },

  async search(query: string): Promise<KnowledgeArticle[]> {
    await delay(700);
    return demoKnowledgeArticles.filter(
      (article) =>
        article.title.toLowerCase().includes(query.toLowerCase()) ||
        article.content.toLowerCase().includes(query.toLowerCase()) ||
        article.tags.some((tag) => tag.toLowerCase().includes(query.toLowerCase()))
    );
  },
};

export const auditService = {
  async getAll(): Promise<AuditLog[]> {
    await delay(800);
    return [...demoAuditLogs];
  },
};

export const dashboardService = {
  async getStats() {
    await delay(900);
    return {
      totalTickets: demoTickets.length,
      openTickets: demoTickets.filter((t) => !["resolved", "closed"].includes(t.status)).length,
      resolvedToday: demoTickets.filter(
        (t) => t.status === "resolved" && t.updatedAt.toDateString() === new Date().toDateString()
      ).length,
      avgResolutionTime: "4.2 hours",
    };
  },

  async getChartData() {
    await delay(1000);
    return chartData;
  },
};

export const auditLogService = {
  async getAll(): Promise<AuditLog[]> {
    await delay(600);
    return [...demoAuditLogs];
  },
};

export const authService = {
  async login(tenant: string, email: string, password: string): Promise<{ user: User; token: string }> {
    await delay(1500);
    
    // Mock validation
    if (password === "password123") {
      const user = demoUsers.find((u) => u.email === email) || demoUsers[0];
      return {
        user,
        token: "mock-jwt-token",
      };
    }
    
    throw new Error("Invalid credentials");
  },

  async validateTenant(slug: string): Promise<Tenant | null> {
    const response = await axiosInstance.get(`/v1/tenant/infoByName/${slug}`);
    return response.data as Tenant || null;
  },
};
