import { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/store";
import { loadStoredAuth, logoutUser } from "@/redux/slices/auth";

export const useAuth = () => {
  const dispatch = useAppDispatch();
  const { 
    user, 
    tenant, 
    tokens, 
    loading, 
    error, 
    isAuthenticated 
  } = useAppSelector((state) => state.auth);

  // Load stored auth data on mount
  useEffect(() => {
    dispatch(loadStoredAuth());
  }, [dispatch]);

  const logout = () => {
    dispatch(logoutUser());
  };

  return {
    user,
    tenant,
    tokens,
    loading,
    error,
    isAuthenticated,
    logout,
  };
};
