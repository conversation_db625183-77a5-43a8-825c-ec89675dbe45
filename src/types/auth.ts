export interface User {
  _id: string;
  email: string;
  name: string;
  displayName: string;
  role: string;
  tenantId: string;
  status: "active" | "inactive";
  userType?: string;
  permissions?: string[];
}

export interface Tenant {
  tenantId: string;
  name: string;
  slug: string;
  companyName: string;
  tenantStatus: "trial" | "active" | "suspended" | "inactive";
  isCompanyOwnerActivated: boolean;
}

export interface LoginRequest {
  email: string;
  password: string;
  tenantId: string;
  mfaCode?: string;
}

export interface LoginResponse {
  success: boolean;
  data: {
    user: User;
    tenant?: Tenant;
    tokens?: {
      accessToken: string;
      refreshToken: string;
    };
    mfaRequired?: boolean;
    activationRequired?: boolean;
  };
  message?: string;
}

export interface TenantValidationResponse {
  success: boolean;
  data: Tenant;
}

export interface ActivateAccountRequest {
  activationCode: string;
  password: string;
}

export interface VerifyTenantRequest {
  tenantSlug: string;
  verificationCode: string;
}

export interface VerifyTenantResponse {
  success: boolean;
  data: Tenant;
  message: string;
}

export interface ActivateAccountResponse {
  success: boolean;
  data: {
    user: User;
    tokens: {
      accessToken: string;
      refreshToken: string;
    };
  };
  message: string;
}

export interface AuthState {
  user: User | null;
  tenant: Tenant | null;
  tokens: {
    accessToken: string | null;
    refreshToken: string | null;
  };
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  mfaRequired?: boolean;
  activationRequired?: boolean;
}
