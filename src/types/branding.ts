// Branding types
export interface BrandingItem {
  _id: string;
  tenantId: string;
  customColors: boolean;
  colors: string;
  tagline: string;
  description: string;
  url: string;
  featureHighlights: string[];
  isActive: boolean;
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  __v: number;
  id: string;
}

export interface BrandingListResponse {
  success: boolean;
  data: {
    branding: BrandingItem[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
  message: string;
}

export interface CreateUpdateBrandingRequest {
  tenantId: string;
  customColors: boolean;
  colors: string;
  tagline: string;
  description: string;
  featureHighlights: string[];
  image?: File;
}

export interface BrandingApiResponse {
  success: boolean;
  data?: {
    branding: BrandingItem;
  };
  message: string;
}

export interface BrandingFilters {
  tenantId: string;
  page?: number;
  limit?: number;
}
