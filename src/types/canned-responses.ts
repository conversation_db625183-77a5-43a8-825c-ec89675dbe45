export interface CannedResponse {
  id: string;
  title: string;
  content: string;
  tags: string[];
  category?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateCannedResponseRequest {
  title: string;
  content: string;
  tags: string[];
  category?: string;
  isActive?: boolean;
}

export interface UpdateCannedResponseRequest {
  title?: string;
  content?: string;
  tags?: string[];
  category?: string;
  isActive?: boolean;
}

export interface CannedResponsesResponse {
  data: CannedResponse[];
  total: number;
}