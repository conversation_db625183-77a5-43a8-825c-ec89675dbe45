import { User } from './auth';

export interface KnowledgeArticle {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  author: Pick<User, '_id' | 'name' | 'email'>;
  status: 'draft' | 'published' | 'archived';
  helpful: number;
  notHelpful: number;
  views: number;
  isPinned: boolean;
  isInternal: boolean;
  userVote?: boolean | null; // true = helpful, false = not helpful, null = no vote
  createdAt: string;
  updatedAt: string;
}

export interface KnowledgeCategory {
  category: string;
  count: number;
}
