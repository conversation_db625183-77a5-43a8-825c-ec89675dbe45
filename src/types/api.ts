export interface ApiError {
  success: false;
  message: string;
  error?: string;
}

export interface ApiResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

export type ApiResult<T> = ApiResponse<T> | ApiError;

// Comment-related types
export interface CommentAuthor {
  _id: string;
  name: string;
  role: string;
  email?: string;
  avatar?: string;
}

export interface Comment {
  _id: string;
  tenantId: string;
  ticketOrSubticketId: string;
  entityType: number;
  authorId: string | CommentAuthor; // Can be string or populated object
  author?: CommentAuthor; // Populated author object
  body: string;
  visibility: 'public' | 'internal';
  parentCommentId?: string;
  replies?: Comment[];
  attachments: string[];
  isEdited: boolean;
  isSoftDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  editedAt?: string;
  ticketId?: string; // For backward compatibility
  __v?: number;
}

export interface CreateCommentRequest {
  body: string;
  visibility: 'public' | 'internal';
  parentCommentId?: string;
  entityType?: string;
  ticketOrSubticketId?: string;
}

export interface UpdateCommentRequest {
  body: string;
}

export interface CommentsQueryParams {
  visibility?: 'public' | 'internal';
  authorId?: string;
  includeReplies?: boolean;
  page?: number;
  size?: number;
  createdAfter?: string;
  createdBefore?: string;
  entityType?: string;
}

export interface PaginationMeta {
  page: number;
  size: number;
  total: number;
  pages: number;
}

export interface CommentsResponse extends ApiResponse<Comment[]> {
  pagination: PaginationMeta;
}

export interface CommentResponse extends ApiResponse<Comment> {}

export interface CreateCommentResponse extends ApiResponse<Comment> {}

export interface UpdateCommentResponse extends ApiResponse<Comment> {}

export interface DeleteCommentResponse extends ApiResponse<{ message: string }> {}
