export interface Worklog {
  _id: string;
  tenantId: string;
  ticketId?: string;
  subTaskId?: string;
  agentId: string;
  minutes: number;
  note?: string;
  billable: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface WorklogSummary {
  totalMinutes: number;
  billableMinutes: number;
  totalHours: number;
  billableHours: number;
  agentBreakdown?: Array<{
    _id: string;
    totalMinutes: number;
    billableMinutes: number;
    entryCount: number;
  }>;
}